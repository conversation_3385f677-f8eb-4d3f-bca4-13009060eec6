import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors } from '../constants/colors';

/**
 * Bakiye özeti bileşeni
 *
 * @param {Object} props - Bileşen ö<PERSON>leri
 * @param {number} props.income - Toplam gelir
 * @param {number} props.expense - Toplam gider
 * @param {number} props.balance - Bakiye
 * @returns {JSX.Element} Bakiye özeti bileşeni
 */
export default function BalanceSummary({ income, expense, balance }) {
  return (
    <View style={styles.container}>
      <View style={styles.balanceContainer}>
        <Text style={styles.balanceLabel}>Toplam Bakiye</Text>
        <Text style={styles.balanceAmount}>₺{balance.toLocaleString('tr-TR')}</Text>
      </View>

      <View style={styles.detailsContainer}>
        <View style={styles.detailItem}>
          <View style={styles.iconContainer}>
            <MaterialIcons name="arrow-upward" size={20} color="#fff" />
          </View>
          <View>
            <Text style={styles.detailLabel}>Gelir</Text>
            <Text style={styles.incomeAmount}>₺{income.toLocaleString('tr-TR')}</Text>
          </View>
        </View>

        <View style={styles.detailItem}>
          <View style={[styles.iconContainer, styles.expenseIconContainer]}>
            <MaterialIcons name="arrow-downward" size={20} color="#fff" />
          </View>
          <View>
            <Text style={styles.detailLabel}>Gider</Text>
            <Text style={styles.expenseAmount}>₺{expense.toLocaleString('tr-TR')}</Text>
          </View>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  balanceContainer: {
    marginBottom: 16,
  },
  balanceLabel: {
    fontSize: 14,
    color: '#333333',
    marginBottom: 4,
  },
  balanceAmount: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333333',
  },
  detailsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: Colors.SUCCESS,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  expenseIconContainer: {
    backgroundColor: Colors.DANGER,
  },
  detailLabel: {
    fontSize: 12,
    color: '#333333',
  },
  incomeAmount: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.SUCCESS,
  },
  expenseAmount: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.DANGER,
  },
});
