import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors } from '../constants/colors';

/**
 * Filtre çubuğu bileşeni
 * 
 * @param {Object} props - Bileşen özellikleri
 * @param {Object} props.filter - Filtre durumu
 * @param {Function} props.onFilterChange - Filtre değişikliği işlevi
 * @returns {JSX.Element} Filtre çubuğu bileşeni
 */
export default function FilterBar({ filter, onFilterChange }) {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={styles.header}
        onPress={() => setIsExpanded(!isExpanded)}
      >
        <View style={styles.headerContent}>
          <MaterialIcons name="filter-list" size={24} color={Colors.PRIMARY} />
          <Text style={styles.headerText}>Filtreler</Text>
        </View>
        <MaterialIcons
          name={isExpanded ? 'expand-less' : 'expand-more'}
          size={24}
          color={Colors.PRIMARY}
        />
      </TouchableOpacity>
      
      {isExpanded && (
        <View style={styles.content}>
          <Text style={styles.sectionTitle}>İşlem Türü</Text>
          <View style={styles.buttonGroup}>
            <TouchableOpacity
              style={[
                styles.filterButton,
                filter.type === 'all' && styles.activeFilterButton
              ]}
              onPress={() => onFilterChange({ ...filter, type: 'all' })}
            >
              <Text style={[
                styles.filterButtonText,
                filter.type === 'all' && styles.activeFilterButtonText
              ]}>Tümü</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[
                styles.filterButton,
                filter.type === 'income' && styles.activeFilterButton
              ]}
              onPress={() => onFilterChange({ ...filter, type: 'income' })}
            >
              <Text style={[
                styles.filterButtonText,
                filter.type === 'income' && styles.activeFilterButtonText
              ]}>Gelir</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[
                styles.filterButton,
                filter.type === 'expense' && styles.activeFilterButton
              ]}
              onPress={() => onFilterChange({ ...filter, type: 'expense' })}
            >
              <Text style={[
                styles.filterButtonText,
                filter.type === 'expense' && styles.activeFilterButtonText
              ]}>Gider</Text>
            </TouchableOpacity>
          </View>
          
          <Text style={styles.sectionTitle}>Dönem</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <TouchableOpacity
              style={[
                styles.filterButton,
                filter.period === 'all' && styles.activeFilterButton
              ]}
              onPress={() => onFilterChange({ ...filter, period: 'all' })}
            >
              <Text style={[
                styles.filterButtonText,
                filter.period === 'all' && styles.activeFilterButtonText
              ]}>Tümü</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[
                styles.filterButton,
                filter.period === 'today' && styles.activeFilterButton
              ]}
              onPress={() => onFilterChange({ ...filter, period: 'today' })}
            >
              <Text style={[
                styles.filterButtonText,
                filter.period === 'today' && styles.activeFilterButtonText
              ]}>Bugün</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[
                styles.filterButton,
                filter.period === 'week' && styles.activeFilterButton
              ]}
              onPress={() => onFilterChange({ ...filter, period: 'week' })}
            >
              <Text style={[
                styles.filterButtonText,
                filter.period === 'week' && styles.activeFilterButtonText
              ]}>Son 7 Gün</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[
                styles.filterButton,
                filter.period === 'month' && styles.activeFilterButton
              ]}
              onPress={() => onFilterChange({ ...filter, period: 'month' })}
            >
              <Text style={[
                styles.filterButtonText,
                filter.period === 'month' && styles.activeFilterButtonText
              ]}>Bu Ay</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[
                styles.filterButton,
                filter.period === 'year' && styles.activeFilterButton
              ]}
              onPress={() => onFilterChange({ ...filter, period: 'year' })}
            >
              <Text style={[
                styles.filterButtonText,
                filter.period === 'year' && styles.activeFilterButtonText
              ]}>Bu Yıl</Text>
            </TouchableOpacity>
          </ScrollView>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    borderRadius: 12,
    marginHorizontal: 16,
    marginTop: 16,
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
    overflow: 'hidden',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333333',
    marginLeft: 8,
  },
  content: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color:'#333333',
    marginBottom: 8,
  },
  buttonGroup: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#f5f5f5',
    marginRight: 8,
    marginBottom: 8,
  },
  activeFilterButton: {
    backgroundColor: Colors.PRIMARY,
  },
  filterButtonText: {
    fontSize: 14,
    color: '#333333',
  },
  activeFilterButtonText: {
    color: '#fff',
  },
});
