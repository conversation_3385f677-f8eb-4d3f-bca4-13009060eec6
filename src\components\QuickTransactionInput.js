import React, { useState, useEffect } from 'react';
import { View, TextInput, StyleSheet, TouchableOpacity, Text, Alert, Modal, FlatList } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useSQLiteContext } from 'expo-sqlite';
import { Colors } from '../constants/colors';
import { useExchangeRate } from '../context/ExchangeRateProvider';
import CategorySelectorSimple from './transaction/CategorySelectorSimple';
import CategoryForm from './category/CategoryForm';

/**
 * Hızlı işlem ekleme bileşeni
 *
 * @param {Object} props - Bileşen özellikleri
 * @param {Function} props.onTransactionAdded - İşlem eklendiğinde çağrılacak fonksiyon
 * @returns {JSX.Element} Hızlı işlem ekleme bileşeni
 */
export default function QuickTransactionInput({ onTransactionAdded }) {
  const db = useSQLiteContext();
  // Döviz kuru context'ini kullanmaya çalış, hata durumunda null kullan
  let exchangeRateContext;
  try {
    exchangeRateContext = useExchangeRate();
  } catch (error) {
    console.warn('ExchangeRate context not available:', error);
    exchangeRateContext = null;
  }

  const [amount, setAmount] = useState('');
  const [description, setDescription] = useState('');
  const [transactionType, setTransactionType] = useState('expense'); // Varsayılan olarak gider
  const [showCurrencyModal, setShowCurrencyModal] = useState(false);
  const [showCategoryModal, setShowCategoryModal] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showCategoryForm, setShowCategoryForm] = useState(false);
  const [selectedCurrency, setSelectedCurrency] = useState('TRY');
  const [preferredCurrency, setPreferredCurrency] = useState('USD'); // Varsayılan tercih edilen para birimi
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [categories, setCategories] = useState([]);
  const [transactionDate, setTransactionDate] = useState(new Date());
  const [categoryFormType, setCategoryFormType] = useState('expense');

  // Bileşen yüklendiğinde tercih edilen para birimini ayarla
  useEffect(() => {
    const loadPreferences = async () => {
      try {
        // Varsayılan para birimini ayarla
        if (exchangeRateContext) {
          // Eğer context'te preferredCurrency varsa onu kullan
          if (exchangeRateContext.preferredCurrency) {
            setPreferredCurrency(exchangeRateContext.preferredCurrency);
          }

          // Eğer context'te baseCurrency varsa, seçili para birimi olarak onu kullan
          if (exchangeRateContext.baseCurrency) {
            setSelectedCurrency(exchangeRateContext.baseCurrency);
          }
        }
      } catch (error) {
        console.error('Para birimi tercihleri yüklenirken hata:', error);
      }
    };

    loadPreferences();
  }, [exchangeRateContext]);

  // Kategorileri yükleme fonksiyonu
  const loadCategories = async () => {
    try {
      // İşlem tipine göre kategorileri getir
      const categoryType = transactionType === 'income' ? 'income' : 'expense';

      const result = await db.getAllAsync(`
        SELECT * FROM categories
        WHERE type = ? OR type = 'both'
        ORDER BY name ASC
      `, [categoryType]);

      setCategories(result);

      // Varsayılan kategoriyi seç
      const defaultCategory = result.find(cat => cat.is_default === 1);
      if (defaultCategory) {
        setSelectedCategory(defaultCategory.id);
      } else if (result.length > 0) {
        setSelectedCategory(result[0].id);
      }
    } catch (error) {
      console.error('Kategorileri yükleme hatası:', error);
    }
  };

  // Kategorileri yükle
  useEffect(() => {
    loadCategories();
  }, [transactionType, db]);

  /**
   * İşlemi ekler
   *
   * @returns {Promise<void>}
   */
  const addTransaction = async () => {
    try {
      // Boş değer kontrolü
      if (!amount || amount === '0') {
        Alert.alert('Hata', 'Lütfen geçerli bir tutar girin.');
        return;
      }

      // Tutarı sayıya çevir
      const numericAmount = parseFloat(amount.replace(/[^0-9.-]/g, ''));

      if (isNaN(numericAmount) || numericAmount === 0) {
        Alert.alert('Hata', 'Lütfen geçerli bir tutar girin.');
        return;
      }

      // Tutarın mutlak değerini al
      const absoluteAmount = Math.abs(numericAmount);

      // Kategori ID'si selectedCategory değişkeninde

      // İşlem verilerini hazırla
      const transactionData = {
        type: transactionType,
        amount: absoluteAmount,
        description: description || (transactionType === 'income' ? 'Gelir' : 'Gider'),
        date: transactionDate.toISOString().split('T')[0],
        category_id: selectedCategory,
        currency: selectedCurrency,
        preferred_currency: preferredCurrency
      };

      // Döviz kuru bilgilerini al ve dönüşüm yap
      if (exchangeRateContext && selectedCurrency !== preferredCurrency) {
        try {
          // Döviz kurunu al
          let exchangeRate;
          let convertedAmount;

          if (exchangeRateContext.rates && exchangeRateContext.rates[preferredCurrency]) {
            exchangeRate = exchangeRateContext.rates[preferredCurrency];
          }

          // Dönüşüm yap
          if (exchangeRate) {
            // exchangeRateContext'in yapısına göre uygun metodu kullan
            if (typeof exchangeRateContext.convertCurrency === 'function') {
              convertedAmount = exchangeRateContext.convertCurrency(
                absoluteAmount,
                selectedCurrency,
                preferredCurrency
              );
            } else if (typeof exchangeRateContext.convert === 'function') {
              convertedAmount = exchangeRateContext.convert(
                absoluteAmount,
                selectedCurrency,
                preferredCurrency
              );
            }

            if (convertedAmount) {
              transactionData.exchange_rate = exchangeRate;
              transactionData.converted_amount = convertedAmount;
            }
          }
        } catch (error) {
          console.error('Döviz kuru dönüşüm hatası:', error);
        }
      }

      // Metadata bilgilerini ekle
      const metadata = {
        exchange_rate_date: exchangeRateContext?.lastUpdated || new Date().toISOString(),
        original_currency: selectedCurrency,
        preferred_currency: preferredCurrency,
        exchange_rate: transactionData.exchange_rate
      };

      transactionData.metadata = JSON.stringify(metadata);

      // İşlemi ekle
      await db.runAsync(`
        INSERT INTO transactions (
          type, amount, description, date, category_id,
          currency, preferred_currency, metadata,
          exchange_rate, converted_amount
        )
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        transactionData.type,
        transactionData.amount,
        transactionData.description,
        transactionData.date,
        transactionData.category_id,
        transactionData.currency,
        transactionData.preferred_currency,
        transactionData.metadata,
        transactionData.exchange_rate || null,
        transactionData.converted_amount || null
      ]);

      // Alanları temizle
      setAmount('');
      setDescription('');

      // Başarı mesajı göster
      Alert.alert(
        'Başarılı',
        `${transactionType === 'income' ? 'Gelir' : 'Gider'} başarıyla eklendi.`,
        [{ text: 'Tamam', onPress: onTransactionAdded }]
      );
    } catch (error) {
      console.error('İşlem ekleme hatası:', error);
      Alert.alert('Hata', 'İşlem eklenirken bir hata oluştu.');
    }
  };

  // İşlem tipini değiştir
  const toggleTransactionType = () => {
    setTransactionType(prevType => prevType === 'income' ? 'expense' : 'income');
  };

  // Para birimi seçimi
  const toggleCurrencyModal = () => {
    setShowCurrencyModal(!showCurrencyModal);
  };

  // Para birimi seçme
  const selectCurrency = (currency) => {
    setSelectedCurrency(currency);
    setShowCurrencyModal(false);
  };

  // Tercih edilen para birimi seçme
  const selectPreferredCurrency = (currency) => {
    setPreferredCurrency(currency);
    setShowCurrencyModal(false);
  };

  // Kategori seçimi modalını aç/kapat
  const toggleCategoryModal = () => {
    setShowCategoryModal(!showCategoryModal);
  };

  // Kategori seç
  const selectCategory = (categoryId) => {
    setSelectedCategory(categoryId);
    setShowCategoryModal(false);
  };

  // Yeni kategori ekleme modalını aç
  const handleAddCategory = (type) => {
    setCategoryFormType(type || transactionType);
    setShowCategoryForm(true);
  };

  // Kategori kaydetme
  const handleSaveCategory = (newCategory) => {
    // Kategorileri yeniden yükle
    loadCategories();

    // Yeni kategoriyi seç
    if (newCategory && newCategory.id) {
      setSelectedCategory(newCategory.id);
    }
  };

  // Tarih seçimi modalını aç/kapat
  const toggleDatePicker = () => {
    setShowDatePicker(!showDatePicker);
  };

  // Tarih seç
  const onDateChange = (event, selectedDate) => {
    const currentDate = selectedDate || transactionDate;
    setShowDatePicker(false);
    setTransactionDate(currentDate);
  };

  // Tarih formatla
  const formatDate = (date) => {
    return date.toLocaleDateString('tr-TR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Para birimi sembolü
  const getCurrencySymbol = (currencyCode) => {
    const symbols = {
      TRY: '₺',
      USD: '$',
      EUR: '€',
      GBP: '£',
      JPY: '¥'
    };

    return symbols[currencyCode] || currencyCode;
  };

  // Anlık döviz kuru dönüşümü
  const getConvertedAmount = () => {
    if (!amount || amount === '0' || !exchangeRateContext || selectedCurrency === preferredCurrency) {
      return null;
    }

    try {
      // Tutarı sayıya çevir
      const numericAmount = parseFloat(amount.replace(/[^0-9.-]/g, ''));

      if (isNaN(numericAmount) || numericAmount === 0) {
        return null;
      }

      // Basit döviz kuru hesaplama - rates'den direkt al
      if (exchangeRateContext.rates && exchangeRateContext.rates[preferredCurrency]) {
        const rate = exchangeRateContext.rates[preferredCurrency];

        if (selectedCurrency === 'TRY' && preferredCurrency !== 'TRY') {
          // TRY'den başka para birimine
          const convertedAmount = Math.abs(numericAmount) * rate;
          return exchangeRateContext.formatCurrency ?
            exchangeRateContext.formatCurrency(convertedAmount, preferredCurrency) :
            `${convertedAmount.toFixed(2)} ${preferredCurrency}`;
        } else if (selectedCurrency !== 'TRY' && preferredCurrency === 'TRY') {
          // Başka para biriminden TRY'ye
          const convertedAmount = Math.abs(numericAmount) / rate;
          return exchangeRateContext.formatCurrency ?
            exchangeRateContext.formatCurrency(convertedAmount, preferredCurrency) :
            `${convertedAmount.toFixed(2)} ${preferredCurrency}`;
        } else if (selectedCurrency !== 'TRY' && preferredCurrency !== 'TRY') {
          // İki farklı para birimi arasında (TRY üzerinden)
          const tryRates = exchangeRateContext.rates;
          if (tryRates[selectedCurrency] && tryRates[preferredCurrency]) {
            const amountInTRY = Math.abs(numericAmount) / tryRates[selectedCurrency];
            const convertedAmount = amountInTRY * tryRates[preferredCurrency];
            return exchangeRateContext.formatCurrency ?
              exchangeRateContext.formatCurrency(convertedAmount, preferredCurrency) :
              `${convertedAmount.toFixed(2)} ${preferredCurrency}`;
          }
        }
      }

      return null;
    } catch (error) {
      console.error('Anlık dönüşüm hatası:', error);
      return null;
    }
  };

  // Döviz kuru bilgisi
  const getExchangeRateInfo = () => {
    if (!exchangeRateContext || selectedCurrency === preferredCurrency) {
      return null;
    }

    try {
      let rate;

      // exchangeRateContext'in yapısına göre uygun şekilde kur bilgisini al
      if (exchangeRateContext.rates && exchangeRateContext.rates[preferredCurrency]) {
        rate = exchangeRateContext.rates[preferredCurrency];
      } else {
        return null;
      }

      if (rate) {
        return `1 ${selectedCurrency} = ${typeof rate === 'number' ? rate.toFixed(4) : rate} ${preferredCurrency}`;
      }
      return null;
    } catch (error) {
      console.error('Döviz kuru bilgisi hatası:', error);
      return null;
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Hızlı İşlem</Text>
        <TouchableOpacity
          style={[
            styles.typeToggle,
            { backgroundColor: transactionType === 'income' ? Colors.income.main : Colors.expense.main }
          ]}
          onPress={toggleTransactionType}
        >
          <MaterialIcons
            name={transactionType === 'income' ? 'arrow-upward' : 'arrow-downward'}
            size={16}
            color="#fff"
          />
          <Text style={styles.typeText}>
            {transactionType === 'income' ? 'Gelir' : 'Gider'}
          </Text>
        </TouchableOpacity>
      </View>

      <View style={styles.inputContainer}>
        <View style={styles.amountContainer}>
          <TouchableOpacity
            style={styles.currencyButton}
            onPress={toggleCurrencyModal}
          >
            <Text style={styles.currencySymbol}>{getCurrencySymbol(selectedCurrency)}</Text>
            <MaterialIcons name="arrow-drop-down" size={16} color={Colors.GRAY_600} />
          </TouchableOpacity>

          <TextInput
            style={[
              styles.amountInput,
              { color: transactionType === 'income' ? Colors.income.main : Colors.expense.main }
            ]}
            value={amount}
            onChangeText={setAmount}
            placeholder="0.00"
            keyboardType="numeric"
            placeholderTextColor={Colors.GRAY_500}
          />

          <TouchableOpacity
            style={styles.preferredCurrencyButton}
            onPress={toggleCurrencyModal}
          >
            <Text style={styles.preferredCurrencyText}>{preferredCurrency}</Text>
          </TouchableOpacity>
        </View>

        {/* Anlık döviz kuru dönüşümü */}
        {selectedCurrency !== preferredCurrency && amount && amount !== '0' && (
          <View style={styles.conversionContainer}>
            <Text style={styles.conversionText}>
              {getConvertedAmount() || `${getCurrencySymbol(preferredCurrency)}0.00`}
            </Text>
            <Text style={styles.exchangeRateText}>
              {getExchangeRateInfo()}
            </Text>
          </View>
        )}

        <View style={styles.categoryContainer}>
          <MaterialIcons name="category" size={20} color={Colors.GRAY_500} />
          <TouchableOpacity
            style={styles.categorySelector}
            onPress={() => setShowCategoryModal(true)}
          >
            <Text style={styles.categorySelectorText}>
              {categories.find(c => c.id === selectedCategory)?.name || 'Kategori seçin'}
            </Text>
            <MaterialIcons name="arrow-drop-down" size={20} color={Colors.GRAY_500} />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.addCategoryButton}
            onPress={() => handleAddCategory(transactionType)}
          >
            <MaterialIcons name="add" size={20} color={Colors.PRIMARY} />
          </TouchableOpacity>
        </View>

        <View style={styles.descriptionContainer}>
          <MaterialIcons name="description" size={20} color={Colors.GRAY_500} />
          <TextInput
            style={styles.descriptionInput}
            value={description}
            onChangeText={setDescription}
            placeholder="Açıklama (opsiyonel)"
            placeholderTextColor={Colors.GRAY_500}
          />
        </View>
      </View>

      <TouchableOpacity
        style={[
          styles.addButton,
          { backgroundColor: transactionType === 'income' ? Colors.income.main : Colors.expense.main }
        ]}
        onPress={addTransaction}
      >
        <MaterialIcons name="add" size={24} color="#fff" />
      </TouchableOpacity>

      {/* Para Birimi Seçme Modalı */}
      <Modal
        visible={showCurrencyModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowCurrencyModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Para Birimi Seçin</Text>
              <TouchableOpacity onPress={() => setShowCurrencyModal(false)}>
                <MaterialIcons name="close" size={24} color={Colors.GRAY_700} />
              </TouchableOpacity>
            </View>

            <View style={styles.modalSection}>
              <Text style={styles.modalSectionTitle}>İşlem Para Birimi</Text>
              <FlatList
                data={[
                  { code: 'TRY', name: 'Türk Lirası', symbol: '₺' },
                  { code: 'USD', name: 'Amerikan Doları', symbol: '$' },
                  { code: 'EUR', name: 'Euro', symbol: '€' },
                  { code: 'GBP', name: 'İngiliz Sterlini', symbol: '£' }
                ]}
                horizontal
                showsHorizontalScrollIndicator={false}
                keyExtractor={(item) => item.code}
                renderItem={({ item }) => (
                  <TouchableOpacity
                    style={[
                      styles.currencyItem,
                      selectedCurrency === item.code && styles.selectedCurrencyItem
                    ]}
                    onPress={() => selectCurrency(item.code)}
                  >
                    <View style={styles.currencyItemContent}>
                      <Text style={styles.currencySymbolText}>{item.symbol}</Text>
                      <Text style={[
                        styles.currencyItemText,
                        selectedCurrency === item.code && styles.selectedCurrencyItemText
                      ]}>
                        {item.code} - {item.name}
                      </Text>
                    </View>
                    {selectedCurrency === item.code && (
                      <MaterialIcons name="check" size={16} color="#fff" />
                    )}
                  </TouchableOpacity>
                )}
              />
            </View>

            <View style={styles.modalSection}>
              <Text style={styles.modalSectionTitle}>Tercih Edilen Para Birimi</Text>
              <FlatList
                data={[
                  { code: 'TRY', name: 'Türk Lirası', symbol: '₺' },
                  { code: 'USD', name: 'Amerikan Doları', symbol: '$' },
                  { code: 'EUR', name: 'Euro', symbol: '€' },
                  { code: 'GBP', name: 'İngiliz Sterlini', symbol: '£' }
                ]}
                horizontal
                showsHorizontalScrollIndicator={false}
                keyExtractor={(item) => item.code}
                renderItem={({ item }) => (
                  <TouchableOpacity
                    style={[
                      styles.currencyItem,
                      preferredCurrency === item.code && styles.selectedCurrencyItem
                    ]}
                    onPress={() => selectPreferredCurrency(item.code)}
                  >
                    <View style={styles.currencyItemContent}>
                      <Text style={styles.currencySymbolText}>{item.symbol}</Text>
                      <Text style={[
                        styles.currencyItemText,
                        preferredCurrency === item.code && styles.selectedCurrencyItemText
                      ]}>
                        {item.code} - {item.name}
                      </Text>
                    </View>
                    {preferredCurrency === item.code && (
                      <MaterialIcons name="check" size={16} color="#fff" />
                    )}
                  </TouchableOpacity>
                )}
              />
            </View>

            <TouchableOpacity
              style={styles.modalButton}
              onPress={() => setShowCurrencyModal(false)}
            >
              <Text style={styles.modalButtonText}>Tamam</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Kategori Seçim Modalı */}
      <Modal
        visible={showCategoryModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowCategoryModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Kategori Seçin</Text>
              <TouchableOpacity onPress={() => setShowCategoryModal(false)}>
                <MaterialIcons name="close" size={24} color={Colors.GRAY_700} />
              </TouchableOpacity>
            </View>

            <View style={styles.modalSection}>
              <CategorySelectorSimple
                categories={categories}
                selectedCategoryId={selectedCategory}
                onSelectCategory={(categoryId) => {
                  setSelectedCategory(categoryId);
                  setShowCategoryModal(false);
                }}
                onAddCategory={handleAddCategory}
                type={transactionType}
              />
            </View>

            <TouchableOpacity
              style={styles.modalButton}
              onPress={() => setShowCategoryModal(false)}
            >
              <Text style={styles.modalButtonText}>Tamam</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Kategori Ekleme/Düzenleme Modalı */}
      <CategoryForm
        visible={showCategoryForm}
        onClose={() => setShowCategoryForm(false)}
        category={null}
        type={categoryFormType}
        onSave={handleSaveCategory}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.WHITE,
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    shadowColor: Colors.BLACK,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.GRAY_900,
  },
  typeToggle: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 12,
  },
  typeText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 4,
  },
  inputContainer: {
    marginBottom: 12,
  },
  amountContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: Colors.GRAY_300,
    paddingBottom: 8,
    marginBottom: 12,
  },
  currencyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    backgroundColor: Colors.GRAY_100,
    borderRadius: 8,
    marginRight: 8,
  },
  currencySymbol: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.GRAY_700,
    marginRight: 2,
  },
  amountInput: {
    flex: 1,
    fontSize: 20,
    fontWeight: 'bold',
  },
  preferredCurrencyButton: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    backgroundColor: Colors.GRAY_100,
    borderRadius: 8,
  },
  preferredCurrencyText: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.GRAY_700,
  },
  descriptionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: Colors.GRAY_300,
    paddingBottom: 8,
  },
  descriptionInput: {
    flex: 1,
    fontSize: 16,
    color: Colors.GRAY_900,
    marginLeft: 8,
  },
  addButton: {
    alignSelf: 'flex-end',
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 8,
    shadowColor: Colors.BLACK,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  // Modal stilleri
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContainer: {
    backgroundColor: Colors.WHITE,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.GRAY_900,
  },
  modalSection: {
    marginBottom: 20,
  },
  modalSectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.GRAY_700,
    marginBottom: 12,
  },
  currencyItem: {
    paddingHorizontal: 16,
    paddingVertical: 10,
    backgroundColor: Colors.GRAY_100,
    borderRadius: 12,
    marginRight: 8,
    marginBottom: 8,
  },
  selectedCurrencyItem: {
    backgroundColor: Colors.PRIMARY,
  },
  currencyItemText: {
    fontSize: 14,
    color: Colors.GRAY_800,
  },
  selectedCurrencyItemText: {
    color: Colors.WHITE,
    fontWeight: '600',
  },
  modalButton: {
    backgroundColor: Colors.PRIMARY,
    borderRadius: 12,
    paddingVertical: 14,
    alignItems: 'center',
    marginTop: 8,
  },
  modalButtonText: {
    color: Colors.WHITE,
    fontSize: 16,
    fontWeight: 'bold',
  },
  currencyItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  currencySymbolText: {
    fontSize: 16,
    fontWeight: 'bold',
    marginRight: 8,
    color: Colors.GRAY_800,
  },
  conversionContainer: {
    marginBottom: 12,
    paddingHorizontal: 8,
    alignItems: 'flex-end',
  },
  conversionText: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.GRAY_700,
  },
  exchangeRateText: {
    fontSize: 12,
    color: Colors.GRAY_500,
    marginTop: 2,
  },
  categoryContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: Colors.GRAY_300,
    paddingBottom: 8,
    marginBottom: 12,
  },
  categorySelector: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginLeft: 8,
    paddingVertical: 4,
  },
  categorySelectorText: {
    fontSize: 16,
    color: Colors.GRAY_900,
  },
  addCategoryButton: {
    width: 32,
    height: 32,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 16,
    backgroundColor: Colors.GRAY_100,
    marginLeft: 8,
  },
});
