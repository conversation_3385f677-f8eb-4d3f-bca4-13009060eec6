import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  TextInput,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Alert
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useSQLiteContext } from 'expo-sqlite';
import DateTimePicker from '@react-native-community/datetimepicker';
import { Colors } from '../constants/colors';
import CategoryForm from './category/CategoryForm';

/**
 * İşlem formu bileşeni
 *
 * @param {Object} props - Bileşen özellikleri
 * @param {boolean} props.visible - Görünürlük durumu
 * @param {Function} props.onClose - Kapatma işlevi
 * @param {Function} props.onSave - Kaydetme işlevi
 * @param {Object} props.transaction - Düzenlenecek işlem (opsiyonel)
 * @returns {JSX.Element} İşlem formu bileşeni
 */
export default function TransactionForm({ visible, onClose, onSave, transaction }) {
  const db = useSQLiteContext();

  const [type, setType] = useState(transaction?.type || 'expense');
  const [amount, setAmount] = useState(transaction?.amount?.toString() || '');
  const [description, setDescription] = useState(transaction?.description || '');
  const [date, setDate] = useState(transaction?.date ? new Date(transaction.date) : new Date());
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [categoryId, setCategoryId] = useState(transaction?.category_id || null);
  const [categories, setCategories] = useState([]);
  const [showCategoryForm, setShowCategoryForm] = useState(false);

  // Kategorileri yükleme fonksiyonu
  const loadCategories = async () => {
    try {
      const result = await db.getAllAsync(`
        SELECT * FROM categories
        WHERE type = ? OR type = 'both'
        ORDER BY name
      `, [type]);

      setCategories(result);

      // Eğer seçili kategori yoksa veya seçili kategori türü uygun değilse
      if (!categoryId || !result.some(cat => cat.id === categoryId)) {
        setCategoryId(result.length > 0 ? result[0].id : null);
      }
    } catch (error) {
      console.error('Kategori yükleme hatası:', error);
    }
  };

  // Kategorileri yükle
  useEffect(() => {
    loadCategories();
  }, [type]);

  // Tarih değişikliği
  const onDateChange = (event, selectedDate) => {
    setShowDatePicker(false);
    if (selectedDate) {
      setDate(selectedDate);
    }
  };

  // Tarih formatla
  const formatDate = (date) => {
    return date.toLocaleDateString('tr-TR');
  };

  // Yeni kategori ekleme modalını aç
  const handleAddCategory = () => {
    setShowCategoryForm(true);
  };

  // Kategori kaydetme
  const handleSaveCategory = (newCategory) => {
    // Kategorileri yeniden yükle
    loadCategories();

    // Yeni kategoriyi seç
    if (newCategory?.id) {
      setCategoryId(newCategory.id);

      // Kategori ekleme başarılı mesajı
      Alert.alert(
        'Başarılı',
        'Kategori başarıyla eklendi ve seçildi.',
        [{ text: 'Tamam' }]
      );
    }
  };

  // İşlemi kaydet
  const saveTransaction = async () => {
    try {
      if (!amount || isNaN(parseFloat(amount)) || parseFloat(amount) <= 0) {
        Alert.alert('Hata', 'Lütfen geçerli bir tutar girin.');
        return;
      }

      if (!categoryId) {
        Alert.alert('Hata', 'Lütfen bir kategori seçin.');
        return;
      }

      const parsedAmount = parseFloat(amount);

      if (transaction?.id) {
        // Mevcut işlemi güncelle
        await db.runAsync(`
          UPDATE transactions
          SET type = ?, amount = ?, description = ?, date = ?, category_id = ?
          WHERE id = ?
        `, [type, parsedAmount, description, date.toISOString().split('T')[0], categoryId, transaction.id]);
      } else {
        // Yeni işlem ekle
        await db.runAsync(`
          INSERT INTO transactions (type, amount, description, date, category_id)
          VALUES (?, ?, ?, ?, ?)
        `, [type, parsedAmount, description, date.toISOString().split('T')[0], categoryId]);
      }

      onSave();
    } catch (error) {
      console.error('İşlem kaydetme hatası:', error);
      Alert.alert('Hata', 'İşlem kaydedilirken bir hata oluştu.');
    }
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      >
        <View style={styles.content}>
          <View style={styles.header}>
            <Text style={styles.title}>
              {transaction?.id ? 'İşlemi Düzenle' : 'Yeni İşlem Ekle'}
            </Text>
            <TouchableOpacity onPress={onClose}>
              <MaterialIcons name="close" size={24} color="#999" />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.form}>
            <View style={styles.typeSelector}>
              <TouchableOpacity
                style={[
                  styles.typeButton,
                  type === 'expense' && styles.activeTypeButton
                ]}
                onPress={() => setType('expense')}
              >
                <MaterialIcons
                  name="arrow-downward"
                  size={20}
                  color={type === 'expense' ? '#fff' : Colors.DANGER}
                />
                <Text
                  style={[
                    styles.typeButtonText,
                    type === 'expense' && styles.activeTypeButtonText
                  ]}
                >
                  Gider
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.typeButton,
                  type === 'income' && styles.activeIncomeButton
                ]}
                onPress={() => setType('income')}
              >
                <MaterialIcons
                  name="arrow-upward"
                  size={20}
                  color={type === 'income' ? '#fff' : Colors.SUCCESS}
                />
                <Text
                  style={[
                    styles.typeButtonText,
                    type === 'income' && styles.activeTypeButtonText
                  ]}
                >
                  Gelir
                </Text>
              </TouchableOpacity>
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Tutar</Text>
              <View style={styles.amountInputContainer}>
                <Text style={styles.currencySymbol}>₺</Text>
                <TextInput
                  style={styles.amountInput}
                  value={amount}
                  onChangeText={setAmount}
                  keyboardType="numeric"
                  placeholder="0.00"
                />
              </View>
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Açıklama</Text>
              <TextInput
                style={styles.input}
                value={description}
                onChangeText={setDescription}
                placeholder="İşlem açıklaması"
              />
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Tarih</Text>
              <TouchableOpacity
                style={styles.dateSelector}
                onPress={() => setShowDatePicker(true)}
              >
                <Text style={styles.dateText}>{formatDate(date)}</Text>
                <MaterialIcons name="calendar-today" size={20} color={Colors.PRIMARY} />
              </TouchableOpacity>

              {showDatePicker && (
                <DateTimePicker
                  value={date}
                  mode="date"
                  display="default"
                  onChange={onDateChange}
                />
              )}
            </View>

            <View style={styles.formGroup}>
              <View style={styles.labelContainer}>
                <Text style={styles.label}>Kategori</Text>
                <TouchableOpacity
                  style={styles.addCategoryButtonSmall}
                  onPress={handleAddCategory}
                >
                  <MaterialIcons name="add" size={16} color={Colors.PRIMARY} />
                  <Text style={styles.addCategoryTextSmall}>Yeni</Text>
                </TouchableOpacity>
              </View>

              <View style={styles.categoryGridContainer}>
                <ScrollView
                  horizontal
                  showsHorizontalScrollIndicator={false}
                  style={styles.categoryList}
                >
                  {categories.map(category => (
                    <TouchableOpacity
                      key={category.id}
                      style={[
                        styles.categoryItem,
                        categoryId === category.id && {
                          backgroundColor: category.color || Colors.PRIMARY
                        }
                      ]}
                      onPress={() => setCategoryId(category.id)}
                    >
                      <View style={styles.categoryIconContainer}>
                        <MaterialIcons
                          name={category.icon || 'category'}
                          size={24}
                          color={categoryId === category.id ? '#fff' : category.color || Colors.PRIMARY}
                        />
                      </View>
                      <Text
                        style={[
                          styles.categoryText,
                          categoryId === category.id && styles.activeCategoryText
                        ]}
                        numberOfLines={1}
                      >
                        {category.name}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </ScrollView>
              </View>
            </View>
          </ScrollView>

          <View style={styles.footer}>
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={onClose}
            >
              <Text style={styles.cancelButtonText}>İptal</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.saveButton}
              onPress={saveTransaction}
            >
              <Text style={styles.saveButtonText}>Kaydet</Text>
            </TouchableOpacity>
          </View>
        </View>
      </KeyboardAvoidingView>

      {/* Kategori Ekleme/Düzenleme Modalı */}
      <CategoryForm
        visible={showCategoryForm}
        onClose={() => setShowCategoryForm(false)}
        category={null}
        type={type}
        onSave={handleSaveCategory}
      />
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  content: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '90%',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
  },
  form: {
    padding: 16,
  },
  typeSelector: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  typeButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: '#eee',
    borderRadius: 8,
    marginRight: 8,
  },
  activeTypeButton: {
    backgroundColor: Colors.DANGER,
    borderColor: Colors.DANGER,
  },
  activeIncomeButton: {
    backgroundColor: Colors.SUCCESS,
    borderColor: Colors.SUCCESS,
  },
  typeButtonText: {
    marginLeft: 8,
    fontSize: 16,
    fontWeight: '500',
  },
  activeTypeButtonText: {
    color: '#fff',
  },
  formGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    color: '#333333',
    marginBottom: 8,
  },
  amountInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 12,
  },
  currencySymbol: {
    fontSize: 18,
    color: '#333333',
    marginRight: 8,
  },
  amountInput: {
    flex: 1,
    fontSize: 18,
    paddingVertical: 12,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
  },
  dateSelector: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
  },
  dateText: {
    fontSize: 16,
    color: '#333333',
  },
  categoryList: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  categoryItem: {
    width: 80,
    alignItems: 'center',
    paddingVertical: 8,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 12,
    marginRight: 8,
  },
  categoryText: {
    fontSize: 12,
    color: '#333333',
    textAlign: 'center',
    paddingHorizontal: 4,
    width: '100%',
  },
  activeCategoryText: {
    color: '#fff',
  },
  addCategoryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderWidth: 1,
    borderColor: Colors.PRIMARY_LIGHT || '#d0e8f2',
    borderRadius: 20,
    marginRight: 8,
    backgroundColor: '#f0f8ff',
  },
  addCategoryText: {
    marginLeft: 4,
    fontSize: 14,
    color: Colors.PRIMARY,
    fontWeight: '500',
  },
  labelContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  addCategoryButtonSmall: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    backgroundColor: '#f0f8ff',
    borderWidth: 1,
    borderColor: Colors.PRIMARY_LIGHT || '#d0e8f2',
  },
  addCategoryTextSmall: {
    fontSize: 12,
    color: Colors.PRIMARY,
    marginLeft: 2,
  },
  categoryGridContainer: {
    marginBottom: 8,
  },
  categoryIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f5f5f5',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 4,
  },
  footer: {
    flexDirection: 'row',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  cancelButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    marginRight: 8,
  },
  cancelButtonText: {
    fontSize: 16,
    color: '#333333',
  },
  saveButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    backgroundColor: Colors.PRIMARY,
    borderRadius: 8,
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#fff',
  },
});
