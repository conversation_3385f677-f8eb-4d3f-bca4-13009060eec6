import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useSQLiteContext } from 'expo-sqlite';
import { Colors } from '../constants/colors';
import TransactionForm from './TransactionForm';

/**
 * İşlem öğesi bileşeni
 *
 * @param {Object} props - Bileşen özellikleri
 * @param {Object} props.transaction - İşlem verisi
 * @param {Function} props.onRefresh - Yenileme işlevi
 * @returns {JSX.Element} İşlem öğesi bileşeni
 */
export default function TransactionItem({ transaction, onRefresh }) {
  const db = useSQLiteContext();
  const [isEditing, setIsEditing] = useState(false);

  // Tarih formatla
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('tr-TR');
  };

  // İşlemi sil
  const deleteTransaction = async () => {
    try {
      await db.runAsync('DELETE FROM transactions WHERE id = ?', [transaction.id]);
      onRefresh();
    } catch (error) {
      console.error('İşlem silme hatası:', error);
      Alert.alert('Hata', 'İşlem silinirken bir hata oluştu.');
    }
  };

  // Silme onayı
  const confirmDelete = () => {
    Alert.alert(
      'İşlemi Sil',
      'Bu işlemi silmek istediğinizden emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        { text: 'Sil', style: 'destructive', onPress: deleteTransaction }
      ]
    );
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={styles.content}
        onPress={() => setIsEditing(true)}
      >
        <View style={styles.leftSection}>
          <View
            style={[
              styles.categoryIcon,
              { backgroundColor: transaction.category_color || Colors.PRIMARY }
            ]}
          >
            <MaterialIcons
              name={transaction.category_icon || 'category'}
              size={20}
              color="#fff"
            />
          </View>

          <View style={styles.details}>
            <Text style={styles.description}>
              {transaction.description || transaction.category_name || 'İsimsiz İşlem'}
            </Text>
            <Text style={styles.date}>{formatDate(transaction.date)}</Text>
          </View>
        </View>

        <View style={styles.rightSection}>
          <Text
            style={[
              styles.amount,
              transaction.type === 'income' ? styles.incomeAmount : styles.expenseAmount
            ]}
          >
            {transaction.type === 'income' ? '+' : '-'}₺{transaction.amount.toLocaleString('tr-TR')}
          </Text>

          <TouchableOpacity
            style={styles.deleteButton}
            onPress={confirmDelete}
          >
            <MaterialIcons name="delete" size={20} color="#999" />
          </TouchableOpacity>
        </View>
      </TouchableOpacity>

      {isEditing && (
        <TransactionForm
          visible={isEditing}
          onClose={() => setIsEditing(false)}
          onSave={() => {
            setIsEditing(false);
            onRefresh();
          }}
          transaction={transaction}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    borderRadius: 12,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  content: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  leftSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  categoryIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.PRIMARY,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  details: {
    flex: 1,
  },
  description: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333333',
    marginBottom: 4,
  },
  date: {
    fontSize: 14,
    color: '#333333',
  },
  rightSection: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  amount: {
    fontSize: 16,
    fontWeight: 'bold',
    marginRight: 12,
  },
  incomeAmount: {
    color: Colors.SUCCESS,
  },
  expenseAmount: {
    color: Colors.DANGER,
  },
  deleteButton: {
    padding: 4,
  },
});
