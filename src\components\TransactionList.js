import React from 'react';
import { View, Text, StyleSheet, FlatList } from 'react-native';
import { Colors } from '../constants/colors';
import TransactionItem from './TransactionItem';

/**
 * İşlem listesi bileşeni
 * 
 * @param {Object} props - <PERSON><PERSON><PERSON>en ö<PERSON>leri
 * @param {Array} props.transactions - <PERSON>şlem listesi
 * @param {Function} props.onRefresh - <PERSON><PERSON><PERSON><PERSON>
 * @returns {JSX.Element} İşlem listesi bileşeni
 */
export default function TransactionList({ transactions, onRefresh }) {
  if (!transactions || transactions.length === 0) {
    return (
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyText}>Henüz işlem bulunmuyor</Text>
      </View>
    );
  }

  return (
    <FlatList
      data={transactions}
      keyExtractor={(item) => item.id.toString()}
      renderItem={({ item }) => (
        <TransactionItem
          transaction={item}
          onRefresh={onRefresh}
        />
      )}
      scrollEnabled={false}
    />
  );
}

const styles = StyleSheet.create({
  emptyContainer: {
    padding: 16,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    color: '#333333',
  },
});
