import React from 'react';
import { TouchableOpacity, Platform } from 'react-native';
import DateTimePicker from '@react-native-community/datetimepicker';
import { format } from 'date-fns';
import { tr } from 'date-fns/locale';
import  Input from './Input';

/**
 * <PERSON><PERSON>h seçici bileşeni
 * @param {Object} props Component props
 * @param {Date} props.value Seçili tarih
 * @param {Function} props.onChange Tarih değ<PERSON>im handler'ı
 * @param {string} [props.error] Hata mesajı
 * @param {string} [props.placeholder] Placeholder metni
 */
const DatePicker = ({ value, onChange, error, placeholder = 'Ta<PERSON>h seçiniz' }) => {
    const [show, setShow] = React.useState(false);

    const handleChange = (event, selectedDate) => {
        setShow(false);
        if (event.type === 'dismissed') return;

        onChange?.(selectedDate || value);
    };

    const formattedDate = value ? format(new Date(value), 'dd.MM.yyyy', { locale: tr }) : '';

    return (
        <>
            <TouchableOpacity onPress={() => setShow(true)}>
                <Input
                    value={formattedDate}
                    placeholder={placeholder}
                    error={error}
                    editable={false}
                />
            </TouchableOpacity>

            {show && (
                <DateTimePicker
                    value={value ? new Date(value) : new Date()}
                    mode="date"
                    display={'default'}
                    onChange={handleChange}
                    locale="tr-TR"
                />
            )}
        </>
    );
};

export default DatePicker;