import React, { useState, useRef, useEffect } from 'react';
import { View, Text, TextInput, StyleSheet, TouchableOpacity, Dimensions, Animated } from 'react-native';
import { Picker } from '@react-native-picker/picker';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors } from '../../constants/colors';

/**
 * Düzenli gelir için temel bilgi girişi bileşeni
 * 
 * @param {Object} props - Bileşen özellikleri
 * @param {string} props.title - <PERSON><PERSON><PERSON> ba<PERSON>
 * @param {Function} props.setTitle - Başlık değiştirme fonksiyonu
 * @param {string} props.amount - <PERSON><PERSON><PERSON> miktarı
 * @param {Function} props.handleAmountChange - Miktar değişimi i<PERSON>ley<PERSON>
 * @param {string} props.currencyCode - Para birimi kodu
 * @param {Function} props.setCurrencyCode - Para birimi değiştirme fonksiyonu
 * @param {Object} props.fieldErrors - <PERSON>
 * @param {Function} props.handleInputFocus - Input odak işleyici
 * @param {Function} props.handleInputBlur - Input odak kaybı işleyici
 * @param {Function} props.getInputStyle - Input stil işleyici
 * @param {Function} props.getFieldErrorText - Hata metni işleyici
 * @param {string} props.getFormattedAmount - Formatlanmış miktar
 * @returns {JSX.Element} Temel bilgiler formu
 */
const RegularIncomeBasicInfo = React.memo(({
  title,
  setTitle,
  amount,
  currencyCode,
  setCurrencyCode,
  fieldErrors,
  handleInputFocus,
  handleInputBlur,
  getInputStyle,
  getFieldErrorText,
  handleAmountChange,
  getFormattedAmount
}) => {
  // Desteklenen para birimleri - useMemo ile optimize edildi
  const currencyOptions = React.useMemo(() => [
    { value: 'TRY', label: 'Türk Lirası', shortLabel: 'TRY', icon: '₺', color: '#e30a17', flag: '🇹🇷' },
    { value: 'USD', label: 'Amerikan Doları', shortLabel: 'USD', icon: '$', color: '#3c8a3f', flag: '🇺🇸' },
    { value: 'EUR', label: 'Euro', shortLabel: 'EUR', icon: '€', color: '#0052b4', flag: '🇪🇺' },
    { value: 'GBP', label: 'İngiliz Sterlini', shortLabel: 'GBP', icon: '£', color: '#003399', flag: '🇬🇧' }
  ], []);
  
  // Seçili para birimi bilgilerini al
  const selectedCurrency = React.useMemo(() => 
    currencyOptions.find(option => option.value === currencyCode) || currencyOptions[0],
  [currencyCode, currencyOptions]);
    // Temel bilgilerin tamamlanmış olup olmadığını kontrol et
  const isBasicInfoComplete = React.useMemo(() => {
    return !!(title && amount && 
      !isNaN(parseFloat(amount.replace(/[^\d,.]/g, '').replace(',', '.'))) && 
      parseFloat(amount.replace(/[^\d,.]/g, '').replace(',', '.')) > 0);
  }, [title, amount]);

  // Yardım bölümü için animasyon değeri
  const helpAnimation = useRef(new Animated.Value(0)).current;
  
  // Yardım görünürlüğü değiştiğinde animasyonu çalıştır
  const [showHelp, setShowHelp] = useState(false);
  
  useEffect(() => {
    Animated.timing(helpAnimation, {
      toValue: showHelp ? 1 : 0,
      duration: 300,
      useNativeDriver: false,
    }).start();
  }, [showHelp]);
  
  // Animasyon değerlerini hesapla
  const helpContainerHeight = helpAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 150], // Tahmini yükseklik
  });
  
  const helpOpacity = helpAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 1],
  });
  
  const toggleHelp = () => {
    setShowHelp(!showHelp);
  };

  // Ekran genişliğini al
  const screenWidth = Dimensions.get('window').width;
  
  // Ekran genişliğine göre düzen belirleme
  const isSmallScreen = screenWidth < 360;
  
  // Satır düzeni için stil hesaplama
  const rowContainerStyle = React.useMemo(() => [
    styles.rowContainer,
    isSmallScreen && styles.rowContainerColumn
  ], [isSmallScreen]);
  
  // Miktar konteynırı için stil hesaplama
  const amountContainerStyle = React.useMemo(() => [
    styles.amountContainer,
    isSmallScreen ? styles.fullWidthContainer : {}
  ], [isSmallScreen]);
  
  // Para birimi konteynırı için stil hesaplama
  const currencyContainerStyle = React.useMemo(() => [
    styles.currencyContainer,
    isSmallScreen ? styles.fullWidthContainer : {}
  ], [isSmallScreen]);
  
  // Picker odak durumu
  const [isPickerFocused, setPickerFocused] = useState(false);
  
  // Picker konteynırı stilleri
  const pickerContainerStyle = React.useMemo(() => [
    styles.pickerContainer,
    isSmallScreen ? { marginBottom: 0 } : null, // Override marginBottom if small screen
    isPickerFocused && styles.pickerContainerFocused,
  ].filter(Boolean), [isSmallScreen, isPickerFocused]);
  
  // Formatlanmış miktar için animasyon
  const formattedAmountAnim = useRef(new Animated.Value(0)).current;
  
  // Miktar değiştiğinde animasyonu çalıştır
  useEffect(() => {
    const isValidAmount = amount && 
      !isNaN(parseFloat(amount.replace(/[^\d,.]/g, '').replace(',', '.'))) && 
      parseFloat(amount.replace(/[^\d,.]/g, '').replace(',', '.')) > 0;
      
    Animated.timing(formattedAmountAnim, {
      toValue: isValidAmount ? 1 : 0,
      duration: 300,
      useNativeDriver: true,
    }).start();
  }, [amount]);
  
  return (
    <View style={styles.section}>
      <View style={styles.sectionHeader}>
        <View style={styles.sectionTitleContainer}>
          <View style={styles.sectionIconContainer}>
            <MaterialIcons name="info-outline" size={20} color={Colors.PRIMARY} />
          </View>
          <Text style={styles.sectionTitleText}>Temel Bilgiler</Text>
          
          {/* Tamamlanma durumunu göster */}
          {isBasicInfoComplete ? (
            <View style={styles.completionBadge}>
              <MaterialIcons name="check-circle" size={16} color={Colors.SUCCESS} />
              <Text style={styles.completionBadgeText}>Tamamlandı</Text>
            </View>
          ) : (
            <View style={styles.incompleteBadge}>
              <MaterialIcons name="error-outline" size={16} color={Colors.WARNING} />
              <Text style={styles.incompleteBadgeText}>Eksik Bilgi</Text>
            </View>
          )}
          
          {/* Yardım butonu */}
          <TouchableOpacity 
            style={[styles.helpButton, showHelp && styles.helpButtonActive]} 
            onPress={toggleHelp}
          >
            <MaterialIcons 
              name={showHelp ? "close" : "help-outline"} 
              size={20} 
              color={showHelp ? Colors.PRIMARY : Colors.GRAY_600} 
            />
          </TouchableOpacity>
        </View>
        <View style={styles.sectionHeaderDivider} />
      </View>
      
      {/* Yardım metni - animasyonlu */}
      <Animated.View style={[
        styles.helpContainer, 
        { 
          height: helpContainerHeight, 
          opacity: helpOpacity,
          overflow: 'hidden'
        }
      ]}>
        <Text style={styles.helpTitle}>Temel Bilgiler Hakkında</Text>
        <Text style={styles.helpText}>
          Bu bölümde düzenli geliriniz için temel bilgileri girmeniz gerekmektedir. 
          Başlık ve miktar alanları zorunludur. Para birimi olarak farklı seçenekler mevcuttur.
        </Text>
        <View style={styles.helpTipContainer}>
          <MaterialIcons name="lightbulb-outline" size={16} color={Colors.WARNING} style={{marginRight: 6}} />
          <Text style={styles.helpTip}>
            İpucu: Tutarı girerken virgül (,) kullanarak kuruş bilgisini ekleyebilirsiniz.
          </Text>
        </View>
      </Animated.View>
      
      <View style={styles.inputGroup}>
        <Text style={styles.label}>
          Başlık
          <Text style={styles.requiredStar}> *</Text>
        </Text>
        <View style={[
          styles.titleInputContainer,
          fieldErrors?.title ? styles.inputError : {}
        ]}>
          <View style={styles.titleInputIcon}>
            <MaterialIcons name="label-outline" size={18} color={Colors.GRAY_600} />
          </View>          <TextInput
            style={[styles.titleInput, getInputStyle('title')]}
            value={title}
            onChangeText={setTitle}
            placeholder="Örn: Maaş, Kira Geliri"
            accessibilityLabel="Gelir başlığı"
            accessibilityHint="Maaş veya gelir için bir başlık girin"
            onFocus={() => handleInputFocus('title')}
            onBlur={() => handleInputBlur('title')}
          />
        </View>
        
        {!title && !fieldErrors?.title && (
          <View style={styles.helperTextContainer}>
            <MaterialIcons name="info-outline" size={12} color={Colors.GRAY_500} style={{marginRight: 4}} />
            <Text style={styles.helperText}>Gelir kaynağını tanımlayan kısa bir başlık girin</Text>
          </View>
        )}
        
        {getFieldErrorText('title', 'Gelirin başlığını girmelisiniz')}
      </View>
      
      <View style={rowContainerStyle}>
        <View style={amountContainerStyle}>
          <Text style={styles.label}>
            Miktar
            <Text style={styles.requiredStar}> *</Text>
          </Text>
          <View style={[
            styles.amountInputContainer,
            fieldErrors?.amount ? styles.inputError : {}
          ]}>
            <View style={styles.titleInputIcon}>
              <MaterialIcons name="attach-money" size={18} color={Colors.GRAY_600} />
            </View>            <TextInput
              style={[styles.titleInput, getInputStyle('amount')]}
              value={amount}
              onChangeText={handleAmountChange}
              placeholder="Örn: 5000"
              keyboardType="numeric"
              accessibilityLabel="Gelir miktarı"
              accessibilityHint="Gelir miktarını rakamla girin"
              onFocus={() => handleInputFocus('amount')}
              onBlur={() => handleInputBlur('amount')}
            />
          </View>
          
          {!amount && !fieldErrors?.amount && (
            <View style={styles.helperTextContainer}>
              <MaterialIcons name="info-outline" size={12} color={Colors.GRAY_500} style={{marginRight: 4}} />
              <Text style={styles.helperText}>Virgül (,) kullanarak kuruş ekleyebilirsiniz</Text>
            </View>
          )}
          
          {Boolean(amount && 
           !isNaN(parseFloat(amount.replace(/[^\d,.]/g, '').replace(',', '.'))) && 
           parseFloat(amount.replace(/[^\d,.]/g, '').replace(',', '.')) > 0) && (
            <Animated.View 
              style={[
                styles.formattedAmountContainer,
                {
                  opacity: formattedAmountAnim,
                  transform: [{
                    translateY: formattedAmountAnim.interpolate({
                      inputRange: [0, 1],
                      outputRange: [10, 0]
                    })
                  }]
                }
              ]}>
              <View style={styles.formattedAmountContent}>
                <MaterialIcons name="check-circle" size={16} color={Colors.SUCCESS} style={styles.formattedAmountIcon} />
                <Text style={styles.formattedAmount}>{getFormattedAmount}</Text>
                <Text style={styles.formattedAmountCurrency}>{selectedCurrency.icon}</Text>
              </View>
            </Animated.View>
          )}
          {getFieldErrorText('amount', 'Gelir miktarını girmelisiniz')}
        </View>
        
        <View style={currencyContainerStyle}>
          <Text style={styles.label}>Para Birimi</Text>
          <View style={pickerContainerStyle}>
            <View style={styles.pickerFlagContainer}>
              <Text style={styles.currencyFlag}>{selectedCurrency.flag}</Text>
            </View>
            <Picker
              selectedValue={currencyCode}
              onValueChange={(itemValue) => setCurrencyCode(itemValue)}
              style={styles.picker}
              accessibilityLabel="Para birimi seçici"
              mode="dropdown"
              onFocus={() => setPickerFocused(true)}
              onBlur={() => setPickerFocused(false)}
            >
              {currencyOptions.map((option) => (
                <Picker.Item 
                  key={option.value} 
                  label={`${option.flag} ${option.icon} ${option.label} (${option.shortLabel})`}
                  value={option.value}
                />
              ))}
            </Picker>
            <View style={[
              styles.selectedCurrencyIndicator,
              {backgroundColor: selectedCurrency.color + '15'}
            ]}>
              <Text style={[
                styles.currencySymbol,
                {color: selectedCurrency.color}
              ]}>
                {selectedCurrency.icon}
              </Text>
            </View>
          </View>
        </View>
      </View>
    </View>
  );
});

/**
 * Enhanced modern styles for RegularIncomeBasicInfo component
 * Following Material Design 3.0 principles with improved visual hierarchy
 */
const styles = StyleSheet.create({
  // === Section Container - Enhanced ===
  section: {
    backgroundColor: Colors.WHITE,
    borderRadius: 20,
    marginBottom: 28,
    paddingVertical: 28,
    paddingHorizontal: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.12,
    shadowRadius: 16,
    elevation: 8,
    borderWidth: 1,
    borderColor: Colors.GRAY_100,
    overflow: 'hidden',
  },
  
  // === Section Header - Enhanced ===
  sectionHeader: {
    marginBottom: 24,
  },
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sectionIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: Colors.PRIMARY + '15',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
    shadowColor: Colors.PRIMARY,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 3,
    borderWidth: 2,
    borderColor: Colors.PRIMARY + '25',
  },
  sectionTitleText: {
    fontSize: 20,
    fontWeight: '800',
    color: Colors.GRAY_900,
    letterSpacing: 0.3,
    flex: 1,
    lineHeight: 28,
  },
  sectionHeaderDivider: {
    height: 3,
    backgroundColor: Colors.PRIMARY + '20',
    marginTop: 18,
    borderRadius: 2,
    shadowColor: Colors.PRIMARY,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  
  // === Form Labels - Enhanced ===
  label: {
    fontSize: 16,
    color: Colors.GRAY_800,
    marginBottom: 10,
    fontWeight: '700',
    letterSpacing: 0.3,
  },
  
  // === Input Groups - Enhanced ===
  inputGroup: {
    marginBottom: 20,
  },
  
  // === Title Input Container - Enhanced ===
  titleInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: Colors.GRAY_200,
    borderRadius: 16,
    backgroundColor: Colors.WHITE,
    overflow: 'hidden',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.08,
    shadowRadius: 6,
    marginBottom: 10,
  },
  titleInputIcon: {
    padding: 16,
    backgroundColor: Colors.GRAY_50,
    borderRightWidth: 2,
    borderRightColor: Colors.GRAY_200,
  },
  titleInput: {
    flex: 1,
    paddingVertical: 16,
    paddingHorizontal: 16,
    fontSize: 16,
    fontWeight: '500',
    color: Colors.GRAY_900,
  },
  
  // === Row Layout - Enhanced ===
  rowContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    flexWrap: 'wrap',
    gap: 16,
  },
  rowContainerColumn: {
    flexDirection: 'column',
    gap: 20,
  },
  
  // === Amount Container - Enhanced ===
  amountContainer: {
    flex: 2,
    minWidth: 200,
  },
  fullWidthContainer: {
    flex: 1,
    width: '100%',
    marginBottom: 20,
  },
  amountInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: Colors.GRAY_200,
    borderRadius: 16,
    backgroundColor: Colors.WHITE,
    overflow: 'hidden',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.08,
    shadowRadius: 6,
    marginBottom: 10,
  },
  
  // === Currency Container - Enhanced ===
  currencyContainer: {
    flex: 1,
    minWidth: 140,
  },
  
  // === Picker Container - Enhanced ===
  pickerContainer: {
    position: 'relative',
    borderWidth: 2,
    borderColor: Colors.GRAY_200,
    borderRadius: 16,
    backgroundColor: Colors.WHITE,
    marginBottom: 20,
    overflow: 'hidden',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  pickerContainerFocused: {
    borderColor: Colors.PRIMARY,
    borderWidth: 3,
    backgroundColor: Colors.PRIMARY + '08',
    shadowColor: Colors.PRIMARY,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 6,
  },
  
  // === Currency Flag Container - Enhanced ===
  pickerFlagContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    bottom: 0,
    width: 48,
    backgroundColor: Colors.GRAY_50,
    justifyContent: 'center',
    alignItems: 'center',
    borderRightWidth: 2,
    borderRightColor: Colors.GRAY_200,
    zIndex: 2,
  },
  
  // === Selected Currency Indicator - Enhanced ===
  selectedCurrencyIndicator: {
    position: 'absolute',
    top: 0,
    right: 0,
    bottom: 0,
    width: 48,
    backgroundColor: Colors.PRIMARY + '18',
    justifyContent: 'center',
    alignItems: 'center',
    borderLeftWidth: 2,
    borderLeftColor: Colors.PRIMARY + '30',
    zIndex: 2,
  },
  currencySymbol: {
    fontSize: 20,
    fontWeight: '800',
    color: Colors.PRIMARY,
  },
  currencyFlag: {
    fontSize: 22,
  },
  
  // === Formatted Amount Container - Enhanced ===
  formattedAmountContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 12,
    marginBottom: 12,
    backgroundColor: Colors.SUCCESS + '12',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
    alignSelf: 'flex-start',
    borderWidth: 2,
    borderColor: Colors.SUCCESS + '35',
    shadowColor: Colors.SUCCESS,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 3,
  },
  formattedAmountContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  formattedAmountIcon: {
    marginRight: 8,
  },
  formattedAmount: {
    fontSize: 16,
    color: Colors.SUCCESS,
    fontWeight: '700',
    letterSpacing: 0.2,
  },
  formattedAmountCurrency: {
    fontSize: 16,
    fontWeight: '700',
    color: Colors.SUCCESS,
    marginLeft: 6,
  },
  
  // === Error Handling - Enhanced ===
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    marginBottom: 12,
  },
  errorText: {
    color: Colors.DANGER,
    fontSize: 14,
    marginLeft: 8,
    fontWeight: '600',
  },
  inputError: {
    borderColor: Colors.DANGER,
    borderWidth: 3,
    backgroundColor: Colors.DANGER + '08',
    shadowColor: Colors.DANGER,
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.2,
    shadowRadius: 6,
    elevation: 4,
  },
  
  // === Picker Style - Enhanced ===
  picker: {
    height: 56,
    width: '100%',
    color: Colors.GRAY_800,
    paddingLeft: 56, // Space for flag
    paddingRight: 16,
    fontSize: 16,
    fontWeight: '500',
  },
  
  // === Helper Text - Enhanced ===
  helperTextContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    marginBottom: 12,
    backgroundColor: Colors.INFO + '10',
    paddingVertical: 10,
    paddingHorizontal: 14,
    borderRadius: 10,
    borderLeftWidth: 3,
    borderLeftColor: Colors.INFO,
  },
  helperText: {
    color: Colors.GRAY_600,
    fontSize: 14,
    fontWeight: '500',
    lineHeight: 20,
    marginLeft: 8,
  },
  
  // === Required Field Indicator - Enhanced ===
  requiredStar: {
    color: Colors.DANGER,
    fontWeight: '800',
    fontSize: 18,
  },
  
  // === Completion Badges - Enhanced ===
  completionBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.SUCCESS + '18',
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginLeft: 'auto',
    borderWidth: 1,
    borderColor: Colors.SUCCESS + '30',
    shadowColor: Colors.SUCCESS,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  completionBadgeText: {
    color: Colors.SUCCESS,
    fontSize: 12,
    fontWeight: '700',
    marginLeft: 6,
    letterSpacing: 0.2,
  },
  incompleteBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.WARNING + '18',
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginLeft: 'auto',
    borderWidth: 1,
    borderColor: Colors.WARNING + '30',
    shadowColor: Colors.WARNING,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  incompleteBadgeText: {
    color: Colors.WARNING,
    fontSize: 12,
    fontWeight: '700',
    marginLeft: 6,
    letterSpacing: 0.2,
  },
  
  // === Help Button and Content - Enhanced ===
  helpButton: {
    marginLeft: 12,
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.GRAY_100,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  helpButtonActive: {
    backgroundColor: Colors.PRIMARY + '18',
    borderWidth: 2,
    borderColor: Colors.PRIMARY + '30',
  },
  helpContainer: {
    backgroundColor: Colors.GRAY_50,
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
    borderLeftWidth: 4,
    borderLeftColor: Colors.PRIMARY,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  helpTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: Colors.GRAY_800,
    marginBottom: 10,
    letterSpacing: 0.2,
  },
  helpText: {
    fontSize: 14,
    color: Colors.GRAY_700,
    lineHeight: 22,
    marginBottom: 12,
    fontWeight: '500',
  },
  helpTipContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.WARNING + '12',
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: Colors.WARNING + '25',
  },
  helpTip: {
    fontSize: 13,
    color: Colors.GRAY_800,
    flex: 1,
    lineHeight: 20,
    fontWeight: '500',
    marginLeft: 8,
  },
});

// Follow ESM exports pattern
export default RegularIncomeBasicInfo;
