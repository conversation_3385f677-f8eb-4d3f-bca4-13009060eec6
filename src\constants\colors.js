/**
 * <PERSON>y<PERSON>lama genelinde kullanılacak merkezi renk sistemi.
 * Bu dosya tüm renk tanımlamalarını içerir ve global bir Colors nesnesi oluşturur.
 */

// Ana renk paleti - Pastel ve modern renkler
export const palette = {
  white: '#FFFFFF',
  black: '#000000',
  transparent: 'transparent',

  // Pastel mavi
  blue: {
    50: '#e6f3ff',
    100: '#cce7ff',
    200: '#99ceff',
    300: '#66b5ff',
    400: '#339cff',
    500: '#6c5ce7', // primary - daha modern mor-mavi
    600: '#5a4bd1', // primary-dark
    700: '#483abb',
    800: '#3629a5',
    900: '#24188f'
  },

  // Pastel yeşil
  green: {
    50: '#e6fff2',
    100: '#ccffe6',
    200: '#99ffcc',
    300: '#66ffb3',
    400: '#33ff99',
    500: '#00cec9', // success - turkuaz
    600: '#00b5a7', // success-dark
    700: '#009c8f',
    800: '#008377',
    900: '#006a5f'
  },

  // Pastel kırmızı
  red: {
    50: '#ffe6e6',
    100: '#ffcccc',
    200: '#ff9999',
    300: '#ff6666',
    400: '#ff3333',
    500: '#ff7675', // danger - yumuşak kırmızı
    600: '#e84a5f', // danger-dark
    700: '#d13b4f',
    800: '#ba2c3f',
    900: '#a31d2f'
  },

  // Pastel sarı/turuncu
  yellow: {
    50: '#fff9e6',
    100: '#fff3cc',
    200: '#ffe799',
    300: '#ffdb66',
    400: '#ffcf33',
    500: '#fdcb6e', // warning - pastel sarı
    600: '#fab1a0', // warning-dark - şeftali
    700: '#f3a683',
    800: '#e9815e',
    900: '#e05c3b'
  },

  // Gri tonları
  gray: {
    50: '#f8f9fa',
    100: '#f1f3f5',
    200: '#e9ecef',
    300: '#dee2e6',
    400: '#ced4da',
    500: '#adb5bd',
    600: '#868e96',
    700: '#495057',
    800: '#343a40',
    900: '#212529'
  },

  // Ek pastel renkler
  pastel: {
    purple: '#a29bfe',
    pink: '#fd79a8',
    mint: '#55efc4',
    lavender: '#dfe6e9',
    peach: '#ffeaa7',
    coral: '#fab1a0'
  }
};

/**
 * Uygulama genelinde kullanılacak renkleri içerir
 */
export const Colors = {
  // Temel renkler
  WHITE: palette.white,
  BLACK: palette.black,
  TRANSPARENT: palette.transparent,

  // Ana renkler
  PRIMARY: palette.blue[500],
  PRIMARY_LIGHT: palette.blue[400],
  PRIMARY_DARK: palette.blue[600],

  // Anlamsal renkler
  SUCCESS: palette.green[500],
  SUCCESS_LIGHT: palette.green[400],
  SUCCESS_DARK: palette.green[600],

  DANGER: palette.red[500],
  DANGER_LIGHT: palette.red[400],
  DANGER_DARK: palette.red[600],

  WARNING: palette.yellow[500],
  WARNING_LIGHT: palette.yellow[400],
  WARNING_DARK: palette.yellow[600],

  INFO: palette.blue[500],
  INFO_LIGHT: palette.blue[400],
  INFO_DARK: palette.blue[600],

  // Gri tonları
  GRAY_50: palette.gray[50],
  GRAY_100: palette.gray[100],
  GRAY_200: palette.gray[200],
  GRAY_300: palette.gray[300],
  GRAY_400: palette.gray[400],
  GRAY_500: palette.gray[500],
  GRAY_600: palette.gray[600],
  GRAY_700: palette.gray[700],
  GRAY_800: palette.gray[800],
  GRAY_900: palette.gray[900],

  // İşlem renkleri
  INCOME: palette.green[500],
  INCOME_LIGHT: palette.green[400],
  INCOME_DARK: palette.green[600],

  EXPENSE: palette.red[500],
  EXPENSE_LIGHT: palette.red[400],
  EXPENSE_DARK: palette.red[600],

  // Tema uyumluluğu için eski stil renk nesnesi
  common: {
    white: palette.white,
    black: palette.black,
    transparent: palette.transparent
  },

  // Pastel renkler
  pastel: palette.pastel,

  primary: {
    main: palette.blue[500],
    light: palette.blue[400],
    dark: palette.blue[600]
  },

  neutral: palette.gray,

  income: {
    main: palette.green[500],
    light: palette.green[400],
    dark: palette.green[600]
  },

  expense: {
    main: palette.red[500],
    light: palette.red[400],
    dark: palette.red[600]
  },

  info: {
    main: palette.blue[500],
    light: palette.blue[400],
    dark: palette.blue[600]
  },

  warning: {
    main: palette.yellow[500],
    light: palette.yellow[400],
    dark: palette.yellow[600]
  },

  success: {
    main: palette.green[500],
    light: palette.green[400],
    dark: palette.green[600]
  },

  error: {
    main: palette.red[500],
    light: palette.red[400],
    dark: palette.red[600]
  },

  danger: {
    main: palette.red[500],
    light: palette.red[400],
    dark: palette.red[600]
  }
};

// Immediately set global Colors
if (typeof global !== 'undefined') {
  global.Colors = Colors;
}

// Eski tema sistemleri için uyumluluk sağlayan nesne
export const COLORS = Colors;

export default Colors;