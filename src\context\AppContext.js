import React, { createContext, useState, useContext, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useSQLiteContext } from 'expo-sqlite';
import * as settingsService from '../services/settingsService';
import { Colors } from '../constants/colors';

// AppContext - Uygulama genelinde kullanılacak değerler için merkezi depo
const AppContext = createContext({
  theme: Colors,
  defaultCurrency: 'TRY',
});

/**
 * Ana uygulama context sağlayıcısı
 * Tema ve para birimi gibi uygulama genelindeki ayarları yönetir
 * 
 * @param {Object} props - Bileşen props'ları
 * @param {React.ReactNode} props.children - Alt bileşenler
 * @returns {JSX.Element} AppProvider bileşeni
 */
export const AppProvider = ({ children }) => {
  const db = useSQLiteContext();
  const [defaultCurrency, setDefaultCurrency] = useState('TRY');

  // Varsayılan para birimini yükle
  useEffect(() => {
    const loadDefaultCurrency = async () => {
      try {
        // Önce AsyncStorage'den kontrol et
        const storedCurrency = await AsyncStorage.getItem('defaultCurrency');
        
        if (storedCurrency) {
          setDefaultCurrency(storedCurrency);
          return;
        }
        
        // Veritabanından kontrol et (eğer settingsService mevcutsa)
        if (db && settingsService.getSettings) {
          const settings = await settingsService.getSettings(db);
          if (settings && settings.defaultCurrency) {
            setDefaultCurrency(settings.defaultCurrency);
            
            // AsyncStorage'e de kaydet
            await AsyncStorage.setItem('defaultCurrency', settings.defaultCurrency);
          }
        }
      } catch (error) {
        console.error('Varsayılan para birimi yüklenirken hata:', error);
      }
    };
    
    loadDefaultCurrency();
  }, [db]);

  // Context değeri
  const value = {
    theme: Colors,
    defaultCurrency,
  };

  return (
    <AppContext.Provider value={value}>
      {children}
    </AppContext.Provider>
  );
};

/**
 * Uygulama context'ini kullanmak için hook
 * @returns {Object} AppContext değerleri
 */
export const useAppContext = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useAppContext must be used within an AppProvider');
  }
  return context;
};
