import React, { createContext, useContext, useState, useEffect } from 'react';
import * as LocalAuthentication from 'expo-local-authentication';
import * as SecureStore from 'expo-secure-store';
import { Alert } from 'react-native';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

/**
 * Güvenlik ve Kimlik Doğrulama Context
 * PIN ve biometrik giriş yönetimi
 */
export const AuthProvider = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [authSettings, setAuthSettings] = useState({
    pinEnabled: false,
    biometricEnabled: false,
    autoLockEnabled: false,
    autoLockTime: 5 // dakika
  });

  // Güvenlik ayarları anahtarları
  const STORAGE_KEYS = {
    PIN: 'user_pin',
    PIN_ENABLED: 'pin_enabled',
    BIOMETRIC_ENABLED: 'biometric_enabled',
    AUTO_LOCK_ENABLED: 'auto_lock_enabled',
    AUTO_LOCK_TIME: 'auto_lock_time',
    LAST_AUTH_TIME: 'last_auth_time'
  };

  // Başlangıçta ayarları yükle
  useEffect(() => {
    loadAuthSettings();
  }, []);

  // Güvenlik ayarlarını yükle
  const loadAuthSettings = async () => {
    try {
      setIsLoading(true);
      
      const [pinEnabled, biometricEnabled, autoLockEnabled, autoLockTime] = await Promise.all([
        SecureStore.getItemAsync(STORAGE_KEYS.PIN_ENABLED),
        SecureStore.getItemAsync(STORAGE_KEYS.BIOMETRIC_ENABLED),
        SecureStore.getItemAsync(STORAGE_KEYS.AUTO_LOCK_ENABLED),
        SecureStore.getItemAsync(STORAGE_KEYS.AUTO_LOCK_TIME)
      ]);

      const settings = {
        pinEnabled: pinEnabled === 'true',
        biometricEnabled: biometricEnabled === 'true',
        autoLockEnabled: autoLockEnabled === 'true',
        autoLockTime: autoLockTime ? parseInt(autoLockTime) : 5
      };

      setAuthSettings(settings);

      // Eğer güvenlik aktifse kimlik doğrulama gerekli
      if (settings.pinEnabled || settings.biometricEnabled) {
        setIsAuthenticated(false);
      } else {
        setIsAuthenticated(true);
      }

      setIsLoading(false);
    } catch (error) {
      console.error('Güvenlik ayarları yükleme hatası:', error);
      setIsAuthenticated(true); // Hata durumunda erişim ver
      setIsLoading(false);
    }
  };

  // PIN ayarla
  const setPin = async (pin) => {
    try {
      await SecureStore.setItemAsync(STORAGE_KEYS.PIN, pin);
      await SecureStore.setItemAsync(STORAGE_KEYS.PIN_ENABLED, 'true');
      
      setAuthSettings(prev => ({ ...prev, pinEnabled: true }));
      return { success: true };
    } catch (error) {
      console.error('PIN ayarlama hatası:', error);
      return { success: false, error: 'PIN ayarlanamadı' };
    }
  };

  // PIN kaldır
  const removePin = async () => {
    try {
      await SecureStore.deleteItemAsync(STORAGE_KEYS.PIN);
      await SecureStore.setItemAsync(STORAGE_KEYS.PIN_ENABLED, 'false');
      
      setAuthSettings(prev => ({ ...prev, pinEnabled: false }));
      return { success: true };
    } catch (error) {
      console.error('PIN kaldırma hatası:', error);
      return { success: false, error: 'PIN kaldırılamadı' };
    }
  };

  // PIN doğrula
  const verifyPin = async (inputPin) => {
    try {
      const storedPin = await SecureStore.getItemAsync(STORAGE_KEYS.PIN);
      return storedPin === inputPin;
    } catch (error) {
      console.error('PIN doğrulama hatası:', error);
      return false;
    }
  };

  // Biometrik ayarları
  const setBiometric = async (enabled) => {
    try {
      if (enabled) {
        // Biometrik desteği kontrol et
        const hasHardware = await LocalAuthentication.hasHardwareAsync();
        const isEnrolled = await LocalAuthentication.isEnrolledAsync();
        
        if (!hasHardware) {
          return { success: false, error: 'Cihazınız biometrik kimlik doğrulamayı desteklemiyor' };
        }
        
        if (!isEnrolled) {
          return { success: false, error: 'Cihazınızda kayıtlı biometrik veri bulunmuyor' };
        }
      }

      await SecureStore.setItemAsync(STORAGE_KEYS.BIOMETRIC_ENABLED, enabled.toString());
      setAuthSettings(prev => ({ ...prev, biometricEnabled: enabled }));
      
      return { success: true };
    } catch (error) {
      console.error('Biometrik ayarlama hatası:', error);
      return { success: false, error: 'Biometrik ayarlar güncellenemedi' };
    }
  };

  // Biometrik kimlik doğrulama
  const authenticateWithBiometric = async () => {
    try {
      const result = await LocalAuthentication.authenticateAsync({
        promptMessage: 'Uygulamaya erişmek için kimliğinizi doğrulayın',
        cancelLabel: 'İptal',
        fallbackLabel: 'PIN kullan'
      });

      return result.success;
    } catch (error) {
      console.error('Biometrik kimlik doğrulama hatası:', error);
      return false;
    }
  };

  // Otomatik kilit ayarları
  const setAutoLock = async (enabled, timeInMinutes = 5) => {
    try {
      await SecureStore.setItemAsync(STORAGE_KEYS.AUTO_LOCK_ENABLED, enabled.toString());
      await SecureStore.setItemAsync(STORAGE_KEYS.AUTO_LOCK_TIME, timeInMinutes.toString());
      
      setAuthSettings(prev => ({ 
        ...prev, 
        autoLockEnabled: enabled, 
        autoLockTime: timeInMinutes 
      }));
      
      return { success: true };
    } catch (error) {
      console.error('Otomatik kilit ayarlama hatası:', error);
      return { success: false, error: 'Otomatik kilit ayarları güncellenemedi' };
    }
  };

  // Kimlik doğrulama başarılı
  const authenticate = async () => {
    try {
      setIsAuthenticated(true);
      await SecureStore.setItemAsync(STORAGE_KEYS.LAST_AUTH_TIME, Date.now().toString());
    } catch (error) {
      console.error('Kimlik doğrulama kaydetme hatası:', error);
    }
  };

  // Çıkış yap (kilitle)
  const logout = () => {
    setIsAuthenticated(false);
  };

  // Biometrik desteği kontrol et
  const checkBiometricSupport = async () => {
    try {
      const hasHardware = await LocalAuthentication.hasHardwareAsync();
      const isEnrolled = await LocalAuthentication.isEnrolledAsync();
      const supportedTypes = await LocalAuthentication.supportedAuthenticationTypesAsync();
      
      return {
        hasHardware,
        isEnrolled,
        supportedTypes,
        isSupported: hasHardware && isEnrolled
      };
    } catch (error) {
      console.error('Biometrik destek kontrolü hatası:', error);
      return {
        hasHardware: false,
        isEnrolled: false,
        supportedTypes: [],
        isSupported: false
      };
    }
  };

  // Tam kimlik doğrulama (PIN veya biometrik)
  const performAuthentication = async () => {
    try {
      // Önce biometrik dene (eğer aktifse)
      if (authSettings.biometricEnabled) {
        const biometricResult = await authenticateWithBiometric();
        if (biometricResult) {
          await authenticate();
          return { success: true, method: 'biometric' };
        }
      }

      // Biometrik başarısızsa veya aktif değilse PIN'e yönlendir
      if (authSettings.pinEnabled) {
        return { success: false, method: 'pin', requirePin: true };
      }

      // Hiçbir güvenlik yoksa direkt geçir
      await authenticate();
      return { success: true, method: 'none' };
    } catch (error) {
      console.error('Kimlik doğrulama hatası:', error);
      return { success: false, error: 'Kimlik doğrulama başarısız' };
    }
  };

  const value = {
    isAuthenticated,
    isLoading,
    authSettings,
    setPin,
    removePin,
    verifyPin,
    setBiometric,
    authenticateWithBiometric,
    setAutoLock,
    authenticate,
    logout,
    checkBiometricSupport,
    performAuthentication,
    loadAuthSettings
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
