/**
 * Veritabanı düzeltme işlemleri
 * 
 * <PERSON><PERSON> dosya, veritabanı tablolarını düzeltmek için kullanılır.
 */

/**
 * Veritabanı tablolarını düzeltir
 * 
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @returns {Promise<void>}
 */
export const fixDatabase = async (db) => {
  try {
    console.log('Veritabanı düzeltme işlemi başlatılıyor...');
    
    // Tabloları yeniden oluştur
    await recreateTables(db);
    
    console.log('Veritabanı düzeltme işlemi tamamlandı.');
  } catch (error) {
    console.error('Veritabanı düzeltme hatası:', error);
  }
};

/**
 * Tabloları yeniden oluşturur
 * 
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @returns {Promise<void>}
 */
const recreateTables = async (db) => {
  try {
    // Kullanıcı ayarları tablosunu oluştur
    await db.execAsync(`
      CREATE TABLE IF NOT EXISTS user_settings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        key TEXT NOT NULL UNIQUE,
        value TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    
    // Varsayılan özel para birimini ekle
    await db.execAsync(`
      INSERT OR IGNORE INTO user_settings (key, value)
      VALUES ('custom_currency', 'GBP')
    `);
    
    // Maaş tablosunu düzelt
    await fixSalariesTable(db);
    
    // Maaş ödemeleri tablosunu düzelt
    await fixSalaryPaymentsTable(db);
    
    // İşlemler tablosunu düzelt
    await fixTransactionsTable(db);
    
  } catch (error) {
    console.error('Tabloları yeniden oluşturma hatası:', error);
    throw error;
  }
};

/**
 * Maaş tablosunu düzeltir
 * 
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @returns {Promise<void>}
 */
const fixSalariesTable = async (db) => {
  try {
    // Mevcut tabloyu kontrol et
    const tableExists = await checkTableExists(db, 'salaries');
    
    if (!tableExists) {
      console.log('salaries tablosu bulunamadı, oluşturuluyor...');
      await db.execAsync(`
        CREATE TABLE salaries (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          amount REAL NOT NULL,
          currency TEXT DEFAULT 'TRY',
          payment_day INTEGER NOT NULL,
          is_active INTEGER DEFAULT 1,
          category_id INTEGER,
          tax_rate REAL DEFAULT 0,
          notes TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          usd_equivalent REAL DEFAULT 0,
          eur_equivalent REAL DEFAULT 0,
          custom_currency TEXT DEFAULT 'GBP',
          custom_equivalent REAL DEFAULT 0
        )
      `);
      return;
    }
    
    // Sütunları kontrol et ve ekle
    await addColumnIfNotExists(db, 'salaries', 'usd_equivalent', 'REAL DEFAULT 0');
    await addColumnIfNotExists(db, 'salaries', 'eur_equivalent', 'REAL DEFAULT 0');
    await addColumnIfNotExists(db, 'salaries', 'custom_currency', 'TEXT DEFAULT "GBP"');
    await addColumnIfNotExists(db, 'salaries', 'custom_equivalent', 'REAL DEFAULT 0');
    
  } catch (error) {
    console.error('Maaş tablosunu düzeltme hatası:', error);
    throw error;
  }
};

/**
 * Maaş ödemeleri tablosunu düzeltir
 * 
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @returns {Promise<void>}
 */
const fixSalaryPaymentsTable = async (db) => {
  try {
    // Mevcut tabloyu kontrol et
    const tableExists = await checkTableExists(db, 'salary_payments');
    
    if (!tableExists) {
      console.log('salary_payments tablosu bulunamadı, oluşturuluyor...');
      await db.execAsync(`
        CREATE TABLE salary_payments (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          salary_id INTEGER NOT NULL,
          amount REAL NOT NULL,
          currency TEXT DEFAULT 'TRY',
          payment_date TEXT NOT NULL,
          is_paid INTEGER DEFAULT 0,
          transaction_id INTEGER,
          notes TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          usd_equivalent REAL DEFAULT 0,
          eur_equivalent REAL DEFAULT 0,
          custom_currency TEXT DEFAULT 'GBP',
          custom_equivalent REAL DEFAULT 0,
          FOREIGN KEY (salary_id) REFERENCES salaries (id) ON DELETE CASCADE
        )
      `);
      return;
    }
    
    // Sütunları kontrol et ve ekle
    await addColumnIfNotExists(db, 'salary_payments', 'usd_equivalent', 'REAL DEFAULT 0');
    await addColumnIfNotExists(db, 'salary_payments', 'eur_equivalent', 'REAL DEFAULT 0');
    await addColumnIfNotExists(db, 'salary_payments', 'custom_currency', 'TEXT DEFAULT "GBP"');
    await addColumnIfNotExists(db, 'salary_payments', 'custom_equivalent', 'REAL DEFAULT 0');
    
  } catch (error) {
    console.error('Maaş ödemeleri tablosunu düzeltme hatası:', error);
    throw error;
  }
};

/**
 * İşlemler tablosunu düzeltir
 * 
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @returns {Promise<void>}
 */
const fixTransactionsTable = async (db) => {
  try {
    // Mevcut tabloyu kontrol et
    const tableExists = await checkTableExists(db, 'transactions');
    
    if (!tableExists) {
      console.log('transactions tablosu bulunamadı, oluşturuluyor...');
      return;
    }
    
    // Sütunları kontrol et ve ekle
    await addColumnIfNotExists(db, 'transactions', 'usd_equivalent', 'REAL DEFAULT 0');
    await addColumnIfNotExists(db, 'transactions', 'eur_equivalent', 'REAL DEFAULT 0');
    await addColumnIfNotExists(db, 'transactions', 'custom_currency', 'TEXT DEFAULT "GBP"');
    await addColumnIfNotExists(db, 'transactions', 'custom_equivalent', 'REAL DEFAULT 0');
    await addColumnIfNotExists(db, 'transactions', 'preferred_currency', 'TEXT DEFAULT "TRY"');
    
  } catch (error) {
    console.error('İşlemler tablosunu düzeltme hatası:', error);
    throw error;
  }
};

/**
 * Tablonun varlığını kontrol eder
 * 
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {string} tableName - Tablo adı
 * @returns {Promise<boolean>} Tablo var mı?
 */
const checkTableExists = async (db, tableName) => {
  try {
    const result = await db.getFirstAsync(`
      SELECT name FROM sqlite_master 
      WHERE type='table' AND name=?
    `, [tableName]);
    
    return !!result;
  } catch (error) {
    console.error(`Tablo varlığı kontrol hatası (${tableName}):`, error);
    return false;
  }
};

/**
 * Sütun yoksa ekler
 * 
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @param {string} tableName - Tablo adı
 * @param {string} columnName - Sütun adı
 * @param {string} columnType - Sütun tipi
 * @returns {Promise<boolean>} Başarılı mı?
 */
const addColumnIfNotExists = async (db, tableName, columnName, columnType) => {
  try {
    // Sütunun varlığını kontrol et
    const columns = await db.getAllAsync(`PRAGMA table_info(${tableName})`);
    const columnExists = columns.some(col => col.name === columnName);
    
    if (!columnExists) {
      console.log(`${tableName} tablosuna ${columnName} sütunu ekleniyor...`);
      await db.execAsync(`ALTER TABLE ${tableName} ADD COLUMN ${columnName} ${columnType}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`Sütun ekleme hatası (${tableName}.${columnName}):`, error);
    return false;
  }
};
