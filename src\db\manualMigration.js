/**
 * Manuel migrasyon işlemi
 *
 * <PERSON><PERSON>, veritabanı tablolarını manuel olarak güncellemek için kullan<PERSON>lır.
 */

/**
 * Manuel migrasyon işlemini çalıştırır
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @returns {Promise<void>}
 */
export const runManualMigration = async (db) => {
  try {
    console.log('Manuel migrasyon başlatılıyor...');

    // Kullanıcı ayarları tablosunu oluştur
    try {
      await db.execAsync(`
        CREATE TABLE IF NOT EXISTS user_settings (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          key TEXT NOT NULL UNIQUE,
          value TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // Varsayılan özel para birimini ekle
      await db.execAsync(`
        INSERT OR IGNORE INTO user_settings (key, value)
        VALUES ('custom_currency', 'GBP')
      `);

      console.log('user_settings tablosu oluşturuldu.');
    } catch (error) {
      console.error('user_settings tablosu oluşturma hatası:', error);
    }

    // Doğrudan SQL ile tabloları güncelle
    try {
      // Salaries tablosunu güncelle
      await db.execAsync(`
        BEGIN TRANSACTION;

        -- Salaries tablosunu güncelle
        ALTER TABLE salaries ADD COLUMN IF NOT EXISTS usd_equivalent REAL DEFAULT 0;
        ALTER TABLE salaries ADD COLUMN IF NOT EXISTS eur_equivalent REAL DEFAULT 0;
        ALTER TABLE salaries ADD COLUMN IF NOT EXISTS custom_currency TEXT DEFAULT NULL;
        ALTER TABLE salaries ADD COLUMN IF NOT EXISTS custom_equivalent REAL DEFAULT 0;

        -- Salary_payments tablosunu güncelle
        ALTER TABLE salary_payments ADD COLUMN IF NOT EXISTS usd_equivalent REAL DEFAULT 0;
        ALTER TABLE salary_payments ADD COLUMN IF NOT EXISTS eur_equivalent REAL DEFAULT 0;
        ALTER TABLE salary_payments ADD COLUMN IF NOT EXISTS custom_currency TEXT DEFAULT NULL;
        ALTER TABLE salary_payments ADD COLUMN IF NOT EXISTS custom_equivalent REAL DEFAULT 0;

        -- Transactions tablosunu güncelle
        ALTER TABLE transactions ADD COLUMN IF NOT EXISTS usd_equivalent REAL DEFAULT 0;
        ALTER TABLE transactions ADD COLUMN IF NOT EXISTS eur_equivalent REAL DEFAULT 0;
        ALTER TABLE transactions ADD COLUMN IF NOT EXISTS custom_currency TEXT DEFAULT NULL;
        ALTER TABLE transactions ADD COLUMN IF NOT EXISTS custom_equivalent REAL DEFAULT 0;

        COMMIT;
      `);

      console.log('Tablolar başarıyla güncellendi.');
    } catch (error) {
      console.error('Tablo güncelleme hatası:', error);

      // Hata durumunda her tabloyu ayrı ayrı güncellemeyi dene
      try {
        console.log('Tabloları tek tek güncellemeye çalışılıyor...');

        // Salaries tablosunu güncelle
        try {
          await db.execAsync(`ALTER TABLE salaries ADD COLUMN usd_equivalent REAL DEFAULT 0`);
          console.log('salaries tablosuna usd_equivalent sütunu eklendi.');
        } catch (e) {
          console.log('salaries.usd_equivalent zaten var veya eklenemedi:', e.message);
        }

        try {
          await db.execAsync(`ALTER TABLE salaries ADD COLUMN eur_equivalent REAL DEFAULT 0`);
          console.log('salaries tablosuna eur_equivalent sütunu eklendi.');
        } catch (e) {
          console.log('salaries.eur_equivalent zaten var veya eklenemedi:', e.message);
        }

        try {
          await db.execAsync(`ALTER TABLE salaries ADD COLUMN custom_currency TEXT DEFAULT NULL`);
          console.log('salaries tablosuna custom_currency sütunu eklendi.');
        } catch (e) {
          console.log('salaries.custom_currency zaten var veya eklenemedi:', e.message);
        }

        try {
          await db.execAsync(`ALTER TABLE salaries ADD COLUMN custom_equivalent REAL DEFAULT 0`);
          console.log('salaries tablosuna custom_equivalent sütunu eklendi.');
        } catch (e) {
          console.log('salaries.custom_equivalent zaten var veya eklenemedi:', e.message);
        }

        // Salary_payments tablosunu güncelle
        try {
          await db.execAsync(`ALTER TABLE salary_payments ADD COLUMN usd_equivalent REAL DEFAULT 0`);
          console.log('salary_payments tablosuna usd_equivalent sütunu eklendi.');
        } catch (e) {
          console.log('salary_payments.usd_equivalent zaten var veya eklenemedi:', e.message);
        }

        try {
          await db.execAsync(`ALTER TABLE salary_payments ADD COLUMN eur_equivalent REAL DEFAULT 0`);
          console.log('salary_payments tablosuna eur_equivalent sütunu eklendi.');
        } catch (e) {
          console.log('salary_payments.eur_equivalent zaten var veya eklenemedi:', e.message);
        }

        try {
          await db.execAsync(`ALTER TABLE salary_payments ADD COLUMN custom_currency TEXT DEFAULT NULL`);
          console.log('salary_payments tablosuna custom_currency sütunu eklendi.');
        } catch (e) {
          console.log('salary_payments.custom_currency zaten var veya eklenemedi:', e.message);
        }

        try {
          await db.execAsync(`ALTER TABLE salary_payments ADD COLUMN custom_equivalent REAL DEFAULT 0`);
          console.log('salary_payments tablosuna custom_equivalent sütunu eklendi.');
        } catch (e) {
          console.log('salary_payments.custom_equivalent zaten var veya eklenemedi:', e.message);
        }

        // Transactions tablosunu güncelle
        try {
          await db.execAsync(`ALTER TABLE transactions ADD COLUMN usd_equivalent REAL DEFAULT 0`);
          console.log('transactions tablosuna usd_equivalent sütunu eklendi.');
        } catch (e) {
          console.log('transactions.usd_equivalent zaten var veya eklenemedi:', e.message);
        }

        try {
          await db.execAsync(`ALTER TABLE transactions ADD COLUMN eur_equivalent REAL DEFAULT 0`);
          console.log('transactions tablosuna eur_equivalent sütunu eklendi.');
        } catch (e) {
          console.log('transactions.eur_equivalent zaten var veya eklenemedi:', e.message);
        }

        try {
          await db.execAsync(`ALTER TABLE transactions ADD COLUMN custom_currency TEXT DEFAULT NULL`);
          console.log('transactions tablosuna custom_currency sütunu eklendi.');
        } catch (e) {
          console.log('transactions.custom_currency zaten var veya eklenemedi:', e.message);
        }

        try {
          await db.execAsync(`ALTER TABLE transactions ADD COLUMN custom_equivalent REAL DEFAULT 0`);
          console.log('transactions tablosuna custom_equivalent sütunu eklendi.');
        } catch (e) {
          console.log('transactions.custom_equivalent zaten var veya eklenemedi:', e.message);
        }

      } catch (innerError) {
        console.error('Tek tek güncelleme hatası:', innerError);
      }
    }

    console.log('Manuel migrasyon tamamlandı.');
  } catch (error) {
    console.error('Manuel migrasyon hatası:', error);
  }
};
