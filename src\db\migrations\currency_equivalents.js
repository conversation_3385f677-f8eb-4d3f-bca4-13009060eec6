/**
 * Transaction durumunu kontrol eder
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @returns {Promise<boolean>} Transaction aktif mi
 */
const checkTransactionStatus = async (db) => {
  try {
    // SQLite'da transaction durumunu kontrol etmenin bir yolu yok
    // Bu yüzden basit bir test sorgusu çalıştırıyoruz
    await db.execAsync('SELECT 1');
    return false; // Eğer hata yoksa transaction içinde değiliz
  } catch (error) {
    if (error.message?.includes('cannot start a transaction within a transaction')) {
      return true; // Transaction içindeyiz
    }
    return false;
  }
};

/**
 * Para birimlerinin döviz karşılıklarını saklamak için veritabanı yapısını güncelleyen migrasyon
 *
 * @param {Object} db - SQLite veritabanı bağlantısı
 * @returns {Promise<void>}
 */
export const migrateCurrencyEquivalents = async (db) => {
  try {
    // Transaction durumunu kontrol et
    const inTransaction = await checkTransactionStatus(db);

    if (!inTransaction) {
      await db.execAsync('BEGIN TRANSACTION;');
    }

    // Salaries tablosuna döviz karşılıkları ekle
    await db.execAsync(`
      ALTER TABLE salaries ADD COLUMN usd_equivalent REAL DEFAULT 0;
      ALTER TABLE salaries ADD COLUMN eur_equivalent REAL DEFAULT 0;
      ALTER TABLE salaries ADD COLUMN custom_currency TEXT DEFAULT NULL;
      ALTER TABLE salaries ADD COLUMN custom_equivalent REAL DEFAULT 0;
    `);

    // Salary_payments tablosuna döviz karşılıkları ekle
    await db.execAsync(`
      ALTER TABLE salary_payments ADD COLUMN usd_equivalent REAL DEFAULT 0;
      ALTER TABLE salary_payments ADD COLUMN eur_equivalent REAL DEFAULT 0;
      ALTER TABLE salary_payments ADD COLUMN custom_currency TEXT DEFAULT NULL;
      ALTER TABLE salary_payments ADD COLUMN custom_equivalent REAL DEFAULT 0;
    `);

    // Transactions tablosuna döviz karşılıkları ekle
    await db.execAsync(`
      ALTER TABLE transactions ADD COLUMN usd_equivalent REAL DEFAULT 0;
      ALTER TABLE transactions ADD COLUMN eur_equivalent REAL DEFAULT 0;
      ALTER TABLE transactions ADD COLUMN custom_currency TEXT DEFAULT NULL;
      ALTER TABLE transactions ADD COLUMN custom_equivalent REAL DEFAULT 0;
    `);

    // Work_shifts tablosuna döviz karşılıkları ekle
    await db.execAsync(`
      ALTER TABLE work_shifts ADD COLUMN usd_equivalent REAL DEFAULT 0;
      ALTER TABLE work_shifts ADD COLUMN eur_equivalent REAL DEFAULT 0;
      ALTER TABLE work_shifts ADD COLUMN custom_currency TEXT DEFAULT NULL;
      ALTER TABLE work_shifts ADD COLUMN custom_equivalent REAL DEFAULT 0;
    `);

    // Kullanıcı ayarları tablosunu oluştur veya güncelle
    const hasSettingsTable = await db.getFirstAsync(`
      SELECT name FROM sqlite_master WHERE type='table' AND name='user_settings'
    `);

    if (!hasSettingsTable) {
      await db.execAsync(`
        CREATE TABLE IF NOT EXISTS user_settings (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          key TEXT NOT NULL UNIQUE,
          value TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `);
    }

    // Varsayılan özel para birimini ekle
    await db.execAsync(`
      INSERT OR IGNORE INTO user_settings (key, value)
      VALUES ('custom_currency', 'GBP')
    `);

    // Transaction'ı tamamla (sadece biz başlattıysak)
    if (!inTransaction) {
      await db.execAsync('COMMIT;');
    }
  } catch (error) {
    // Hata durumunda transaction'ı geri al (sadece biz başlattıysak)
    if (!inTransaction) {
      try {
        await db.execAsync('ROLLBACK;');
      } catch (rollbackError) {
        console.warn('Rollback hatası:', rollbackError);
      }
    }

    // Eğer sütun zaten varsa hata verme
    if (error.message && error.message.includes('duplicate column name')) {
      console.warn('Döviz karşılıkları sütunları zaten mevcut:', error.message);
    } else {
      console.error('Döviz karşılıkları migrasyonu hatası:', error);
      throw error;
    }
  }
};
