import { executeSql } from '../../utils/dbUtils';

/**
 * `regular_incomes` tablosunu oluşturur.
 * @param {object} db - SQLite veritabanı nesnesi
 * @returns {Promise<void>}
 */
export const migrateRegularIncomes = async (db) => {
  try {
    console.log('Migrating regular_incomes table...');
    await executeSql(
      db,
      `
      CREATE TABLE IF NOT EXISTS regular_incomes (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        amount REAL NOT NULL,
        currency_code TEXT NOT NULL,
        payment_day INTEGER, -- <PERSON><PERSON><PERSON><PERSON> (1-31), ya da haftanın günü (Pazartesi için 1, Pazar için 7) recurrence_type'a bağlı
        recurrence_type TEXT NOT NULL CHECK(recurrence_type IN ('daily', 'weekly', 'monthly', 'yearly', 'custom')), -- 'custom' için recurrence_interval kullanılır
        recurrence_interval INTEGER, -- recurrence_type 'custom' ise gün sayısı, 'weekly' ise hafta sayısı vb.
        next_payment_date TEXT NOT NULL,
        notification_settings TEXT, -- JSON formatında: { enabled: true, days_before: 3, time: '09:00' }
        status TEXT DEFAULT 'active' CHECK(status IN ('active', 'paused', 'ended')),
        notes TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );
    `
    );

    // Gerekirse mevcut maaş verilerini bu tabloya taşımak için bir yapı eklenebilir.
    // Örneğin, 'salaries' tablosundaki her bir maaş için bir 'regular_incomes' kaydı oluşturulabilir.
    // Bu kısım, mevcut 'salaries' tablosunun yapısına ve istenen geçiş senaryosuna göre detaylandırılmalıdır.

    // updated_at trigger'ı (varsa dbUtils içinde merkezi bir yerden yönetilebilir)
    await executeSql(
      db,
      `
      CREATE TRIGGER IF NOT EXISTS update_regular_incomes_updated_at
      AFTER UPDATE ON regular_incomes
      FOR EACH ROW
      BEGIN
        UPDATE regular_incomes SET updated_at = CURRENT_TIMESTAMP WHERE id = OLD.id;
      END;
    `
    );
    console.log('regular_incomes table migrated successfully.');
  } catch (error) {
    console.error('Error migrating regular_incomes table:', error);
    throw error;
  }
};
