import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  RefreshControl,
  ActivityIndicator,
  Alert
} from 'react-native';
import { useSQLiteContext } from 'expo-sqlite';
import { useFocusEffect } from '@react-navigation/native';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors } from '../constants/colors';
import * as budgetService from '../services/budgetService';
import { formatCurrency } from '../utils/formatters';

/**
 * Bütçeler Ekranı
 *
 * @param {Object} props - Bileşen özellikleri
 * @param {Object} props.navigation - Navigation nesnesi
 * @returns {JSX.Element} Bütçeler Ekranı
 */
export default function BudgetsScreen({ navigation }) {
  const db = useSQLiteContext();

  // Durum değişkenleri
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [budgets, setBudgets] = useState([]);
  const [activeBudget, setActiveBudget] = useState(null);
  const [filter, setFilter] = useState('all'); // all, active, completed

  // Verileri yükle
  const loadData = useCallback(async () => {
    try {
      setLoading(true);

      // Tüm bütçeleri getir
      const allBudgets = await budgetService.getBudgets(db);
      setBudgets(allBudgets);

      // Aktif bütçe durumunu getir
      const currentBudget = await budgetService.getCurrentBudgetStatus(db);
      setActiveBudget(currentBudget);

      setLoading(false);
    } catch (error) {
      console.error('Bütçe verileri yükleme hatası:', error);
      Alert.alert('Hata', 'Bütçe verileri yüklenirken bir hata oluştu.');
      setLoading(false);
    }
  }, [db]);

  // Ekran odaklandığında verileri yükle
  useFocusEffect(
    useCallback(() => {
      loadData();
    }, [loadData])
  );

  // Yenileme işlemi
  const handleRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  // Yeni bütçe ekle
  const addNewBudget = () => {
    navigation.navigate('BudgetForm');
  };

  // Bütçe detaylarını görüntüle
  const viewBudgetDetails = (budgetId) => {
    navigation.navigate('BudgetDetail', { budgetId });
  };

  // Bütçeyi düzenle
  const editBudget = (budget) => {
    navigation.navigate('BudgetForm', { budget });
  };

  // Bütçeyi sil
  const deleteBudget = async (budgetId) => {
    try {
      await budgetService.deleteBudget(db, budgetId);
      Alert.alert('Başarılı', 'Bütçe başarıyla silindi.');
      loadData();
    } catch (error) {
      console.error('Bütçe silme hatası:', error);
      Alert.alert('Hata', 'Bütçe silinirken bir hata oluştu.');
    }
  };

  // Bütçe silme onayı
  const confirmDeleteBudget = (budget) => {
    Alert.alert(
      'Bütçeyi Sil',
      `"${budget.name}" bütçesini silmek istediğinize emin misiniz?`,
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: () => deleteBudget(budget.id)
        }
      ]
    );
  };

  // Periyot adını formatla
  const formatPeriod = (period) => {
    switch (period) {
      case 'daily': return 'Günlük';
      case 'weekly': return 'Haftalık';
      case 'monthly': return 'Aylık';
      case 'yearly': return 'Yıllık';
      default: return period;
    }
  };

  // Filtrelenmiş bütçeler
  const filteredBudgets = budgets.filter(budget => {
    const today = new Date().toISOString().split('T')[0];
    const isActive = budget.is_active === 1 &&
                    budget.start_date <= today &&
                    (!budget.end_date || budget.end_date >= today);

    if (filter === 'active') return isActive;
    if (filter === 'completed') return !isActive;
    return true;
  });

  // İlerleme çubuğu rengi
  const getProgressColor = (progress) => {
    if (progress < 50) return Colors.SUCCESS;
    if (progress < 80) return Colors.WARNING;
    return Colors.DANGER;
  };

  if (loading) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <ActivityIndicator size="large" color={Colors.PRIMARY} />
        <Text style={styles.loadingText}>Bütçe verileri yükleniyor...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <MaterialIcons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Bütçelerim</Text>
        <TouchableOpacity
          style={styles.addButton}
          onPress={addNewBudget}
        >
          <MaterialIcons name="add" size={24} color="#fff" />
        </TouchableOpacity>
      </View>

      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[Colors.PRIMARY]}
          />
        }
      >
        {/* Aktif Bütçe Özeti */}
        {activeBudget ? (
          <TouchableOpacity
            style={styles.activeBudgetCard}
            onPress={() => viewBudgetDetails(activeBudget.id)}
          >
            <View style={styles.activeBudgetHeader}>
              <Text style={styles.activeBudgetTitle}>Aktif Bütçe</Text>
              <Text style={styles.activeBudgetName}>{activeBudget.name}</Text>
            </View>

            <View style={styles.activeBudgetInfo}>
              <View style={styles.activeBudgetPeriod}>
                <MaterialIcons name="date-range" size={16} color={Colors.GRAY_600} />
                <Text style={styles.activeBudgetPeriodText}>
                  {formatPeriod(activeBudget.period)} ({new Date(activeBudget.start_date).toLocaleDateString('tr-TR')}
                  {activeBudget.end_date ? ` - ${new Date(activeBudget.end_date).toLocaleDateString('tr-TR')}` : ''})
                </Text>
              </View>

              <View style={styles.activeBudgetAmount}>
                <Text style={styles.activeBudgetAmountLabel}>Toplam Bütçe:</Text>
                <Text style={styles.activeBudgetAmountValue}>
                  {formatCurrency(activeBudget.totalBudget, 'TRY')}
                </Text>
              </View>

              <View style={styles.activeBudgetAmount}>
                <Text style={styles.activeBudgetAmountLabel}>Harcanan:</Text>
                <Text style={styles.activeBudgetAmountValue}>
                  {formatCurrency(activeBudget.totalSpent, 'TRY')}
                </Text>
              </View>

              <View style={styles.activeBudgetAmount}>
                <Text style={styles.activeBudgetAmountLabel}>Kalan:</Text>
                <Text style={[
                  styles.activeBudgetAmountValue,
                  { color: activeBudget.totalRemaining >= 0 ? Colors.SUCCESS : Colors.DANGER }
                ]}>
                  {formatCurrency(activeBudget.totalRemaining, 'TRY')}
                </Text>
              </View>
            </View>

            <View style={styles.progressContainer}>
              <View style={styles.progressBar}>
                <View
                  style={[
                    styles.progressFill,
                    {
                      width: `${activeBudget.totalProgress}%`,
                      backgroundColor: getProgressColor(activeBudget.totalProgress)
                    }
                  ]}
                />
              </View>
              <Text style={styles.progressText}>
                {activeBudget.totalProgress.toFixed(0)}% Kullanıldı
              </Text>
            </View>

            <View style={styles.viewDetailsButton}>
              <Text style={styles.viewDetailsButtonText}>Detayları Görüntüle</Text>
              <MaterialIcons name="chevron-right" size={20} color={Colors.PRIMARY} />
            </View>
          </TouchableOpacity>
        ) : (
          <View style={styles.noBudgetCard}>
            <MaterialIcons name="account-balance-wallet" size={48} color={Colors.GRAY_400} />
            <Text style={styles.noBudgetText}>Henüz aktif bir bütçeniz bulunmuyor.</Text>
            <TouchableOpacity
              style={styles.createBudgetButton}
              onPress={addNewBudget}
            >
              <Text style={styles.createBudgetButtonText}>Bütçe Oluştur</Text>
            </TouchableOpacity>
          </View>
        )}

        {/* Filtreler */}
        <View style={styles.filterContainer}>
          <TouchableOpacity
            style={[
              styles.filterButton,
              filter === 'all' && styles.filterButtonActive
            ]}
            onPress={() => setFilter('all')}
          >
            <Text style={[
              styles.filterButtonText,
              filter === 'all' && styles.filterButtonTextActive
            ]}>
              Tümü
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.filterButton,
              filter === 'active' && styles.filterButtonActive
            ]}
            onPress={() => setFilter('active')}
          >
            <Text style={[
              styles.filterButtonText,
              filter === 'active' && styles.filterButtonTextActive
            ]}>
              Aktif
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.filterButton,
              filter === 'completed' && styles.filterButtonActive
            ]}
            onPress={() => setFilter('completed')}
          >
            <Text style={[
              styles.filterButtonText,
              filter === 'completed' && styles.filterButtonTextActive
            ]}>
              Tamamlanan
            </Text>
          </TouchableOpacity>
        </View>

        {/* Bütçe Listesi */}
        <View style={styles.budgetListContainer}>
          <Text style={styles.sectionTitle}>Tüm Bütçeler</Text>

          {filteredBudgets.length === 0 ? (
            <View style={styles.emptyState}>
              <Text style={styles.emptyStateText}>
                {filter === 'all'
                  ? 'Henüz bütçe bulunmuyor.'
                  : filter === 'active'
                    ? 'Aktif bütçe bulunmuyor.'
                    : 'Tamamlanan bütçe bulunmuyor.'}
              </Text>
            </View>
          ) : (
            filteredBudgets.map((budget) => {
              const today = new Date().toISOString().split('T')[0];
              const isActive = budget.is_active === 1 &&
                              budget.start_date <= today &&
                              (!budget.end_date || budget.end_date >= today);

              return (
                <TouchableOpacity
                  key={budget.id}
                  style={styles.budgetItem}
                  onPress={() => viewBudgetDetails(budget.id)}
                >
                  <View style={styles.budgetItemHeader}>
                    <View style={styles.budgetItemInfo}>
                      <Text style={styles.budgetItemName}>{budget.name}</Text>
                      <View style={styles.budgetItemPeriod}>
                        <MaterialIcons name="date-range" size={14} color={Colors.GRAY_600} />
                        <Text style={styles.budgetItemPeriodText}>
                          {formatPeriod(budget.period)}
                        </Text>
                      </View>
                    </View>
                    <View style={styles.budgetItemActions}>
                      <TouchableOpacity
                        style={styles.budgetItemAction}
                        onPress={() => editBudget(budget)}
                      >
                        <MaterialIcons name="edit" size={20} color={Colors.PRIMARY} />
                      </TouchableOpacity>
                      <TouchableOpacity
                        style={styles.budgetItemAction}
                        onPress={() => confirmDeleteBudget(budget)}
                      >
                        <MaterialIcons name="delete" size={20} color={Colors.DANGER} />
                      </TouchableOpacity>
                    </View>
                  </View>

                  <View style={styles.budgetItemDates}>
                    <Text style={styles.budgetItemDateText}>
                      {new Date(budget.start_date).toLocaleDateString('tr-TR')}
                      {budget.end_date ? ` - ${new Date(budget.end_date).toLocaleDateString('tr-TR')}` : ''}
                    </Text>
                    {isActive && (
                      <View style={styles.activeBadge}>
                        <Text style={styles.activeBadgeText}>Aktif</Text>
                      </View>
                    )}
                  </View>

                  <View style={styles.budgetItemAmount}>
                    <Text style={styles.budgetItemAmountLabel}>Toplam:</Text>
                    <Text style={styles.budgetItemAmountValue}>
                      {formatCurrency(budget.amount, 'TRY')}
                    </Text>
                  </View>

                  <View style={styles.budgetItemCategories}>
                    <Text style={styles.budgetItemCategoriesLabel}>
                      {budget.category_count || 0} kategori
                    </Text>
                    <Text style={styles.budgetItemAllocated}>
                      {formatCurrency(budget.total_allocated || 0, 'TRY')} ayrıldı
                    </Text>
                  </View>
                </TouchableOpacity>
              );
            })
          )}
        </View>
      </ScrollView>

      {/* Hızlı Ekleme Butonu */}
      <TouchableOpacity
        style={styles.fab}
        onPress={addNewBudget}
      >
        <MaterialIcons name="add" size={24} color="#fff" />
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: Colors.PRIMARY,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  backButton: {
    padding: 8,
    marginRight: 8,
  },
  headerTitle: {
    flex: 1,
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
  },
  addButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  activeBudgetCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  activeBudgetHeader: {
    marginBottom: 12,
  },
  activeBudgetTitle: {
    fontSize: 14,
    color: Colors.PRIMARY,
    fontWeight: '500',
    marginBottom: 4,
  },
  activeBudgetName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  activeBudgetInfo: {
    marginBottom: 16,
  },
  activeBudgetPeriod: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  activeBudgetPeriodText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 4,
  },
  activeBudgetAmount: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  activeBudgetAmountLabel: {
    fontSize: 14,
    color: '#666',
  },
  activeBudgetAmountValue: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
  },
  progressContainer: {
    marginBottom: 16,
  },
  progressBar: {
    height: 8,
    backgroundColor: '#eee',
    borderRadius: 4,
    overflow: 'hidden',
    marginBottom: 4,
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  progressText: {
    fontSize: 12,
    color: '#666',
    textAlign: 'right',
  },
  viewDetailsButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 8,
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  viewDetailsButtonText: {
    fontSize: 14,
    color: Colors.PRIMARY,
    fontWeight: '500',
    marginRight: 4,
  },
  noBudgetCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 24,
    marginBottom: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  noBudgetText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginTop: 12,
    marginBottom: 16,
  },
  createBudgetButton: {
    backgroundColor: Colors.PRIMARY,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  createBudgetButtonText: {
    fontSize: 14,
    color: '#fff',
    fontWeight: '500',
  },
  filterContainer: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  filterButton: {
    flex: 1,
    paddingVertical: 8,
    alignItems: 'center',
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#ddd',
    marginRight: 8,
    borderRadius: 8,
  },
  filterButtonActive: {
    backgroundColor: Colors.PRIMARY,
    borderColor: Colors.PRIMARY,
  },
  filterButtonText: {
    fontSize: 14,
    color: '#666',
  },
  filterButtonTextActive: {
    color: '#fff',
    fontWeight: '500',
  },
  budgetListContainer: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
  },
  emptyState: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
  },
  emptyStateText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
  budgetItem: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  budgetItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  budgetItemInfo: {
    flex: 1,
  },
  budgetItemName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  budgetItemPeriod: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  budgetItemPeriodText: {
    fontSize: 12,
    color: '#666',
    marginLeft: 4,
  },
  budgetItemActions: {
    flexDirection: 'row',
  },
  budgetItemAction: {
    marginLeft: 12,
    padding: 4,
  },
  budgetItemDates: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  budgetItemDateText: {
    fontSize: 12,
    color: '#666',
  },
  activeBadge: {
    backgroundColor: Colors.SUCCESS_LIGHT,
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
    marginLeft: 8,
  },
  activeBadgeText: {
    fontSize: 10,
    color: Colors.SUCCESS,
    fontWeight: '500',
  },
  budgetItemAmount: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  budgetItemAmountLabel: {
    fontSize: 14,
    color: '#666',
  },
  budgetItemAmountValue: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
  },
  budgetItemCategories: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    borderTopWidth: 1,
    borderTopColor: '#eee',
    paddingTop: 8,
  },
  budgetItemCategoriesLabel: {
    fontSize: 12,
    color: '#666',
  },
  budgetItemAllocated: {
    fontSize: 12,
    color: '#666',
  },
  fab: {
    position: 'absolute',
    right: 16,
    bottom: 16,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: Colors.PRIMARY,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
});
