import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors } from '../constants/colors';

/**
 * Tüm özelliklerin listelendiği ekran
 * @param {Object} props Component props
 * @param {Object} props.navigation Navigation objesi
 */
const FeaturesScreen = ({ navigation }) => {
  const insets = useSafeAreaInsets();

  // Özellik listesi
  const features = [
    {
      id: 'investment',
      name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
      icon: 'trending-up',
      color: '#4CAF50',
      onPress: () => navigation.navigate('Investment')
    },
    {
      id: 'salary',
      name: '<PERSON><PERSON><PERSON>',
      icon: 'payments',
      color: '#E91E63',
      onPress: () => navigation.navigate('Salaries')
    },
    {
      id: 'work',
      name: '<PERSON><PERSON>',
      icon: 'schedule',
      color: '#2196F3',
      onPress: () => navigation.navigate('Overtime')
    },
    {
      id: 'savings',
      name: '<PERSON><PERSON><PERSON>',
      icon: 'savings',
      color: '#9C27B0',
      onPress: () => navigation.navigate('Savings')
    },
    {
      id: 'budget',
      name: 'Bütçe Planlama',
      icon: 'account-balance-wallet',
      color: '#FF9800',
      onPress: () => navigation.navigate('Budgets')
    },
    {
      id: 'reports',
      name: 'Raporlar',
      icon: 'bar-chart',
      color: '#795548',
      onPress: () => console.log('Raporlar')
    },
    {
      id: 'currency',
      name: 'Döviz Çevirici',
      icon: 'currency-exchange',
      color: '#607D8B',
      onPress: () => navigation.navigate('Currency')
    },
    {
      id: 'reminders',
      name: 'Hatırlatıcılar',
      icon: 'alarm',
      color: '#F44336',
      onPress: () => navigation.navigate('Reminders')
    },
    {
      id: 'notifications',
      name: 'Bildirimler',
      icon: 'notifications',
      color: '#9C27B0',
      onPress: () => navigation.navigate('Notifications')
    },
    {
      id: 'shopping',
      name: 'Alışveriş Listesi',
      icon: 'shopping-cart',
      color: '#00BCD4',
      onPress: () => console.log('Alışveriş Listesi')
    }
  ];

  return (
    <View style={[styles.container, { paddingTop: insets.top }]}>
      <View style={styles.header}>
        <Text style={styles.title}>Özellikler</Text>
      </View>

      <ScrollView style={styles.content}>
        <View style={styles.featuresGrid}>
          {features.map(feature => (
            <TouchableOpacity
              key={feature.id}
              style={styles.featureItem}
              onPress={feature.onPress}
            >
              <View style={[styles.iconContainer, { backgroundColor: feature.color }]}>
                <MaterialIcons name={feature.icon} size={28} color="#fff" />
              </View>
              <Text style={styles.featureName}>{feature.name}</Text>
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: Colors.PRIMARY,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  featuresGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  featureItem: {
    width: '48%',
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  iconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  featureName: {
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
    color: '#333',
  },
});

export default FeaturesScreen;
