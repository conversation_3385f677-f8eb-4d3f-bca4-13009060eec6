import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, RefreshControl, Alert, Modal } from 'react-native';
import { useSQLiteContext } from 'expo-sqlite';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { MaterialIcons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Colors } from '../constants/colors';
import { useAppContext } from '../context/AppContext';
import TransactionForm from '../components/TransactionForm';
import TransactionList from '../components/TransactionList';
import BalanceSummary from '../components/BalanceSummary';
import QuickTransactionInput from '../components/QuickTransactionInput';
import CustomizableWidget from '../components/CustomizableWidget';
import ComingSoonWidget from '../components/common/ComingSoonWidget';

// Design System imports
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>on,
  Card,
  CardHeader,
  CardBody,
  Typography,
  Title1,
  Title2,
  Headline,
  Body,
  Caption,
  useDesignSystem,
  responsive,
  flex,
  AnimationUtils
} from '../design-system';

/**
 * Ana sayfa ekranı - Modern Design System ile
 *
 * @returns {JSX.Element} Ana sayfa ekranı
 */
export default function HomeScreen({ navigation }) {
  const insets = useSafeAreaInsets();
  const db = useSQLiteContext();
  const { theme } = useAppContext();
  const ds = useDesignSystem(); // Design system hook

  const [transactions, setTransactions] = useState([]);
  const [isAddingTransaction, setIsAddingTransaction] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [balanceSummary, setBalanceSummary] = useState({
    income: 0,
    expense: 0,
    balance: 0
  });

  // Widget görünürlük ayarları
  const [widgetSettings, setWidgetSettings] = useState({
    showBalanceSummary: false,
    showRecentTransactions: false,
    showCustomWidget: false,
    showComingSoonWidget: true // Varsayılan olarak açık
  });

  // Widget ayarları modal
  const [showWidgetSettings, setShowWidgetSettings] = useState(false);

  // Widget ayarlarını yükle
  const loadWidgetSettings = async () => {
    try {
      const savedSettings = await AsyncStorage.getItem('homeWidgetSettings');
      if (savedSettings) {
        setWidgetSettings(JSON.parse(savedSettings));
      }
    } catch (error) {
      console.error('Widget ayarları yükleme hatası:', error);
    }
  };

  // Widget ayarlarını kaydet
  const saveWidgetSettings = async (newSettings) => {
    try {
      await AsyncStorage.setItem('homeWidgetSettings', JSON.stringify(newSettings));
      setWidgetSettings(newSettings);
    } catch (error) {
      console.error('Widget ayarları kaydetme hatası:', error);
    }
  };

  // Widget toggle fonksiyonu
  const toggleWidget = (widgetKey) => {
    const newSettings = {
      ...widgetSettings,
      [widgetKey]: !widgetSettings[widgetKey]
    };
    saveWidgetSettings(newSettings);
  };

  // Verileri yükle
  const loadData = async () => {
    try {
      // Son 5 işlemi al
      const recentTransactions = await db.getAllAsync(`
        SELECT t.*, c.name as category_name, c.color as category_color, c.icon as category_icon
        FROM transactions t
        LEFT JOIN categories c ON t.category_id = c.id
        ORDER BY t.date DESC, t.id DESC
        LIMIT 5
      `);

      setTransactions(recentTransactions);

      // Özet bilgileri al
      const incomeResult = await db.getFirstAsync(`
        SELECT SUM(amount) as total
        FROM transactions
        WHERE type = 'income'
      `);

      const expenseResult = await db.getFirstAsync(`
        SELECT SUM(amount) as total
        FROM transactions
        WHERE type = 'expense'
      `);

      const income = incomeResult?.total || 0;
      const expense = expenseResult?.total || 0;

      setBalanceSummary({
        income,
        expense,
        balance: income - expense
      });
    } catch (error) {
      console.error('Veri yükleme hatası:', error);
    } finally {
      setRefreshing(false);
    }
  };

  // İlk yükleme
  useEffect(() => {
    loadData();
    loadWidgetSettings();
  }, []);

  // Yenileme işlemi
  const onRefresh = () => {
    setRefreshing(true);
    loadData();
  };

  return (
    <View style={[styles.container, { paddingTop: insets.top, backgroundColor: theme.BACKGROUND }]}>
      {/* Modern Header */}
      <Card
        variant="elevated"
        padding="lg"
        margin="none"
        shadow="lg"
        style={[styles.modernHeader, { backgroundColor: ds.primaryColor }]}
      >
        <View style={[flex.row, flex.spaceBetween, flex.centerVertical]}>
          <View style={[flex.row, flex.centerVertical]}>
            <View style={styles.headerIconContainer}>
              <MaterialIcons name="account-balance-wallet" size={32} color="#fff" />
            </View>
            <View style={styles.headerTextContainer}>
              <Title2 color="#fff" weight="bold">
                Finansal Takip
              </Title2>
              <Caption color="#fff" style={{ opacity: 0.9 }}>
                Kişisel finans yöneticiniz
              </Caption>
            </View>
          </View>

          <View style={[flex.row, { gap: ds.spacing(2) }]}>
            <AnimatedButton
              variant="ghost"
              size="sm"
              icon="widgets"
              onPress={() => setShowWidgetSettings(true)}
              style={styles.headerButton}
              animationType="scale"
            />
            <AnimatedButton
              variant="ghost"
              size="sm"
              icon="add"
              onPress={() => setIsAddingTransaction(true)}
              style={styles.headerButton}
              animationType="scale"
            />
          </View>
        </View>
      </Card>

      <ScrollView
        style={[styles.content, { backgroundColor: theme.BACKGROUND }]}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Hızlı İşlem Ekleme - Her zaman görünür */}
        <QuickTransactionInput onTransactionAdded={loadData} />

        {/* Modern Empty Widget State */}
        {!widgetSettings.showBalanceSummary &&
         !widgetSettings.showRecentTransactions &&
         !widgetSettings.showCustomWidget &&
         !widgetSettings.showComingSoonWidget && (
          <Card
            variant="filled"
            padding="xl"
            margin="md"
            style={styles.emptyWidgetCard}
          >
            <View style={styles.emptyWidgetContent}>
              <View style={styles.emptyWidgetIcon}>
                <MaterialIcons name="widgets" size={48} color={ds.getColor('primary.400')} />
              </View>
              <Title2 align="center" style={{ marginBottom: ds.spacing(2) }}>
                Widget'ları Özelleştir
              </Title2>
              <Body align="center" color="gray.600" style={{ marginBottom: ds.spacing(6) }}>
                Sağ üstteki widget simgesine tıklayarak ana sayfanızı kişiselleştirin
              </Body>
              <AnimatedButton
                variant="primary"
                size="lg"
                icon="settings"
                onPress={() => setShowWidgetSettings(true)}
                style={styles.customizeButton}
                animationType="scale"
              >
                Widget'ları Düzenle
              </AnimatedButton>
            </View>
          </Card>
        )}

        {/* Koşullu Widget'lar */}
        {widgetSettings.showBalanceSummary && (
          <BalanceSummary
            income={balanceSummary.income}
            expense={balanceSummary.expense}
            balance={balanceSummary.balance}
          />
        )}

        {widgetSettings.showCustomWidget && (
          <CustomizableWidget />
        )}

        {widgetSettings.showComingSoonWidget && (
          <ComingSoonWidget
            onPress={() => navigation.navigate('ComingSoon')}
            showProgress={true}
            showStats={true}
            compact={false}
          />
        )}

        {widgetSettings.showRecentTransactions && (
          <Card variant="elevated" padding="lg" margin="md" shadow="md">
            <CardHeader style={styles.modernSectionHeader}>
              <Headline>Son İşlemler</Headline>
              <AnimatedButton
                variant="ghost"
                size="sm"
                onPress={() => navigation.navigate('Features', { screen: 'Transactions' })}
                animationType="scale"
              >
                Tümünü Gör
              </AnimatedButton>
            </CardHeader>

            <CardBody>
              <TransactionList
                transactions={transactions}
                onRefresh={loadData}
              />
            </CardBody>
          </Card>
        )}
      </ScrollView>

      {/* İşlem Ekleme Modal */}
      {isAddingTransaction && (
        <TransactionForm
          visible={isAddingTransaction}
          onClose={() => setIsAddingTransaction(false)}
          onSave={() => {
            setIsAddingTransaction(false);
            loadData();
          }}
        />
      )}

      {/* Widget Ayarları Modal */}
      <Modal
        visible={showWidgetSettings}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowWidgetSettings(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: theme.CARD }]}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: theme.TEXT_PRIMARY }]}>
                Ana Sayfa Widget'ları
              </Text>
              <TouchableOpacity
                onPress={() => setShowWidgetSettings(false)}
                style={styles.modalCloseButton}
              >
                <MaterialIcons name="close" size={24} color={theme.TEXT_SECONDARY} />
              </TouchableOpacity>
            </View>

            <View style={styles.modalBody}>
              <Text style={[styles.modalDescription, { color: theme.TEXT_SECONDARY }]}>
                Ana sayfada görmek istediğiniz widget'ları seçin:
              </Text>

              {/* Widget Seçenekleri */}
              <TouchableOpacity
                style={[styles.widgetOption, { borderColor: theme.BORDER }]}
                onPress={() => toggleWidget('showBalanceSummary')}
              >
                <View style={styles.widgetOptionLeft}>
                  <MaterialIcons
                    name="account-balance-wallet"
                    size={24}
                    color={theme.PRIMARY}
                  />
                  <View style={styles.widgetOptionText}>
                    <Text style={[styles.widgetOptionTitle, { color: theme.TEXT_PRIMARY }]}>
                      Bakiye Özeti
                    </Text>
                    <Text style={[styles.widgetOptionDesc, { color: theme.TEXT_SECONDARY }]}>
                      Toplam gelir, gider ve bakiye bilgileri
                    </Text>
                  </View>
                </View>
                <MaterialIcons
                  name={widgetSettings.showBalanceSummary ? "check-box" : "check-box-outline-blank"}
                  size={24}
                  color={widgetSettings.showBalanceSummary ? theme.PRIMARY : theme.TEXT_SECONDARY}
                />
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.widgetOption, { borderColor: theme.BORDER }]}
                onPress={() => toggleWidget('showRecentTransactions')}
              >
                <View style={styles.widgetOptionLeft}>
                  <MaterialIcons
                    name="receipt-long"
                    size={24}
                    color={theme.PRIMARY}
                  />
                  <View style={styles.widgetOptionText}>
                    <Text style={[styles.widgetOptionTitle, { color: theme.TEXT_PRIMARY }]}>
                      Son İşlemler
                    </Text>
                    <Text style={[styles.widgetOptionDesc, { color: theme.TEXT_SECONDARY }]}>
                      En son eklenen 5 işlem
                    </Text>
                  </View>
                </View>
                <MaterialIcons
                  name={widgetSettings.showRecentTransactions ? "check-box" : "check-box-outline-blank"}
                  size={24}
                  color={widgetSettings.showRecentTransactions ? theme.PRIMARY : theme.TEXT_SECONDARY}
                />
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.widgetOption, { borderColor: theme.BORDER }]}
                onPress={() => toggleWidget('showCustomWidget')}
              >
                <View style={styles.widgetOptionLeft}>
                  <MaterialIcons
                    name="dashboard"
                    size={24}
                    color={theme.PRIMARY}
                  />
                  <View style={styles.widgetOptionText}>
                    <Text style={[styles.widgetOptionTitle, { color: theme.TEXT_PRIMARY }]}>
                      Özel Widget
                    </Text>
                    <Text style={[styles.widgetOptionDesc, { color: theme.TEXT_SECONDARY }]}>
                      Özelleştirilebilir dashboard widget'ı
                    </Text>
                  </View>
                </View>
                <MaterialIcons
                  name={widgetSettings.showCustomWidget ? "check-box" : "check-box-outline-blank"}
                  size={24}
                  color={widgetSettings.showCustomWidget ? theme.PRIMARY : theme.TEXT_SECONDARY}
                />
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.widgetOption, { borderColor: theme.BORDER }]}
                onPress={() => toggleWidget('showComingSoonWidget')}
              >
                <View style={styles.widgetOptionLeft}>
                  <MaterialIcons
                    name="auto-awesome"
                    size={24}
                    color={theme.PRIMARY}
                  />
                  <View style={styles.widgetOptionText}>
                    <Text style={[styles.widgetOptionTitle, { color: theme.TEXT_PRIMARY }]}>
                      Yakında Gelecek Özellikler
                    </Text>
                    <Text style={[styles.widgetOptionDesc, { color: theme.TEXT_SECONDARY }]}>
                      Yeni özellikler ve geliştirme durumu
                    </Text>
                  </View>
                </View>
                <MaterialIcons
                  name={widgetSettings.showComingSoonWidget ? "check-box" : "check-box-outline-blank"}
                  size={24}
                  color={widgetSettings.showComingSoonWidget ? theme.PRIMARY : theme.TEXT_SECONDARY}
                />
              </TouchableOpacity>
            </View>

            <TouchableOpacity
              style={[styles.modalSaveButton, { backgroundColor: theme.PRIMARY }]}
              onPress={() => setShowWidgetSettings(false)}
            >
              <Text style={[styles.modalSaveButtonText, { color: theme.WHITE }]}>
                Tamam
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  modernHeader: {
    borderRadius: 0,
    marginBottom: 16,
  },
  headerIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(255,255,255,0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  headerTextContainer: {
    flex: 1,
  },
  headerButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    borderColor: 'rgba(255,255,255,0.3)',
  },
  emptyWidgetCard: {
    alignItems: 'center',
  },
  emptyWidgetContent: {
    alignItems: 'center',
    width: '100%',
  },
  emptyWidgetIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(14, 165, 233, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  modernSectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingBottom: 0,
    marginBottom: 0,
    borderBottomWidth: 0,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  headerButtons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  settingsButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginTop: 24,
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  seeAllText: {
    fontSize: 14,
  },
  // Boş widget container
  emptyWidgetContainer: {
    marginTop: 24,
    borderRadius: 12,
    padding: 32,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  emptyWidgetTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptyWidgetText: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 24,
  },
  customizeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
  },
  customizeButtonText: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
  },
  // Modal stilleri
  modalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  modalContent: {
    width: '90%',
    maxWidth: 400,
    borderRadius: 16,
    padding: 0,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  modalCloseButton: {
    padding: 4,
  },
  modalBody: {
    padding: 20,
  },
  modalDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 20,
  },
  widgetOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderWidth: 1,
    borderRadius: 12,
    marginBottom: 12,
  },
  widgetOptionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  widgetOptionText: {
    marginLeft: 12,
    flex: 1,
  },
  widgetOptionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  widgetOptionDesc: {
    fontSize: 12,
    lineHeight: 16,
  },
  modalSaveButton: {
    margin: 20,
    paddingVertical: 14,
    borderRadius: 8,
    alignItems: 'center',
  },
  modalSaveButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});
