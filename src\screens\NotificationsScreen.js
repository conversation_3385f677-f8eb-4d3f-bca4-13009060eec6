import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  ActivityIndicator,
  Alert
} from 'react-native';
import { useSQLiteContext } from 'expo-sqlite';
import { useFocusEffect } from '@react-navigation/native';
import { MaterialIcons } from '@expo/vector-icons';
import { format, parseISO } from 'date-fns';
import { tr } from 'date-fns/locale';
import { Colors } from '../constants/colors';
import * as notificationDbService from '../services/notificationDbService';

/**
 * Bildirimler Ekranı
 *
 * @param {Object} props - Bileşen özellikleri
 * @param {Object} props.navigation - Navigation nesnesi
 * @returns {JSX.Element} Bildirimler Ekranı
 */
export default function NotificationsScreen({ navigation }) {
  const db = useSQLiteContext();

  // Durum değişkenleri
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);

  // Verileri yükle
  const loadData = useCallback(async () => {
    try {
      setLoading(true);

      // Bildirimleri getir
      const notificationData = await notificationDbService.getNotifications(db, {
        limit: 50
      });

      setNotifications(notificationData);

      // Okunmamış bildirim sayısını hesapla
      const unread = notificationData.filter(n => n.status === 'pending' || n.status === 'sent').length;
      setUnreadCount(unread);

      setLoading(false);
    } catch (error) {
      console.error('Bildirimler yükleme hatası:', error);
      Alert.alert('Hata', 'Bildirimler yüklenirken bir hata oluştu.');
      setLoading(false);
    }
  }, [db]);

  // Ekran odaklandığında verileri yükle
  useFocusEffect(
    useCallback(() => {
      loadData();
    }, [loadData])
  );

  // Yenileme işlemi
  const handleRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  // Bildirimi okundu olarak işaretle
  const markAsRead = async (notification) => {
    try {
      await notificationDbService.markNotificationAsRead(db, notification.id);

      // Bildirimleri yenile
      loadData();
    } catch (error) {
      console.error('Bildirim okundu işaretleme hatası:', error);
      Alert.alert('Hata', 'Bildirim okundu işaretlenirken bir hata oluştu.');
    }
  };

  // Bildirimi sil
  const deleteNotification = async (notification) => {
    Alert.alert(
      'Bildirimi Sil',
      'Bu bildirimi silmek istediğinize emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: async () => {
            try {
              await notificationDbService.deleteNotification(db, notification.id);

              // Bildirimleri yenile
              loadData();
            } catch (error) {
              console.error('Bildirim silme hatası:', error);
              Alert.alert('Hata', 'Bildirim silinirken bir hata oluştu.');
            }
          }
        }
      ]
    );
  };

  // Tüm bildirimleri okundu olarak işaretle
  const markAllAsRead = async () => {
    try {
      // Okunmamış bildirimleri getir
      const unreadNotifications = notifications.filter(n => n.status === 'pending' || n.status === 'sent');

      if (unreadNotifications.length === 0) {
        Alert.alert('Bilgi', 'Okunmamış bildirim bulunmuyor.');
        return;
      }

      // Her bildirimi okundu olarak işaretle
      for (const notification of unreadNotifications) {
        await notificationDbService.markNotificationAsRead(db, notification.id);
      }

      // Bildirimleri yenile
      loadData();

      Alert.alert('Başarılı', 'Tüm bildirimler okundu olarak işaretlendi.');
    } catch (error) {
      console.error('Tüm bildirimleri okundu işaretleme hatası:', error);
      Alert.alert('Hata', 'Bildirimler okundu işaretlenirken bir hata oluştu.');
    }
  };

  // Bildirim tipi simgesini getir
  const getNotificationIcon = (type) => {
    switch (type) {
      case 'transaction':
        return 'receipt';
      case 'budget':
        return 'account-balance-wallet';
      case 'savings':
        return 'savings';
      case 'reminder':
        return 'notifications';
      case 'system':
        return 'info';
      default:
        return 'notifications';
    }
  };

  // Bildirim tipi rengini getir
  const getNotificationColor = (type, priority) => {
    if (priority === 'high') {
      return Colors.DANGER;
    }

    switch (type) {
      case 'transaction':
        return Colors.PRIMARY;
      case 'budget':
        return Colors.WARNING;
      case 'savings':
        return Colors.SUCCESS;
      case 'reminder':
        return Colors.INFO;
      case 'system':
        return Colors.GRAY_600;
      default:
        return Colors.PRIMARY;
    }
  };

  // Bildirim öğesi render fonksiyonu
  const renderNotificationItem = ({ item }) => {
    const isUnread = item.status === 'pending' || item.status === 'sent';
    const notificationColor = getNotificationColor(item.type, item.priority);
    const notificationIcon = getNotificationIcon(item.type);

    // Tarih formatla
    let formattedDate = '';

    try {
      const date = parseISO(item.scheduled_at);
      formattedDate = format(date, 'dd MMM yyyy, HH:mm', { locale: tr });
    } catch (error) {
      formattedDate = item.scheduled_at || '';
    }

    return (
      <TouchableOpacity
        style={[
          styles.notificationItem,
          isUnread && styles.unreadNotification
        ]}
        onPress={() => markAsRead(item)}
      >
        <View style={[styles.notificationIcon, { backgroundColor: notificationColor }]}>
          <MaterialIcons name={notificationIcon} size={20} color="#fff" />
        </View>

        <View style={styles.notificationContent}>
          <Text style={[
            styles.notificationTitle,
            isUnread && styles.unreadText
          ]}>
            {item.title}
          </Text>

          <Text style={styles.notificationMessage} numberOfLines={2}>
            {item.message}
          </Text>

          <Text style={styles.notificationDate}>
            {formattedDate}
          </Text>
        </View>

        <TouchableOpacity
          style={styles.deleteButton}
          onPress={() => deleteNotification(item)}
        >
          <MaterialIcons name="delete" size={20} color={Colors.GRAY_500} />
        </TouchableOpacity>
      </TouchableOpacity>
    );
  };

  // Boş durum render fonksiyonu
  const renderEmptyState = () => {
    if (loading) {
      return (
        <View style={styles.emptyContainer}>
          <ActivityIndicator size="large" color={Colors.PRIMARY} />
          <Text style={styles.emptyText}>Bildirimler yükleniyor...</Text>
        </View>
      );
    }

    return (
      <View style={styles.emptyContainer}>
        <MaterialIcons name="notifications-off" size={64} color={Colors.GRAY_400} />
        <Text style={styles.emptyText}>Henüz bildiriminiz bulunmuyor.</Text>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Bildirimler</Text>

        {unreadCount > 0 && (
          <TouchableOpacity
            style={styles.markAllReadButton}
            onPress={markAllAsRead}
          >
            <MaterialIcons name="done-all" size={20} color="#fff" />
            <Text style={styles.markAllReadText}>Tümünü Okundu İşaretle</Text>
          </TouchableOpacity>
        )}
      </View>

      <FlatList
        data={notifications}
        renderItem={renderNotificationItem}
        keyExtractor={item => item.id.toString()}
        contentContainerStyle={styles.listContent}
        ListEmptyComponent={renderEmptyState}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[Colors.PRIMARY]}
          />
        }
      />

      <TouchableOpacity
        style={styles.settingsButton}
        onPress={() => navigation.navigate('NotificationSettings')}
      >
        <MaterialIcons name="settings" size={20} color="#fff" />
        <Text style={styles.settingsButtonText}>Bildirim Ayarları</Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: Colors.PRIMARY,
    paddingVertical: 16,
    paddingHorizontal: 16,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  headerTitle: {
    flex: 1,
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
  },
  markAllReadButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  markAllReadText: {
    color: '#fff',
    fontSize: 12,
    marginLeft: 4,
  },
  listContent: {
    padding: 16,
    paddingBottom: 80,
  },
  notificationItem: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
  },
  unreadNotification: {
    backgroundColor: Colors.PRIMARY_LIGHT,
    borderLeftWidth: 4,
    borderLeftColor: Colors.PRIMARY,
  },
  notificationIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  notificationContent: {
    flex: 1,
  },
  notificationTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.GRAY_800,
    marginBottom: 4,
  },
  unreadText: {
    fontWeight: 'bold',
    color: Colors.GRAY_900,
  },
  notificationMessage: {
    fontSize: 14,
    color: Colors.GRAY_700,
    marginBottom: 8,
  },
  notificationDate: {
    fontSize: 12,
    color: Colors.GRAY_500,
  },
  deleteButton: {
    padding: 8,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 32,
  },
  emptyText: {
    fontSize: 16,
    color: Colors.GRAY_600,
    marginTop: 16,
    textAlign: 'center',
  },
  settingsButton: {
    position: 'absolute',
    bottom: 16,
    right: 16,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.PRIMARY,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 24,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  settingsButtonText: {
    color: '#fff',
    fontWeight: '500',
    marginLeft: 8,
  },
});
