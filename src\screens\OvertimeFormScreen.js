import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Switch,
  Alert,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform
} from 'react-native';
import { useSQLiteContext } from 'expo-sqlite';
import { useFocusEffect } from '@react-navigation/native';
import { MaterialIcons } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import { format } from 'date-fns';
import { tr } from 'date-fns/locale';
import { Colors } from '../constants/colors';
import * as overtimeService from '../services/overtimeService';

/**
 * Mesai Ekleme/Düzenleme Ekranı
 *
 * @param {Object} props - Bileşen özellikleri
 * @param {Object} props.navigation - Navigation nesnesi
 * @param {Object} props.route - Route nesnesi
 * @returns {JSX.Element} Mesai Form Ekranı
 */
export default function OvertimeFormScreen({ navigation, route }) {
  const db = useSQLiteContext();
  const { overtimeId, dateString } = route.params || {};
  const isEditing = !!overtimeId;

  // Durum değişkenleri
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [categories, setCategories] = useState([]);
  const [showCategoryModal, setShowCategoryModal] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showStartTimePicker, setShowStartTimePicker] = useState(false);
  const [showEndTimePicker, setShowEndTimePicker] = useState(false);
  const [showPaymentDatePicker, setShowPaymentDatePicker] = useState(false);
  const [colorOptions] = useState([
    '#4CAF50', '#2196F3', '#9C27B0', '#F44336', '#FF9800',
    '#795548', '#607D8B', '#E91E63', '#00BCD4', '#CDDC39'
  ]);

  // Form verileri
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    date: dateString ? new Date(dateString) : new Date(),
    start_time: '09:00',
    end_time: '18:00',
    hourly_rate: '0',
    currency: 'TRY',
    is_paid: false,
    payment_date: null,
    category_id: null,
    color: '#4CAF50',
    notes: ''
  });

  // Verileri yükle
  const loadData = useCallback(async () => {
    try {
      setLoading(true);

      // Kategorileri getir
      const incomeCategories = await db.getAllAsync(`
        SELECT * FROM categories
        WHERE type IN ('income', 'both')
        ORDER BY name
      `);
      setCategories(incomeCategories);

      // Düzenleme modunda ise mevcut mesai verilerini getir
      if (isEditing) {
        const overtimeDetails = await overtimeService.getOvertimeDetails(db, overtimeId);

        // Form verilerini doldur
        setFormData({
          title: overtimeDetails.title,
          description: overtimeDetails.description || '',
          date: new Date(overtimeDetails.date),
          start_time: overtimeDetails.start_time,
          end_time: overtimeDetails.end_time,
          hourly_rate: overtimeDetails.hourly_rate.toString(),
          currency: overtimeDetails.currency || 'TRY',
          is_paid: overtimeDetails.is_paid === 1,
          payment_date: overtimeDetails.payment_date ? new Date(overtimeDetails.payment_date) : null,
          category_id: overtimeDetails.category_id,
          color: overtimeDetails.color || '#4CAF50',
          notes: overtimeDetails.notes || ''
        });
      }

      setLoading(false);
    } catch (error) {
      console.error('Veri yükleme hatası:', error);
      Alert.alert('Hata', 'Veriler yüklenirken bir hata oluştu.');
      setLoading(false);
    }
  }, [isEditing, overtimeId, db, dateString]);

  // Ekran odaklandığında verileri yükle
  useFocusEffect(
    useCallback(() => {
      loadData();
    }, [loadData])
  );

  // Form alanını güncelle
  const updateFormField = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  // Kategori seç
  const selectCategory = (category) => {
    updateFormField('category_id', category.id);
    setShowCategoryModal(false);
  };

  // Renk seç
  const selectColor = (color) => {
    updateFormField('color', color);
  };

  // Seçili kategoriyi bul
  const getSelectedCategory = () => {
    return categories.find(c => c.id === formData.category_id) || null;
  };

  // Tarih değiştiğinde
  const handleDateChange = (event, selectedDate) => {
    setShowDatePicker(false);
    if (selectedDate) {
      updateFormField('date', selectedDate);
    }
  };

  // Başlangıç saati değiştiğinde
  const handleStartTimeChange = (event, selectedTime) => {
    setShowStartTimePicker(false);
    if (selectedTime) {
      const formattedTime = format(selectedTime, 'HH:mm');
      updateFormField('start_time', formattedTime);
    }
  };

  // Bitiş saati değiştiğinde
  const handleEndTimeChange = (event, selectedTime) => {
    setShowEndTimePicker(false);
    if (selectedTime) {
      const formattedTime = format(selectedTime, 'HH:mm');
      updateFormField('end_time', formattedTime);
    }
  };

  // Ödeme tarihi değiştiğinde
  const handlePaymentDateChange = (event, selectedDate) => {
    setShowPaymentDatePicker(false);
    if (selectedDate) {
      updateFormField('payment_date', selectedDate);
    }
  };

  // Formu doğrula
  const validateForm = () => {
    if (!formData.title.trim()) {
      Alert.alert('Hata', 'Lütfen mesai başlığını girin.');
      return false;
    }

    if (!formData.date) {
      Alert.alert('Hata', 'Lütfen tarih seçin.');
      return false;
    }

    if (!formData.start_time || !formData.end_time) {
      Alert.alert('Hata', 'Lütfen başlangıç ve bitiş saatlerini girin.');
      return false;
    }

    // Başlangıç saati bitiş saatinden önce olmalı
    const [startHour, startMinute] = formData.start_time.split(':').map(Number);
    const [endHour, endMinute] = formData.end_time.split(':').map(Number);

    if (startHour > endHour || (startHour === endHour && startMinute >= endMinute)) {
      Alert.alert('Hata', 'Başlangıç saati bitiş saatinden önce olmalıdır.');
      return false;
    }

    if (isNaN(parseFloat(formData.hourly_rate)) || parseFloat(formData.hourly_rate) < 0) {
      Alert.alert('Hata', 'Lütfen geçerli bir saatlik ücret girin.');
      return false;
    }

    return true;
  };

  // Mesai kaydet
  const saveOvertime = async () => {
    if (!validateForm()) return;

    try {
      setSaving(true);

      const overtimeData = {
        ...formData,
        date: format(formData.date, 'yyyy-MM-dd'),
        hourly_rate: parseFloat(formData.hourly_rate),
        is_paid: formData.is_paid ? 1 : 0,
        payment_date: formData.payment_date ? format(formData.payment_date, 'yyyy-MM-dd') : null
      };

      if (isEditing) {
        // Mevcut mesai kaydını güncelle
        await overtimeService.updateOvertime(db, overtimeId, overtimeData);
        Alert.alert('Başarılı', 'Mesai kaydı başarıyla güncellendi.');
      } else {
        // Yeni mesai kaydı oluştur
        await overtimeService.createOvertime(db, overtimeData);
        Alert.alert('Başarılı', 'Mesai kaydı başarıyla oluşturuldu.');
      }

      setSaving(false);
      navigation.goBack();
    } catch (error) {
      console.error('Mesai kaydetme hatası:', error);
      Alert.alert('Hata', 'Mesai kaydedilirken bir hata oluştu.');
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <ActivityIndicator size="large" color={Colors.PRIMARY} />
        <Text style={styles.loadingText}>Yükleniyor...</Text>
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
    >
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <MaterialIcons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>
          {isEditing ? 'Mesai Düzenle' : 'Yeni Mesai Ekle'}
        </Text>
        <TouchableOpacity
          style={styles.saveButton}
          onPress={saveOvertime}
          disabled={saving}
        >
          {saving ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : (
            <MaterialIcons name="check" size={24} color="#fff" />
          )}
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content}>
        <View style={styles.formSection}>
          <Text style={styles.sectionTitle}>Mesai Bilgileri</Text>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Başlık</Text>
            <TextInput
              style={styles.input}
              value={formData.title}
              onChangeText={(text) => updateFormField('title', text)}
              placeholder="Örn: Proje Teslimi"
              placeholderTextColor="#999"
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Açıklama (Opsiyonel)</Text>
            <TextInput
              style={styles.input}
              value={formData.description}
              onChangeText={(text) => updateFormField('description', text)}
              placeholder="Mesai hakkında kısa açıklama"
              placeholderTextColor="#999"
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Tarih</Text>
            <TouchableOpacity
              style={styles.dateInput}
              onPress={() => setShowDatePicker(true)}
            >
              <Text style={styles.dateText}>
                {format(formData.date, 'dd MMMM yyyy, EEEE', { locale: tr })}
              </Text>
              <MaterialIcons name="calendar-today" size={20} color={Colors.GRAY_600} />
            </TouchableOpacity>

            {showDatePicker && (
              <DateTimePicker
                value={formData.date}
                mode="date"
                display="default"
                onChange={handleDateChange}
              />
            )}
          </View>

          <View style={styles.timeContainer}>
            <View style={styles.timeGroup}>
              <Text style={styles.label}>Başlangıç Saati</Text>
              <TouchableOpacity
                style={styles.timeInput}
                onPress={() => setShowStartTimePicker(true)}
              >
                <Text style={styles.timeText}>{formData.start_time}</Text>
                <MaterialIcons name="access-time" size={20} color={Colors.GRAY_600} />
              </TouchableOpacity>

              {showStartTimePicker && (
                <DateTimePicker
                  value={new Date(`2023-01-01T${formData.start_time}:00`)}
                  mode="time"
                  display="default"
                  onChange={handleStartTimeChange}
                />
              )}
            </View>

            <View style={styles.timeGroup}>
              <Text style={styles.label}>Bitiş Saati</Text>
              <TouchableOpacity
                style={styles.timeInput}
                onPress={() => setShowEndTimePicker(true)}
              >
                <Text style={styles.timeText}>{formData.end_time}</Text>
                <MaterialIcons name="access-time" size={20} color={Colors.GRAY_600} />
              </TouchableOpacity>

              {showEndTimePicker && (
                <DateTimePicker
                  value={new Date(`2023-01-01T${formData.end_time}:00`)}
                  mode="time"
                  display="default"
                  onChange={handleEndTimeChange}
                />
              )}
            </View>
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Toplam Süre</Text>
            <View style={styles.durationDisplay}>
              <MaterialIcons name="timer" size={20} color={Colors.PRIMARY} />
              <Text style={styles.durationText}>
                {overtimeService.formatDuration(
                  overtimeService.calculateDuration(formData.start_time, formData.end_time)
                )}
              </Text>
            </View>
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Saatlik Ücret</Text>
            <View style={styles.amountContainer}>
              <TextInput
                style={styles.amountInput}
                value={formData.hourly_rate}
                onChangeText={(text) => updateFormField('hourly_rate', text)}
                placeholder="0.00"
                placeholderTextColor="#999"
                keyboardType="numeric"
              />
              <View style={styles.currencySelector}>
                <Text style={styles.currencyText}>{formData.currency}</Text>
              </View>
            </View>
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Toplam Kazanç</Text>
            <View style={styles.totalEarningDisplay}>
              <MaterialIcons name="attach-money" size={20} color={Colors.SUCCESS} />
              <Text style={styles.totalEarningText}>
                {new Intl.NumberFormat('tr-TR', {
                  style: 'currency',
                  currency: formData.currency
                }).format(
                  parseFloat(formData.hourly_rate) *
                  overtimeService.calculateDuration(formData.start_time, formData.end_time)
                )}
              </Text>
            </View>
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Kategori</Text>
            <TouchableOpacity
              style={styles.selectButton}
              onPress={() => setShowCategoryModal(true)}
            >
              {getSelectedCategory() ? (
                <View style={styles.selectedItem}>
                  <MaterialIcons
                    name={getSelectedCategory().icon || "category"}
                    size={20}
                    color={getSelectedCategory().color || Colors.PRIMARY}
                  />
                  <Text style={styles.selectedItemText}>{getSelectedCategory().name}</Text>
                </View>
              ) : (
                <Text style={styles.selectButtonText}>Kategori Seçin (Opsiyonel)</Text>
              )}
              <MaterialIcons name="chevron-right" size={20} color={Colors.GRAY_500} />
            </TouchableOpacity>
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Renk</Text>
            <View style={styles.colorOptions}>
              {colorOptions.map(color => (
                <TouchableOpacity
                  key={color}
                  style={[
                    styles.colorOption,
                    { backgroundColor: color },
                    formData.color === color && styles.selectedColorOption
                  ]}
                  onPress={() => selectColor(color)}
                >
                  {formData.color === color && (
                    <MaterialIcons name="check" size={16} color="#fff" />
                  )}
                </TouchableOpacity>
              ))}
            </View>
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Notlar (Opsiyonel)</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={formData.notes}
              onChangeText={(text) => updateFormField('notes', text)}
              placeholder="Mesai ile ilgili notlar..."
              placeholderTextColor="#999"
              multiline
              numberOfLines={4}
              textAlignVertical="top"
            />
          </View>

          <View style={styles.switchContainer}>
            <Text style={styles.switchLabel}>Ödendi</Text>
            <Switch
              value={formData.is_paid}
              onValueChange={(value) => {
                updateFormField('is_paid', value);
                if (value && !formData.payment_date) {
                  updateFormField('payment_date', new Date());
                }
              }}
              trackColor={{ false: Colors.GRAY_300, true: Colors.PRIMARY_LIGHT }}
              thumbColor={formData.is_paid ? Colors.PRIMARY : Colors.GRAY_500}
            />
          </View>

          {formData.is_paid && (
            <View style={styles.formGroup}>
              <Text style={styles.label}>Ödeme Tarihi</Text>
              <TouchableOpacity
                style={styles.dateInput}
                onPress={() => setShowPaymentDatePicker(true)}
              >
                <Text style={styles.dateText}>
                  {formData.payment_date
                    ? format(formData.payment_date, 'dd MMMM yyyy', { locale: tr })
                    : 'Tarih seçin'}
                </Text>
                <MaterialIcons name="calendar-today" size={20} color={Colors.GRAY_600} />
              </TouchableOpacity>

              {showPaymentDatePicker && (
                <DateTimePicker
                  value={formData.payment_date || new Date()}
                  mode="date"
                  display="default"
                  onChange={handlePaymentDateChange}
                />
              )}
            </View>
          )}
        </View>
      </ScrollView>

      {/* Kategori Seçim Modalı */}
      {showCategoryModal && (
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Kategori Seçin</Text>
              <TouchableOpacity
                style={styles.modalCloseButton}
                onPress={() => setShowCategoryModal(false)}
              >
                <MaterialIcons name="close" size={24} color={Colors.GRAY_600} />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalBody}>
              <TouchableOpacity
                style={styles.modalItem}
                onPress={() => selectCategory({ id: null, name: 'Kategorisiz' })}
              >
                <View style={styles.modalItemInfo}>
                  <MaterialIcons
                    name="category"
                    size={20}
                    color={Colors.GRAY_500}
                  />
                  <Text style={styles.modalItemName}>Kategorisiz</Text>
                </View>

                {formData.category_id === null && (
                  <MaterialIcons name="check" size={20} color={Colors.SUCCESS} />
                )}
              </TouchableOpacity>

              {categories.map((category) => (
                <TouchableOpacity
                  key={category.id}
                  style={styles.modalItem}
                  onPress={() => selectCategory(category)}
                >
                  <View style={styles.modalItemInfo}>
                    <MaterialIcons
                      name={category.icon || "category"}
                      size={20}
                      color={category.color || Colors.PRIMARY}
                    />
                    <Text style={styles.modalItemName}>{category.name}</Text>
                  </View>

                  {formData.category_id === category.id && (
                    <MaterialIcons name="check" size={20} color={Colors.SUCCESS} />
                  )}
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </View>
      )}
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa'
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center'
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: Colors.GRAY_600
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: Colors.PRIMARY,
    paddingVertical: 16,
    paddingHorizontal: 16,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2
  },
  backButton: {
    padding: 8
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff'
  },
  saveButton: {
    padding: 8
  },
  content: {
    flex: 1,
    padding: 16
  },
  formSection: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.GRAY_800,
    marginBottom: 16
  },
  formGroup: {
    marginBottom: 16
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.GRAY_700,
    marginBottom: 8
  },
  input: {
    backgroundColor: Colors.GRAY_100,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    color: Colors.GRAY_800
  },
  textArea: {
    minHeight: 100
  },
  dateInput: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: Colors.GRAY_100,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12
  },
  dateText: {
    fontSize: 16,
    color: Colors.GRAY_800
  },
  timeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16
  },
  timeGroup: {
    width: '48%'
  },
  timeInput: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: Colors.GRAY_100,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12
  },
  timeText: {
    fontSize: 16,
    color: Colors.GRAY_800
  },
  durationDisplay: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.GRAY_100,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12
  },
  durationText: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.PRIMARY,
    marginLeft: 8
  },
  amountContainer: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  amountInput: {
    flex: 1,
    backgroundColor: Colors.GRAY_100,
    borderTopLeftRadius: 8,
    borderBottomLeftRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    color: Colors.GRAY_800
  },
  currencySelector: {
    backgroundColor: Colors.GRAY_200,
    borderTopRightRadius: 8,
    borderBottomRightRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    justifyContent: 'center'
  },
  currencyText: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.GRAY_700
  },
  totalEarningDisplay: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.GRAY_100,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12
  },
  totalEarningText: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.SUCCESS,
    marginLeft: 8
  },
  selectButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: Colors.GRAY_100,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12
  },
  selectButtonText: {
    fontSize: 16,
    color: Colors.GRAY_500
  },
  selectedItem: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  selectedItemText: {
    fontSize: 16,
    color: Colors.GRAY_800,
    marginLeft: 8
  },
  colorOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap'
  },
  colorOption: {
    width: 36,
    height: 36,
    borderRadius: 18,
    marginRight: 12,
    marginBottom: 12,
    justifyContent: 'center',
    alignItems: 'center'
  },
  selectedColorOption: {
    borderWidth: 2,
    borderColor: '#fff',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2
  },
  switchContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16
  },
  switchLabel: {
    fontSize: 16,
    color: Colors.GRAY_700
  },
  modalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000
  },
  modalContent: {
    width: '90%',
    maxHeight: '80%',
    backgroundColor: '#fff',
    borderRadius: 12,
    overflow: 'hidden'
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.GRAY_200
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.GRAY_800
  },
  modalCloseButton: {
    padding: 4
  },
  modalBody: {
    padding: 16,
    maxHeight: 400
  },
  modalItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderBottomWidth: 1,
    borderBottomColor: Colors.GRAY_100
  },
  modalItemInfo: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  modalItemName: {
    fontSize: 16,
    color: Colors.GRAY_800,
    marginLeft: 8
  }
});
