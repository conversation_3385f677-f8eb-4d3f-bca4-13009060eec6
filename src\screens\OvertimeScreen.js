import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  ActivityIndicator,
  RefreshControl
} from 'react-native';
import { useSQLiteContext } from 'expo-sqlite';
import { useFocusEffect } from '@react-navigation/native';
import { MaterialIcons } from '@expo/vector-icons';
import { format, isSameDay } from 'date-fns';
import { tr } from 'date-fns/locale';
import { Colors } from '../constants/colors';
import * as overtimeService from '../services/overtimeService';
import CalendarView from '../components/calendar/CalendarView';
import DayEvents from '../components/calendar/DayEvents';

/**
 * <PERSON>i Ta<PERSON> E<PERSON>nı
 *
 * @param {Object} props - Bileşen özellikleri
 * @param {Object} props.navigation - Navigation nesnesi
 * @returns {JSX.Element} Mesai Takibi Ekranı
 */
export default function OvertimeScreen({ navigation }) {
  const db = useSQLiteContext();

  // Durum değişkenleri
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [overtimeRecords, setOvertimeRecords] = useState([]);
  const [selectedDateEvents, setSelectedDateEvents] = useState([]);
  const [monthlyStats, setMonthlyStats] = useState(null);

  // Verileri yükle
  const loadData = useCallback(async () => {
    try {
      setLoading(true);

      // Tüm mesai kayıtlarını getir
      const records = await overtimeService.getOvertimeRecords(db);
      setOvertimeRecords(records);

      // Seçili tarihteki mesai kayıtlarını getir
      const dateString = format(selectedDate, 'yyyy-MM-dd');
      const dateEvents = records.filter(record => record.date === dateString);
      setSelectedDateEvents(dateEvents);

      // Aylık istatistikleri getir
      const year = format(selectedDate, 'yyyy');
      const month = format(selectedDate, 'MM');
      const stats = await overtimeService.getMonthlyOvertimeStats(db, year, month);
      setMonthlyStats(stats);

      setLoading(false);
    } catch (error) {
      console.error('Mesai verileri yükleme hatası:', error);
      Alert.alert('Hata', 'Mesai verileri yüklenirken bir hata oluştu.');
      setLoading(false);
    }
  }, [db, selectedDate]);

  // Ekran odaklandığında verileri yükle
  useFocusEffect(
    useCallback(() => {
      loadData();
    }, [loadData])
  );

  // Yenileme işlemi
  const handleRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  // Tarih seçildiğinde
  const handleDateSelect = (date) => {
    setSelectedDate(date);

    // Seçili tarihteki mesai kayıtlarını getir
    const dateString = format(date, 'yyyy-MM-dd');
    const dateEvents = overtimeRecords.filter(record => record.date === dateString);
    setSelectedDateEvents(dateEvents);
  };

  // Yeni mesai ekle
  const addNewOvertime = (date) => {
    // Tarihi string formatına dönüştür (serileştirilebilir olması için)
    const dateString = format(date, 'yyyy-MM-dd');
    navigation.navigate('OvertimeForm', { dateString });
  };

  // Mesai detaylarını görüntüle
  const viewOvertimeDetails = (overtime) => {
    navigation.navigate('OvertimeDetail', { overtimeId: overtime.id });
  };

  // Takvim günü render fonksiyonu
  const renderCalendarDay = ({ date, dateString, isSelected, isToday, events, onSelect }) => {
    const hasEvents = events.length > 0;
    const hasPaidEvents = events.some(event => event.is_paid === 1);
    const hasUnpaidEvents = events.some(event => event.is_paid === 0);

    return (
      <TouchableOpacity
        style={[
          styles.calendarDay,
          isSelected && styles.selectedDay,
          isToday && styles.today
        ]}
        onPress={onSelect}
      >
        <Text style={[
          styles.calendarDayText,
          isSelected && styles.selectedDayText,
          isToday && styles.todayText
        ]}>
          {format(date, 'd')}
        </Text>

        {hasEvents && (
          <View style={styles.eventIndicators}>
            {hasPaidEvents && (
              <View style={[styles.eventIndicator, styles.paidEventIndicator]} />
            )}
            {hasUnpaidEvents && (
              <View style={[styles.eventIndicator, styles.unpaidEventIndicator]} />
            )}
          </View>
        )}
      </TouchableOpacity>
    );
  };

  if (loading) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <ActivityIndicator size="large" color={Colors.PRIMARY} />
        <Text style={styles.loadingText}>Mesai verileri yükleniyor...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Mesai Takibi</Text>
        <TouchableOpacity
          style={styles.headerAction}
          onPress={() => addNewOvertime(selectedDate)}
        >
          <MaterialIcons name="add" size={24} color="#fff" />
        </TouchableOpacity>
      </View>

      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[Colors.PRIMARY]}
          />
        }
      >
        {/* Aylık Özet */}
        {monthlyStats && (
          <View style={styles.monthlyStatsCard}>
            <Text style={styles.monthlyStatsTitle}>
              {format(selectedDate, 'MMMM yyyy', { locale: tr })} Özeti
            </Text>

            <View style={styles.statsRow}>
              <View style={styles.statItem}>
                <Text style={styles.statValue}>{overtimeService.formatDuration(monthlyStats.total_hours)}</Text>
                <Text style={styles.statLabel}>Toplam Süre</Text>
              </View>

              <View style={styles.statItem}>
                <Text style={styles.statValue}>
                  {new Intl.NumberFormat('tr-TR', {
                    style: 'currency',
                    currency: monthlyStats.currency
                  }).format(monthlyStats.total_amount)}
                </Text>
                <Text style={styles.statLabel}>Toplam Kazanç</Text>
              </View>
            </View>

            <View style={styles.statsRow}>
              <View style={styles.statItem}>
                <Text style={styles.statValue}>{monthlyStats.total_count}</Text>
                <Text style={styles.statLabel}>Toplam Mesai</Text>
              </View>

              <View style={styles.statItem}>
                <Text style={styles.statValue}>{monthlyStats.paid_count}</Text>
                <Text style={styles.statLabel}>Ödenen</Text>
              </View>

              <View style={styles.statItem}>
                <Text style={styles.statValue}>{monthlyStats.unpaid_count}</Text>
                <Text style={styles.statLabel}>Ödenmemiş</Text>
              </View>
            </View>
          </View>
        )}

        {/* Takvim */}
        <CalendarView
          selectedDate={selectedDate}
          onDateSelect={handleDateSelect}
          events={overtimeRecords}
          renderDay={renderCalendarDay}
        />

        {/* Seçili Gün Etkinlikleri */}
        <DayEvents
          date={selectedDate}
          events={selectedDateEvents}
          onEventPress={viewOvertimeDetails}
          onAddPress={addNewOvertime}
        />
      </ScrollView>

      {/* Hızlı Ekleme Butonu */}
      <TouchableOpacity
        style={styles.fab}
        onPress={() => addNewOvertime(selectedDate)}
      >
        <MaterialIcons name="add" size={24} color="#fff" />
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: Colors.GRAY_600,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: Colors.PRIMARY,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
  },
  headerAction: {
    padding: 8,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  monthlyStatsCard: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
  },
  monthlyStatsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.GRAY_800,
    marginBottom: 16,
    textTransform: 'capitalize',
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.PRIMARY,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: Colors.GRAY_600,
  },
  calendarDay: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 20,
  },
  selectedDay: {
    backgroundColor: Colors.PRIMARY,
  },
  today: {
    borderWidth: 1,
    borderColor: Colors.PRIMARY,
  },
  calendarDayText: {
    fontSize: 14,
    color: Colors.GRAY_800,
  },
  selectedDayText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  todayText: {
    fontWeight: 'bold',
    color: Colors.PRIMARY,
  },
  eventIndicators: {
    flexDirection: 'row',
    position: 'absolute',
    bottom: 4,
  },
  eventIndicator: {
    width: 6,
    height: 6,
    borderRadius: 3,
    marginHorizontal: 1,
  },
  paidEventIndicator: {
    backgroundColor: Colors.SUCCESS,
  },
  unpaidEventIndicator: {
    backgroundColor: Colors.WARNING,
  },
  fab: {
    position: 'absolute',
    right: 16,
    bottom: 16,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: Colors.PRIMARY,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
});
