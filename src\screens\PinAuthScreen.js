import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Vibration,
  Alert
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors } from '../constants/colors';
import { useAppContext } from '../context/AppContext';
import { useAuth } from '../context/AuthContext';

/**
 * PIN Kimlik Doğrulama Ekranı
 * 4 haneli PIN ile giriş
 */
export default function PinAuthScreen({ navigation, route }) {
  const insets = useSafeAreaInsets();
  const { theme } = useAppContext();
  const { verifyPin, authenticate, authenticateWithBiometric, authSettings } = useAuth();

  const [pin, setPin] = useState('');
  const [attempts, setAttempts] = useState(0);
  const [isLoading, setIsLoading] = useState(false);

  const mode = route?.params?.mode || 'auth'; // 'auth', 'setup', 'change'
  const [confirmPin, setConfirmPin] = useState('');
  const [step, setStep] = useState(mode === 'setup' ? 'enter' : 'auth'); // 'enter', 'confirm', 'auth'

  const maxAttempts = 5;

  // Biometrik giriş dene
  useEffect(() => {
    if (mode === 'auth' && authSettings.biometricEnabled) {
      tryBiometricAuth();
    }
  }, []);

  const tryBiometricAuth = async () => {
    try {
      const result = await authenticateWithBiometric();
      if (result) {
        await authenticate();
      }
    } catch (error) {
      console.error('Biometrik giriş hatası:', error);
    }
  };

  // PIN girişi
  const handleNumberPress = (number) => {
    if (pin.length < 4) {
      const newPin = pin + number;
      setPin(newPin);

      // 4 hane tamamlandığında işlem yap
      if (newPin.length === 4) {
        setTimeout(() => {
          handlePinComplete(newPin);
        }, 100);
      }
    }
  };

  // PIN silme
  const handleBackspace = () => {
    setPin(pin.slice(0, -1));
  };

  // PIN tamamlandığında
  const handlePinComplete = async (completedPin) => {
    setIsLoading(true);

    try {
      if (mode === 'auth') {
        // Kimlik doğrulama modu
        const isValid = await verifyPin(completedPin);
        
        if (isValid) {
          await authenticate();
          Vibration.vibrate(50); // Başarı titreşimi
        } else {
          const newAttempts = attempts + 1;
          setAttempts(newAttempts);
          
          Vibration.vibrate([100, 50, 100]); // Hata titreşimi
          
          if (newAttempts >= maxAttempts) {
            Alert.alert(
              'Çok Fazla Hatalı Deneme',
              'Uygulamadan çıkılıyor...',
              [{ text: 'Tamam', onPress: () => navigation.goBack() }]
            );
          } else {
            Alert.alert(
              'Hatalı PIN',
              `Kalan deneme hakkı: ${maxAttempts - newAttempts}`,
              [{ text: 'Tamam' }]
            );
          }
          
          setPin('');
        }
      } else if (mode === 'setup') {
        // PIN kurulum modu
        if (step === 'enter') {
          setConfirmPin(completedPin);
          setPin('');
          setStep('confirm');
        } else if (step === 'confirm') {
          if (completedPin === confirmPin) {
            // PIN'ler eşleşiyor, kaydet
            const result = await setPin(completedPin);
            if (result.success) {
              Alert.alert(
                'Başarılı',
                'PIN başarıyla ayarlandı',
                [{ text: 'Tamam', onPress: () => navigation.goBack() }]
              );
            } else {
              Alert.alert('Hata', result.error);
              setPin('');
              setStep('enter');
              setConfirmPin('');
            }
          } else {
            Alert.alert(
              'PIN Eşleşmiyor',
              'Lütfen aynı PIN\'i tekrar girin',
              [{ text: 'Tamam' }]
            );
            setPin('');
            setStep('enter');
            setConfirmPin('');
          }
        }
      }
    } catch (error) {
      console.error('PIN işlemi hatası:', error);
      Alert.alert('Hata', 'Bir hata oluştu');
      setPin('');
    } finally {
      setIsLoading(false);
    }
  };

  // Başlık metni
  const getTitle = () => {
    if (mode === 'setup') {
      return step === 'enter' ? 'PIN Oluşturun' : 'PIN\'i Onaylayın';
    }
    return 'PIN Girin';
  };

  // Alt başlık metni
  const getSubtitle = () => {
    if (mode === 'setup') {
      return step === 'enter' ? '4 haneli PIN oluşturun' : 'PIN\'i tekrar girin';
    }
    return 'Uygulamaya erişmek için PIN\'inizi girin';
  };

  // Numara tuşları
  const renderNumberPad = () => {
    const numbers = [
      [1, 2, 3],
      [4, 5, 6],
      [7, 8, 9],
      ['biometric', 0, 'backspace']
    ];

    return (
      <View style={styles.numberPad}>
        {numbers.map((row, rowIndex) => (
          <View key={rowIndex} style={styles.numberRow}>
            {row.map((item, itemIndex) => {
              if (item === 'biometric') {
                return (
                  <TouchableOpacity
                    key={itemIndex}
                    style={[styles.numberButton, { backgroundColor: 'transparent' }]}
                    onPress={tryBiometricAuth}
                    disabled={!authSettings.biometricEnabled || mode !== 'auth'}
                  >
                    {authSettings.biometricEnabled && mode === 'auth' && (
                      <MaterialIcons 
                        name="fingerprint" 
                        size={24} 
                        color={theme.PRIMARY} 
                      />
                    )}
                  </TouchableOpacity>
                );
              }
              
              if (item === 'backspace') {
                return (
                  <TouchableOpacity
                    key={itemIndex}
                    style={[styles.numberButton, { backgroundColor: 'transparent' }]}
                    onPress={handleBackspace}
                    disabled={pin.length === 0}
                  >
                    <MaterialIcons 
                      name="backspace" 
                      size={24} 
                      color={pin.length > 0 ? theme.TEXT_PRIMARY : theme.TEXT_SECONDARY} 
                    />
                  </TouchableOpacity>
                );
              }

              return (
                <TouchableOpacity
                  key={itemIndex}
                  style={[styles.numberButton, { backgroundColor: theme.CARD }]}
                  onPress={() => handleNumberPress(item.toString())}
                  disabled={isLoading}
                >
                  <Text style={[styles.numberText, { color: theme.TEXT_PRIMARY }]}>
                    {item}
                  </Text>
                </TouchableOpacity>
              );
            })}
          </View>
        ))}
      </View>
    );
  };

  // PIN noktaları
  const renderPinDots = () => {
    return (
      <View style={styles.pinDots}>
        {[0, 1, 2, 3].map((index) => (
          <View
            key={index}
            style={[
              styles.pinDot,
              {
                backgroundColor: index < pin.length ? theme.PRIMARY : theme.BORDER,
                borderColor: theme.BORDER
              }
            ]}
          />
        ))}
      </View>
    );
  };

  return (
    <View style={[styles.container, { paddingTop: insets.top, backgroundColor: theme.BACKGROUND }]}>
      {/* Header */}
      <View style={styles.header}>
        {mode !== 'auth' && (
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <MaterialIcons name="arrow-back" size={24} color={theme.TEXT_PRIMARY} />
          </TouchableOpacity>
        )}
      </View>

      {/* Content */}
      <View style={styles.content}>
        {/* Logo/Icon */}
        <View style={styles.logoContainer}>
          <View style={[styles.logoCircle, { backgroundColor: theme.PRIMARY }]}>
            <MaterialIcons name="lock" size={32} color={theme.WHITE} />
          </View>
        </View>

        {/* Title */}
        <Text style={[styles.title, { color: theme.TEXT_PRIMARY }]}>
          {getTitle()}
        </Text>
        <Text style={[styles.subtitle, { color: theme.TEXT_SECONDARY }]}>
          {getSubtitle()}
        </Text>

        {/* PIN Dots */}
        {renderPinDots()}

        {/* Number Pad */}
        {renderNumberPad()}

        {/* Attempts Warning */}
        {attempts > 0 && mode === 'auth' && (
          <Text style={[styles.attemptsText, { color: Colors.DANGER }]}>
            {attempts}/{maxAttempts} hatalı deneme
          </Text>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  backButton: {
    padding: 8,
  },
  content: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 32,
  },
  logoContainer: {
    marginBottom: 32,
  },
  logoCircle: {
    width: 80,
    height: 80,
    borderRadius: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 48,
  },
  pinDots: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 48,
  },
  pinDot: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginHorizontal: 12,
    borderWidth: 2,
  },
  numberPad: {
    width: '100%',
    maxWidth: 300,
  },
  numberRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  numberButton: {
    width: 70,
    height: 70,
    borderRadius: 35,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  numberText: {
    fontSize: 24,
    fontWeight: '600',
  },
  attemptsText: {
    fontSize: 14,
    marginTop: 24,
    textAlign: 'center',
  },
});
