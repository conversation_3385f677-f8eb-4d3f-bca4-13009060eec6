import React, { useState, useEffect, useMemo, useCallback, useLayoutEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  ScrollView,
  Alert,
  TouchableOpacity,
  Platform,
  Switch,
  KeyboardAvoidingView,
  ActivityIndicator
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { Picker } from '@react-native-picker/picker';
import DateTimePicker from '@react-native-community/datetimepicker';
import { MaterialIcons } from '@expo/vector-icons';
import { format, parseISO, isValid, addDays, addMonths, addYears } from 'date-fns';
import { tr } from 'date-fns/locale';
import { useSQLiteContext } from 'expo-sqlite';

/**
 * Güvenli tarih parsing fonksiyonu - geçersiz tarihleri ele alır
 * @param {string} dateString - Parse edilecek tarih string'i
 * @param {Date} fallback - Geçersiz durumda kullanıla<PERSON>k fallback tarih
 * @returns {Date} Geçerli tarih veya fallback
 */
const safeParseDateString = (dateString, fallback = new Date()) => {
  if (!dateString) return fallback;
  
  try {
    const parsed = parseISO(dateString);
    return isValid(parsed) ? parsed : fallback;
  } catch (error) {
    console.warn('Date parsing error:', error);
    return fallback;
  }
};
import { addRegularIncome, getRegularIncomeById, updateRegularIncome } from '../services/regularIncomeService';
import { useAppContext } from '../context/AppContext';
import { Colors } from '../constants/colors';
import { formStyles } from '../components/regular-income/styles';

// Import modular components
import {
  RegularIncomeBasicInfo,
  RegularIncomeStatusBadge,
  RegularIncomeFormStatus,
  RegularIncomeNotificationSettings,
  RegularIncomeRecurrenceSettings,
  RegularIncomeAdditionalInfo,
  RegularIncomeActionButtons,
  RegularIncomeErrorDisplay,
  } from '../components/regular-income';

/**
 * Düzenli Gelir Formu Ekranı
 * Yeni gelir ekleme ve mevcut geliri düzenleme işlevselliği sağlar
 * @returns {JSX.Element} Düzenli Gelir Form Ekranı
 */
const RegularIncomeFormScreen = React.memo(() => {
  const navigation = useNavigation();
  const route = useRoute();
  const { defaultCurrency } = useAppContext();
  const db = useSQLiteContext();
  const incomeId = route.params?.incomeId;
  const isEditMode = !!incomeId;

  // State yönetimi
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    amount: '',
    description: '',
    category: 'salary',
    frequency: 'monthly',
    startDate: new Date(),
    endDate: null,
    isActive: true,
    hasEndDate: false,
    dayOfMonth: new Date().getDate(),
    dayOfWeek: new Date().getDay(),
    weekOfMonth: 1,
    recurrenceInterval: 1, // Add this field
    isNotificationEnabled: true,
    notificationDaysBefore: 1,
    tags: '',
    currency: defaultCurrency || 'TRY'
  });

  const [errors, setErrors] = useState({});
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showEndDatePicker, setShowEndDatePicker] = useState(false);
  const [datePickerMode, setDatePickerMode] = useState('date');
  
  // Additional state for enhanced form interaction
  const [touched, setTouched] = useState({});
  const [focusedField, setFocusedField] = useState(null);
  const [fieldErrors, setFieldErrors] = useState({});

  // Helper function to check if a field is empty
  const isFieldEmpty = useCallback((fieldName) => {
    const emptyChecks = {
      name: !formData.name?.trim(),
      amount: !formData.amount?.trim(),
      description: !formData.description?.trim(),
      dayOfMonth: formData.frequency === 'monthly' && !formData.dayOfMonth,
      notificationDaysBefore: formData.isNotificationEnabled && !formData.notificationDaysBefore,
    };
    
    return emptyChecks[fieldName] || false;
  }, [formData]);

  // Input styling function based on field state
  const getInputStyle = useCallback((fieldName) => {
    // Base style - always applied
    const baseStyle = [styles.input];
    
    // Error state - highest priority
    if (fieldErrors?.[fieldName] || errors?.[fieldName] || (touched[fieldName] && isFieldEmpty(fieldName))) {
      return [styles.input, styles.inputError];
    }
    
    // Focus state
    if (focusedField === fieldName) {
      return [styles.input, styles.inputFocused];
    }

    // Success state - when field is touched, valid, and not empty
    if (touched[fieldName] && !fieldErrors?.[fieldName] && !errors?.[fieldName] && !isFieldEmpty(fieldName)) {
      return [
        styles.input,
        {
          borderColor: Colors.SUCCESS,
          backgroundColor: `${Colors.SUCCESS}08`,
        }
      ];
    }
    
    return baseStyle;
  }, [fieldErrors, errors, focusedField, touched, isFieldEmpty]);

  // Handle input focus
  const handleInputFocus = useCallback((fieldName) => {
    setFocusedField(fieldName);
    if (!touched[fieldName]) {
      setTouched(prev => ({ ...prev, [fieldName]: true }));
    }
  }, [touched]);

  // Handle input blur
  const handleInputBlur = useCallback((fieldName) => {
    setFocusedField(null);
    setTouched(prev => ({ ...prev, [fieldName]: true }));
  }, []);

  // Get field error text
  const getFieldErrorText = useCallback((fieldName) => {
    if (fieldErrors?.[fieldName]) {
      return typeof fieldErrors[fieldName] === 'string' ? fieldErrors[fieldName] : 'Geçersiz değer';
    }
    if (errors?.[fieldName]) {
      return errors[fieldName];
    }
    if (touched[fieldName] && isFieldEmpty(fieldName)) {
      const fieldLabels = {
        name: 'Gelir adı',
        amount: 'Tutar',
        description: 'Açıklama',
        dayOfMonth: 'Ödeme günü',
        notificationDaysBefore: 'Bildirim süresi'
      };
      return `${fieldLabels[fieldName] || 'Bu alan'} zorunludur`;
    }
    return null;
  }, [fieldErrors, errors, touched, isFieldEmpty]);

  // Format amount for display
  const getFormattedAmount = useCallback(() => {
    if (!formData.amount) return '';
    const cleanAmount = formData.amount.replace(/[^\d.,]/g, '');
    const numericAmount = parseFloat(cleanAmount.replace(',', '.'));
    if (isNaN(numericAmount)) return formData.amount;
    
    return numericAmount.toLocaleString('tr-TR', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });
  }, [formData.amount]);

  // Handle amount change with formatting
  const handleAmountChange = useCallback((text) => {
    // Remove any non-numeric characters except comma and period
    const cleanText = text.replace(/[^\d.,]/g, '');
    setFormData(prev => ({ ...prev, amount: cleanText }));
    
    // Mark field as touched and validate
    setTouched(prev => ({ ...prev, amount: true }));
    
    // Validate amount
    const numericValue = parseFloat(cleanText.replace(',', '.'));
    const isValid = !isNaN(numericValue) && numericValue > 0 && numericValue <= 999999999;
    setFieldErrors(prev => ({ ...prev, amount: !isValid }));
  }, []);

  // Recurrence interval management functions
  const setRecurrenceInterval = useCallback((interval) => {
    setFormData(prev => ({
      ...prev,
      recurrenceInterval: interval
    }));
  }, []);

  const adjustRecurrenceInterval = useCallback((frequency, currentInterval) => {
    const maxIntervals = {
      daily: 365,
      weekly: 52,
      monthly: 12,
      yearly: 10
    };
    
    const maxInterval = maxIntervals[frequency] || 12;
    return Math.min(Math.max(1, currentInterval || 1), maxInterval);
  }, []);

  const calculateFuturePaymentDates = useCallback((options = {}) => {
    const { 
      startDate = formData.startDate,
      frequency = formData.frequency,
      interval = formData.recurrenceInterval || 1,
      count = 5
    } = options;
    
    if (!startDate) return [];
    
    const dates = [];
    let currentDate = new Date(startDate);
    
    for (let i = 0; i < count; i++) {
      switch (frequency) {
        case 'daily':
          currentDate = addDays(currentDate, interval);
          break;
        case 'weekly':
          currentDate = addDays(currentDate, interval * 7);
          break;
        case 'monthly':
          currentDate = addMonths(currentDate, interval);
          if (formData.dayOfMonth) {
            currentDate.setDate(Math.min(
              formData.dayOfMonth,
              new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0).getDate()
            ));
          }
          break;
        case 'yearly':
          currentDate = addYears(currentDate, interval);
          break;
        default:
          currentDate = addMonths(currentDate, interval);
      }
      
      dates.push(new Date(currentDate));
    }
    
    return dates;
  }, [formData.startDate, formData.frequency, formData.recurrenceInterval, formData.dayOfMonth]);
  
  const parseSafeAmount = useCallback((amountString) => {
    if (!amountString) return 0;
    
    // Remove any non-numeric characters except comma and period
    const cleanAmount = amountString.toString().replace(/[^\d.,]/g, '');
    
    // Handle Turkish number format (comma as decimal separator)
    const normalizedAmount = cleanAmount.replace(',', '.');
    
    const parsed = parseFloat(normalizedAmount);
    return isNaN(parsed) ? 0 : parsed;
  }, []);

  // Load existing income data for editing
  const loadIncome = useCallback(async () => {
    if (!incomeId) return;
    
    try {
      setLoading(true);
      const income = await getRegularIncomeById(db, incomeId);
      
      if (income) {        setFormData({
          name: income.name || '',
          amount: income.amount?.toString() || '',
          description: income.description || '',
          category: income.category || 'salary',
          frequency: income.frequency || 'monthly',
          startDate: safeParseDateString(income.start_date),
          endDate: income.end_date ? safeParseDateString(income.end_date, null) : null,
          isActive: income.is_active !== 0,
          hasEndDate: !!income.end_date,
          dayOfMonth: income.payment_day || new Date().getDate(),
          dayOfWeek: income.day_of_week || new Date().getDay(),
          weekOfMonth: income.week_of_month || 1,
          recurrenceInterval: income.recurrence_interval || 1,
          isNotificationEnabled: income.notification_enabled !== 0,
          notificationDaysBefore: income.notification_days_before || 1,
          tags: income.tags || '',
          currency: income.currency || defaultCurrency || 'TRY'
        });
      }
    } catch (error) {
      console.error('Error loading income:', error);
      Alert.alert('Hata', 'Gelir bilgileri yüklenirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  }, [incomeId, db, defaultCurrency]);

  // Validate form data
  const validateForm = useCallback(() => {
    const newErrors = {};
    
    if (!formData.name?.trim()) {
      newErrors.name = 'Gelir adı zorunludur';
    }
    
    if (!formData.amount?.trim()) {
      newErrors.amount = 'Tutar zorunludur';
    } else {
      const amount = parseSafeAmount(formData.amount);
      if (amount <= 0) {
        newErrors.amount = 'Tutar 0\'dan büyük olmalıdır';
      }
      if (amount > 999999999) {
        newErrors.amount = 'Tutar çok büyük';
      }
    }
    
    if (formData.frequency === 'monthly' && (!formData.dayOfMonth || formData.dayOfMonth < 1 || formData.dayOfMonth > 31)) {
      newErrors.dayOfMonth = 'Geçerli bir ödeme günü seçiniz (1-31)';
    }
    
    if (formData.isNotificationEnabled && (!formData.notificationDaysBefore || formData.notificationDaysBefore < 1)) {
      newErrors.notificationDaysBefore = 'Bildirim süresi en az 1 gün olmalıdır';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [formData, parseSafeAmount]);

  // Handle form submission
  const handleSubmit = useCallback(async () => {
    if (!validateForm()) {
      Alert.alert('Hata', 'Lütfen tüm gerekli alanları doğru şekilde doldurunuz.');
      return;
    }
    
    try {
      setSaving(true);
      
      const incomeData = {
        name: formData.name.trim(),
        amount: parseSafeAmount(formData.amount),
        description: formData.description?.trim() || null,
        category: formData.category,
        frequency: formData.frequency,
        start_date: formData.startDate.toISOString(),
        end_date: formData.hasEndDate && formData.endDate ? formData.endDate.toISOString() : null,
        is_active: formData.isActive ? 1 : 0,
        payment_day: formData.dayOfMonth,
        day_of_week: formData.dayOfWeek,
        week_of_month: formData.weekOfMonth,
        recurrence_interval: formData.recurrenceInterval || 1,
        notification_enabled: formData.isNotificationEnabled ? 1 : 0,
        notification_days_before: formData.notificationDaysBefore || 1,
        tags: formData.tags?.trim() || null,
        currency: formData.currency
      };
      
      if (isEditMode) {
        await updateRegularIncome(db, incomeId, incomeData);
        Alert.alert('Başarılı', 'Düzenli gelir güncellendi.', [
          { text: 'Tamam', onPress: () => navigation.goBack() }
        ]);
      } else {
        await addRegularIncome(db, incomeData);
        Alert.alert('Başarılı', 'Düzenli gelir eklendi.', [
          { text: 'Tamam', onPress: () => navigation.goBack() }
        ]);
      }
    } catch (error) {
      console.error('Error saving income:', error);
      Alert.alert('Hata', 'Düzenli gelir kaydedilirken bir hata oluştu.');
    } finally {
      setSaving(false);
    }
  }, [formData, validateForm, parseSafeAmount, isEditMode, incomeId, db, navigation]);

  // Load data on component mount
  useEffect(() => {
    if (isEditMode) {
      loadIncome();
    }
  }, [isEditMode, loadIncome]);

  // Set navigation header
  useLayoutEffect(() => {
    navigation.setOptions({
      title: isEditMode ? 'Düzenli Gelir Düzenle' : 'Düzenli Gelir Ekle',
      headerStyle: {
        backgroundColor: Colors.PRIMARY,
      },
      headerTintColor: '#fff',
      headerTitleStyle: {
        fontWeight: 'bold',
      },
    });
  }, [navigation, isEditMode]);

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      style={styles.container}
      keyboardVerticalOffset={Platform.OS === "ios" ? 88 : 0}
    >
      <ScrollView
        style={{ flex: 1 }}
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
        bounces={true}
      >        {/* Basic Information Section */}
        <RegularIncomeBasicInfo
          title={formData.name}
          setTitle={(value) => setFormData(prev => ({ ...prev, name: value }))}
          amount={formData.amount}
          handleAmountChange={handleAmountChange}
          currencyCode={formData.currency}
          setCurrencyCode={(value) => setFormData(prev => ({ ...prev, currency: value }))}
          fieldErrors={fieldErrors}
          handleInputFocus={handleInputFocus}
          handleInputBlur={handleInputBlur}
          getInputStyle={getInputStyle}
          getFieldErrorText={getFieldErrorText}
          getFormattedAmount={getFormattedAmount}
        />

        {/* Recurrence Configuration Section */}
        <RegularIncomeRecurrenceSettings 
          recurrenceType={formData.frequency}
          setRecurrenceType={(value) => setFormData(prev => ({ ...prev, frequency: value }))
          }
          recurrenceInterval={formData.recurrenceInterval}
          setRecurrenceInterval={setRecurrenceInterval}
          paymentDay={formData.dayOfMonth}
          setPaymentDay={(value) => setFormData(prev => ({ ...prev, dayOfMonth: value }))
          }
          nextPaymentDate={formData.startDate}
        />        {/* Notification Settings Section */}
        <RegularIncomeNotificationSettings
          notificationEnabled={formData.isNotificationEnabled}
          setNotificationEnabled={(value) => setFormData(prev => ({ ...prev, isNotificationEnabled: value }))}
          notificationDaysBefore={formData.notificationDaysBefore}
          setNotificationDaysBefore={(value) => setFormData(prev => ({ ...prev, notificationDaysBefore: value }))}
          getInputStyle={getInputStyle}
          handleInputFocus={handleInputFocus}
          handleInputBlur={handleInputBlur}
          getFieldErrorText={getFieldErrorText}
          fieldErrors={fieldErrors}
        />        {/* Additional Information Section */}
        <RegularIncomeAdditionalInfo
          notes={formData.description}
          setNotes={(value) => setFormData(prev => ({ ...prev, description: value }))}
          getInputStyle={getInputStyle}
          handleInputFocus={handleInputFocus}
          handleInputBlur={handleInputBlur}
        />

        {/* Form Actions */}
        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={[styles.button, styles.cancelButton]}
            onPress={() => navigation.goBack()}
          >
            <Text style={styles.cancelButtonText}>İptal</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.button, styles.saveButton]}
            onPress={handleSubmit}
            disabled={loading}
          >
            {loading ? (
              <ActivityIndicator color="#fff" size="small" />
            ) : (
              <Text style={styles.saveButtonText}>
                {isEditMode ? 'Güncelle' : 'Kaydet'}
              </Text>
            )}
          </TouchableOpacity>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  contentContainer: {
    padding: 20,
    paddingBottom: 40,
    gap: 24,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 32,
    gap: 16,
    paddingHorizontal: 4,
  },
  button: {
    flex: 1,
    paddingVertical: 16,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    minHeight: 54,
  },
  cancelButton: {
    backgroundColor: '#ffffff',
    borderWidth: 2,
    borderColor: '#e9ecef',
  },
  saveButton: {
    backgroundColor: Colors.PRIMARY,
    borderWidth: 2,
    borderColor: Colors.PRIMARY,
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '700',
    color: '#6c757d',
    letterSpacing: 0.5,
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: '700',
    color: '#ffffff',
    letterSpacing: 0.5,
  },
});

export default RegularIncomeFormScreen;
