import React, { useState, useEffect, useMemo, useCallback, useLayoutEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  ScrollView,
  Alert,
  TouchableOpacity,
  Platform,
  Switch,
  KeyboardAvoidingView
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { Picker } from '@react-native-picker/picker';
import DateTimePicker from '@react-native-community/datetimepicker';
import { MaterialIcons } from '@expo/vector-icons';
import { format, parseISO, isValid, addDays, addMonths, addYears } from 'date-fns';
import { tr } from 'date-fns/locale';
import { useSQLiteContext } from 'expo-sqlite';
import { addRegularIncome, getRegularIncomeById, updateRegularIncome } from '../services/regularIncomeService';
import { useAppContext } from '../context/AppContext';
import { Colors } from '../constants/colors';
import { formStyles } from '../components/regular-income/styles';

// Import modular components
import {
  RegularIncomeBasicInfo,
  RegularIncomeStatusBadge,
  RegularIncomeFormStatus,
  RegularIncomeNotificationSettings,
  RegularIncomeRecurrenceSettings,
  RegularIncomeAdditionalInfo,
  RegularIncomeActionButtons,
  RegularIncomeErrorDisplay,
  RegularIncomeLoadingSpinner
} from '../components/regular-income';

/**
 * Düzenli Gelir Formu Ekranı
 * Yeni gelir ekleme ve mevcut geliri düzenleme işlevselliği sağlar
 * @returns {JSX.Element} Düzenli Gelir Form Ekranı
 */
const RegularIncomeFormScreen = React.memo(() => {
  const navigation = useNavigation();
  const route = useRoute();
  const { defaultCurrency } = useAppContext();
  const db = useSQLiteContext();
  const incomeId = route.params?.incomeId;
  const isEditMode = !!incomeId;

  // State yönetimi
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    amount: '',
    description: '',
    category: 'salary',
    frequency: 'monthly',
    startDate: new Date(),
    endDate: null,
    isActive: true,
    hasEndDate: false,
    dayOfMonth: new Date().getDate(),
    dayOfWeek: new Date().getDay(),
    weekOfMonth: 1,
    isNotificationEnabled: true,
    notificationDaysBefore: 1,
    tags: '',
    currency: defaultCurrency || 'TRY'
  });

  const [errors, setErrors] = useState({});
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showEndDatePicker, setShowEndDatePicker] = useState(false);
  const [datePickerMode, setDatePickerMode] = useState('date');

  // Form validation
  const validateForm = useCallback(() => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Gelir adı zorunludur';
    } else if (formData.name.trim().length < 2) {
      newErrors.name = 'Gelir adı en az 2 karakter olmalıdır';
    }

    if (!formData.amount.trim()) {
      newErrors.amount = 'Tutar zorunludur';
    } else {
      const amount = parseFloat(formData.amount.replace(',', '.'));
      if (isNaN(amount) || amount <= 0) {
        newErrors.amount = 'Geçerli bir tutar giriniz';
      } else if (amount > 999999999) {
        newErrors.amount = 'Tutar çok büyük';
      }
    }

    if (!formData.startDate) {
      newErrors.startDate = 'Başlangıç tarihi zorunludur';
    }

    if (formData.hasEndDate && !formData.endDate) {
      newErrors.endDate = 'Bitiş tarihi seçiniz';
    }

    if (formData.hasEndDate && formData.endDate && formData.startDate && formData.endDate <= formData.startDate) {
      newErrors.endDate = 'Bitiş tarihi başlangıç tarihinden sonra olmalıdır';
    }

    if (formData.frequency === 'monthly' && (formData.dayOfMonth < 1 || formData.dayOfMonth > 31)) {
      newErrors.dayOfMonth = 'Geçerli bir gün seçiniz (1-31)';
    }

    if (formData.notificationDaysBefore < 0 || formData.notificationDaysBefore > 30) {
      newErrors.notificationDaysBefore = 'Bildirim günü 0-30 arasında olmalıdır';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [formData]);

  // Mevcut geliri yükle
  const loadIncome = useCallback(async () => {
    if (!incomeId) return;

    try {
      setLoading(true);
      const income = await getRegularIncomeById(db, incomeId);
      
      if (income) {
        setFormData({
          name: income.name || '',
          amount: income.amount?.toString() || '',
          description: income.description || '',
          category: income.category || 'salary',
          frequency: income.frequency || 'monthly',
          startDate: income.start_date ? parseISO(income.start_date) : new Date(),
          endDate: income.end_date ? parseISO(income.end_date) : null,
          isActive: income.is_active !== 0,
          hasEndDate: !!income.end_date,
          dayOfMonth: income.day_of_month || new Date().getDate(),
          dayOfWeek: income.day_of_week || new Date().getDay(),
          weekOfMonth: income.week_of_month || 1,
          isNotificationEnabled: income.is_notification_enabled !== 0,
          notificationDaysBefore: income.notification_days_before || 1,
          tags: income.tags || '',
          currency: income.currency || defaultCurrency || 'TRY'
        });
      }
    } catch (error) {
      console.error('Gelir yükleme hatası:', error);
      Alert.alert('Hata', 'Gelir bilgileri yüklenirken bir hata oluştu');
    } finally {
      setLoading(false);
    }
  }, [incomeId, db, defaultCurrency]);

  // Form gönderme işlemi
  const handleSubmit = useCallback(async () => {
    if (!validateForm()) {
      Alert.alert('Hata', 'Lütfen form hatalarını düzeltin');
      return;
    }

    try {
      setSaving(true);
      
      const incomeData = {
        name: formData.name.trim(),
        amount: parseFloat(formData.amount.replace(',', '.')),
        description: formData.description.trim(),
        category: formData.category,
        frequency: formData.frequency,
        start_date: formData.startDate.toISOString(),
        end_date: formData.hasEndDate && formData.endDate ? formData.endDate.toISOString() : null,
        is_active: formData.isActive ? 1 : 0,
        day_of_month: formData.frequency === 'monthly' ? formData.dayOfMonth : null,
        day_of_week: formData.frequency === 'weekly' ? formData.dayOfWeek : null,
        week_of_month: formData.frequency === 'monthly' && formData.weekOfMonth ? formData.weekOfMonth : null,
        is_notification_enabled: formData.isNotificationEnabled ? 1 : 0,
        notification_days_before: formData.notificationDaysBefore,
        tags: formData.tags.trim(),
        currency: formData.currency
      };

      if (isEditMode) {
        await updateRegularIncome(db, incomeId, incomeData);
        Alert.alert('Başarılı', 'Düzenli gelir güncellendi', [
          { text: 'Tamam', onPress: () => navigation.goBack() }
        ]);
      } else {
        await addRegularIncome(db, incomeData);
        Alert.alert('Başarılı', 'Düzenli gelir eklendi', [
          { text: 'Tamam', onPress: () => navigation.goBack() }
        ]);
      }
    } catch (error) {
      console.error('Gelir kaydetme hatası:', error);
      Alert.alert('Hata', 'Gelir kaydedilirken bir hata oluştu');
    } finally {
      setSaving(false);
    }
  }, [formData, validateForm, isEditMode, incomeId, db, navigation]);

  // Navigation header'ı ayarla
  useLayoutEffect(() => {
    navigation.setOptions({
      title: isEditMode ? 'Geliri Düzenle' : 'Yeni Düzenli Gelir',
      headerRight: () => (
        <TouchableOpacity
          style={styles.headerButton}
          onPress={handleSubmit}
          disabled={saving}
        >
          <Text style={styles.headerButtonText}>
            {saving ? 'Kaydediliyor...' : 'Kaydet'}
          </Text>
        </TouchableOpacity>
      ),
    });
  }, [navigation, isEditMode, handleSubmit, saving]);

  // Component mount edildiğinde geliri yükle
  useEffect(() => {
    loadIncome();
  }, [loadIncome]);

  // Form verisi değiştiğinde hataları temizle
  useEffect(() => {
    if (Object.keys(errors).length > 0) {
      const timer = setTimeout(() => {
        setErrors({});
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [formData, errors]);

  // Gelecek ödeme tarihlerini hesapla
  const calculateNextPayments = useMemo(() => {
    const payments = [];
    const startDate = formData.startDate;
    const endDate = formData.hasEndDate ? formData.endDate : null;
    
    if (!startDate) return payments;

    let nextDate = new Date(startDate);
    const today = new Date();
    
    // Bugünden sonraki 5 ödeme tarihini hesapla
    for (let i = 0; i < 5; i++) {
      if (endDate && nextDate > endDate) break;
      
      switch (formData.frequency) {
        case 'daily':
          nextDate = addDays(nextDate, 1);
          break;
        case 'weekly':
          nextDate = addDays(nextDate, 7);
          break;
        case 'monthly':
          nextDate = addMonths(nextDate, 1);
          // Set specific day of month if provided
          if (formData.dayOfMonth) {
            nextDate.setDate(Math.min(formData.dayOfMonth, new Date(nextDate.getFullYear(), nextDate.getMonth() + 1, 0).getDate()));
          }
          break;
        case 'yearly':
          nextDate = addYears(nextDate, 1);
          break;
        default:
          nextDate = addMonths(nextDate, 1);
      }
      
      if (nextDate >= today) {
        payments.push(new Date(nextDate));
      }
    }
    
    return payments;
  }, [formData.startDate, formData.endDate, formData.hasEndDate, formData.frequency, formData.dayOfMonth]);

  const handleDateChange = useCallback((event, selectedDate) => {
    setShowDatePicker(false);
    if (selectedDate) {
      setFormData(prev => ({
        ...prev,
        startDate: selectedDate,
        dayOfMonth: selectedDate.getDate()
      }));
    }
  }, []);

  const handleEndDateChange = useCallback((event, selectedDate) => {
    setShowEndDatePicker(false);
    if (selectedDate) {
      setFormData(prev => ({
        ...prev,
        endDate: selectedDate
      }));
    }
  }, []);

  const updateFormData = useCallback((field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  }, []);

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <RegularIncomeLoadingSpinner />
        <Text style={styles.loadingText}>Gelir bilgileri yükleniyor...</Text>
      </View>
    );
  }

  return (
    <KeyboardAvoidingView 
      style={styles.container} 
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView 
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        keyboardShouldPersistTaps="handled"
        showsVerticalScrollIndicator={false}
      >
        {/* Form Durum Göstergesi */}
        <RegularIncomeFormStatus 
          isEditMode={isEditMode}
          isActive={formData.isActive}
          hasErrors={Object.keys(errors).length > 0}
        />

        {/* Temel Bilgiler Bölümü */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <MaterialIcons name="info" size={20} color={Colors.PRIMARY} />
            <Text style={styles.sectionTitle}>Temel Bilgiler</Text>
          </View>

          <RegularIncomeBasicInfo
            formData={formData}
            errors={errors}
            onUpdateFormData={updateFormData}
          />
        </View>

        {/* Tarih ve Tekrarlama Ayarları */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <MaterialIcons name="schedule" size={20} color={Colors.PRIMARY} />
            <Text style={styles.sectionTitle}>Tarih ve Tekrarlama</Text>
          </View>

          {/* Başlangıç Tarihi */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Başlangıç Tarihi *</Text>
            <TouchableOpacity
              style={[
                styles.dateInput,
                errors.startDate && styles.inputError
              ]}
              onPress={() => setShowDatePicker(true)}
            >
              <Text style={[
                styles.dateText,
                { color: formData.startDate ? Colors.TEXT : Colors.TEXT_SECONDARY }
              ]}>
                {formData.startDate 
                  ? format(formData.startDate, 'dd MMMM yyyy', { locale: tr })
                  : 'Tarih seçiniz'
                }
              </Text>
              <MaterialIcons name="calendar-today" size={20} color={Colors.PRIMARY} />
            </TouchableOpacity>
            {errors.startDate && <RegularIncomeErrorDisplay message={errors.startDate} />}
          </View>

          {/* Tekrarlama Ayarları */}
          <RegularIncomeRecurrenceSettings
            formData={formData}
            errors={errors}
            onUpdateFormData={updateFormData}
          />

          {/* Bitiş Tarihi */}
          <View style={styles.inputGroup}>
            <View style={styles.switchRow}>
              <Text style={styles.label}>Bitiş Tarihi Var</Text>
              <Switch
                value={formData.hasEndDate}
                onValueChange={(value) => updateFormData('hasEndDate', value)}
                trackColor={{ false: Colors.BORDER, true: `${Colors.PRIMARY}40` }}
                thumbColor={formData.hasEndDate ? Colors.PRIMARY : Colors.TEXT_SECONDARY}
              />
            </View>

            {formData.hasEndDate && (
              <TouchableOpacity
                style={[
                  styles.dateInput,
                  errors.endDate && styles.inputError
                ]}
                onPress={() => setShowEndDatePicker(true)}
              >
                <Text style={[
                  styles.dateText,
                  { color: formData.endDate ? Colors.TEXT : Colors.TEXT_SECONDARY }
                ]}>
                  {formData.endDate 
                    ? format(formData.endDate, 'dd MMMM yyyy', { locale: tr })
                    : 'Bitiş tarihi seçiniz'
                  }
                </Text>
                <MaterialIcons name="calendar-today" size={20} color={Colors.PRIMARY} />
              </TouchableOpacity>
            )}
            {errors.endDate && <RegularIncomeErrorDisplay message={errors.endDate} />}
          </View>
        </View>

        {/* Bildirim Ayarları */}
        <RegularIncomeNotificationSettings
          formData={formData}
          errors={errors}
          onUpdateFormData={updateFormData}
        />

        {/* Ek Bilgiler */}
        <RegularIncomeAdditionalInfo
          formData={formData}
          errors={errors}
          onUpdateFormData={updateFormData}
        />

        {/* Durum Ayarları */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <MaterialIcons name="toggle-on" size={20} color={Colors.PRIMARY} />
            <Text style={styles.sectionTitle}>Durum</Text>
          </View>

          <View style={styles.switchRow}>
            <View style={styles.switchInfo}>
              <Text style={styles.switchLabel}>Aktif Durum</Text>
              <Text style={styles.switchDescription}>
                Gelir aktif olduğunda otomatik kayıtlar oluşturulur
              </Text>
            </View>
            <Switch
              value={formData.isActive}
              onValueChange={(value) => updateFormData('isActive', value)}
              trackColor={{ false: Colors.BORDER, true: `${Colors.PRIMARY}40` }}
              thumbColor={formData.isActive ? Colors.PRIMARY : Colors.TEXT_SECONDARY}
            />
          </View>

          <RegularIncomeStatusBadge 
            isActive={formData.isActive}
            style={styles.statusBadge}
          />
        </View>

        {/* Gelecek Ödemeler Önizlemesi */}
        {calculateNextPayments.length > 0 && (
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <MaterialIcons name="preview" size={20} color={Colors.PRIMARY} />
              <Text style={styles.sectionTitle}>Gelecek Ödemeler</Text>
            </View>

            <View style={styles.paymentsPreview}>
              {calculateNextPayments.map((date, index) => (
                <View key={index} style={styles.paymentItem}>
                  <View style={styles.paymentDate}>
                    <MaterialIcons name="schedule" size={16} color={Colors.SUCCESS} />
                    <Text style={styles.paymentDateText}>
                      {format(date, 'dd MMM yyyy', { locale: tr })}
                    </Text>
                  </View>
                  <Text style={styles.paymentAmount}>
                    {parseFloat(formData.amount || 0).toLocaleString('tr-TR', {
                      minimumFractionDigits: 2,
                      maximumFractionDigits: 2
                    })} {formData.currency}
                  </Text>
                </View>
              ))}
            </View>

            <View style={styles.totalSummary}>
              <Text style={styles.totalText}>
                Toplam: {(calculateNextPayments.length * parseFloat(formData.amount || 0))
                  .toLocaleString('tr-TR', {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2
                  })} {formData.currency}
              </Text>
            </View>
          </View>
        )}

        {/* Action Buttons */}
        <RegularIncomeActionButtons
          isEditMode={isEditMode}
          saving={saving}
          onSubmit={handleSubmit}
          onCancel={() => navigation.goBack()}
        />
      </ScrollView>

      {/* Date Pickers */}
      {showDatePicker && (
        <DateTimePicker
          value={formData.startDate || new Date()}
          mode="date"
          display={Platform.OS === 'ios' ? 'spinner' : 'default'}
          onChange={handleDateChange}
          locale="tr-TR"
        />
      )}

      {showEndDatePicker && (
        <DateTimePicker
          value={formData.endDate || new Date()}
          mode="date"
          display={Platform.OS === 'ios' ? 'spinner' : 'default'}
          onChange={handleEndDateChange}
          minimumDate={formData.startDate}
          locale="tr-TR"
        />
      )}
    </KeyboardAvoidingView>
  );
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.BACKGROUND,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.BACKGROUND,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: Colors.TEXT_SECONDARY,
    textAlign: 'center',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 32,
  },
  headerButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: `${Colors.PRIMARY}12`,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: `${Colors.PRIMARY}15`,
  },
  headerButtonText: {
    color: Colors.PRIMARY,
    fontSize: 14,
    fontWeight: '600',
  },
  section: {
    backgroundColor: Colors.SURFACE,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: Colors.SHADOW,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    paddingBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: Colors.BORDER,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.TEXT,
    marginLeft: 8,
  },
  inputGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.TEXT,
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: Colors.BORDER,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: Colors.TEXT,
    backgroundColor: Colors.SURFACE,
  },
  inputFocused: {
    borderColor: Colors.PRIMARY,
    backgroundColor: `${Colors.PRIMARY}04`,
  },
  inputError: {
    borderColor: Colors.DANGER,
    backgroundColor: `${Colors.DANGER}04`,
  },
  textArea: {
    minHeight: 80,
    textAlignVertical: 'top',
  },
  dateInput: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: Colors.BORDER,
    borderRadius: 8,
    padding: 12,
    backgroundColor: Colors.SURFACE,
  },
  dateText: {
    fontSize: 16,
    color: Colors.TEXT,
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: Colors.BORDER,
    borderRadius: 8,
    backgroundColor: Colors.SURFACE,
  },
  picker: {
    height: 50,
  },
  switchRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  switchInfo: {
    flex: 1,
    marginRight: 16,
  },
  switchLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.TEXT,
  },
  switchDescription: {
    fontSize: 12,
    color: Colors.TEXT_SECONDARY,
    marginTop: 2,
  },
  statusBadge: {
    alignSelf: 'flex-start',
    marginTop: 8,
  },
  paymentsPreview: {
    borderRadius: 8,
    overflow: 'hidden',
  },
  paymentItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: Colors.SURFACE,
    borderBottomWidth: 1,
    borderBottomColor: Colors.BORDER,
  },
  paymentDate: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  paymentDateText: {
    fontSize: 14,
    color: Colors.TEXT,
    marginLeft: 8,
  },
  paymentAmount: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.SUCCESS,
  },
  totalSummary: {
    backgroundColor: `${Colors.SUCCESS}10`,
    padding: 12,
    borderRadius: 8,
    marginTop: 8,
    borderWidth: 1,
    borderColor: `${Colors.SUCCESS}20`,
  },
  totalText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.SUCCESS,
    textAlign: 'center',
  },
});

RegularIncomeFormScreen.displayName = 'RegularIncomeFormScreen';

export default RegularIncomeFormScreen;
