import React, { useState, useEffect, useMemo, useCallback, useLayoutEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  ScrollView,
  Alert,
  TouchableOpacity,
  Platform,
  Switch,
  KeyboardAvoidingView
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { Picker } from '@react-native-picker/picker';
import DateTimePicker from '@react-native-community/datetimepicker';
import { MaterialIcons } from '@expo/vector-icons';
import { format, parseISO, isValid, addDays, addMonths, addYears } from 'date-fns';
import { tr } from 'date-fns/locale';
import { useSQLiteContext } from 'expo-sqlite';
import { addRegularIncome, getRegularIncomeById, updateRegularIncome } from '../services/regularIncomeService';
import { useAppContext } from '../context/AppContext';
import { Colors } from '../constants/colors';
import { formStyles } from '../components/regular-income/styles';

// Import modular components
import {
  RegularIncomeBasicInfo,
  RegularIncomeStatusBadge,
  RegularIncomeFormStatus,
  RegularIncomeNotificationSettings,
  RegularIncomeRecurrenceSettings,
  RegularIncomeAdditionalInfo,
  RegularIncomeActionButtons,
  RegularIncomeErrorDisplay,
  RegularIncomeLoadingSpinner
} from '../components/regular-income';

/**
 * Düzenli Gelir Formu Ekranı
 * Yeni gelir ekleme ve mevcut geliri düzenleme işlevselliği sağlar
 * @returns {JSX.Element} Düzenli Gelir Form Ekranı
 */
const RegularIncomeFormScreen = React.memo(() => {
  const navigation = useNavigation();
  const route = useRoute();
  const { defaultCurrency } = useAppContext();
  const db = useSQLiteContext();
  const incomeId = route.params?.incomeId;
  const isEditMode = !!incomeId;
  
  // Form state
  const [title, setTitle] = useState('');
  const [amount, setAmount] = useState('');
  const [currencyCode, setCurrencyCode] = useState(defaultCurrency || 'TRY');
  const [paymentDay, setPaymentDay] = useState('');
  const [recurrenceType, setRecurrenceType] = useState('monthly');
  const [recurrenceInterval, setRecurrenceInterval] = useState('1');
  const [nextPaymentDate, setNextPaymentDate] = useState(new Date());
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [notificationEnabled, setNotificationEnabled] = useState(true);
  const [notificationDaysBefore, setNotificationDaysBefore] = useState('3');
  const [notificationTime, setNotificationTime] = useState(new Date(new Date().setHours(9, 0, 0, 0)));
  const [showTimePicker, setShowTimePicker] = useState(false);
  const [notes, setNotes] = useState('');
  const [status, setStatus] = useState('active');
  const [error, setError] = useState('');
  const [touched, setTouched] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showFuturePaymentsModal, setShowFuturePaymentsModal] = useState(false);
  /**
   * Input focus durumunu takip etmek için state
   */
  const [focusedField, setFocusedField] = useState(null);
  /**
   * Form alanlarındaki hataları takip etmek için state
   */
  const [fieldErrors, setFieldErrors] = useState({});
  /**
   * Formun tam doğrulamasını takip eden state
   */  
  const [allFieldsValidated, setAllFieldsValidated] = useState(false);

  // Declare handleSubmit early to avoid circular reference
  const handleSubmitRef = React.useRef(() => {});

  /**
   * Miktar değerini güvenli bir şekilde ayrıştırır
   * @param {string} value - Ayrıştırılacak miktar değeri
   * @returns {number} Ayrıştırılmış sayısal değer
   */
  const parseSafeAmount = useCallback((value) => {
    if (!value) return 0;
    const cleaned = value.replace(/[^\d,.]/g, '').replace(',', '.');
    const parsed = parseFloat(cleaned);
    return isNaN(parsed) ? 0 : parsed;
  }, []);

  /**
   * Header'da butonların yapılandırması için useLayoutEffect
   */
  useLayoutEffect(() => {
    navigation.setOptions({
      headerRight: () => (
        <View style={styles.headerButtonContainer}>
          <TouchableOpacity 
            onPress={() => setShowFuturePaymentsModal(true)} 
            style={styles.headerIconButton}
          >
            <MaterialIcons name="calendar-today" size={22} color={Colors.PRIMARY} />
          </TouchableOpacity>
          <TouchableOpacity 
            onPress={() => handleSubmitRef.current()} 
            disabled={isSubmitting} 
            style={styles.headerButton}
          >
            <MaterialIcons name={isEditMode ? "save" : "check"} size={24} color={Colors.PRIMARY} />
            <Text style={styles.headerButtonText}>{isEditMode ? 'Güncelle' : 'Kaydet'}</Text>
          </TouchableOpacity>
        </View>
      ),
      headerTitle: isEditMode ? 'Düzenli Geliri Düzenle' : 'Yeni Düzenli Gelir Ekle',
    });
  }, [navigation, isEditMode, isSubmitting, setShowFuturePaymentsModal]);

  /**
   * Eğer düzenleme modundaysa mevcut geliri yükler
   */
  useEffect(() => {
    if (isEditMode && incomeId) {
      const loadIncomeData = async () => {
        try {
          const income = await getRegularIncomeById({ id: incomeId });
          if (income) {
            setTitle(income.title);
            setAmount(String(income.amount));
            setCurrencyCode(income.currency_code);
            setPaymentDay(String(income.payment_day || ''));
            setRecurrenceType(income.recurrence_type);
            setRecurrenceInterval(String(income.recurrence_interval || '1'));
            
            // Tarih formatını doğru bir şekilde kontrol et ve dönüştür
            if (income.next_payment_date) {
              const date = parseISO(income.next_payment_date);
              setNextPaymentDate(isValid(date) ? date : new Date());
            }
            
            // Bildirim ayarlarını kontrol et
            if (income.notification_settings) {
              const settings = typeof income.notification_settings === 'string'
                 ? JSON.parse(income.notification_settings)
                 : income.notification_settings;
              
              setNotificationEnabled(settings.enabled);
              setNotificationDaysBefore(String(settings.days_before));
              
              // Zaman formatını kontrol et
              if (settings.time) {
                const [hours, minutes] = settings.time.split(':').map(Number);
                const timeDate = new Date();
                timeDate.setHours(hours, minutes, 0, 0);
                setNotificationTime(timeDate);
              }
            }
            
            setNotes(income.notes || '');
            setStatus(income.status || 'active');
          }
        } catch (err) {
          setError('Gelir bilgileri yüklenirken bir hata oluştu.');
          console.error('Error loading income data:', err);
        }
      };
      loadIncomeData();
    }
  }, [incomeId, isEditMode]);
  /**
   * Tarih değişikliği işleyicisi - optimize edilmiş versiyon
   * @param {Object} event - Değişiklik olayı
   * @param {Date} selectedDate - Seçilen tarih
   */
  const handleDateChange = useCallback((event, selectedDate) => {
    const currentDate = selectedDate || nextPaymentDate;
    setShowDatePicker(Platform.OS === 'ios');
    setNextPaymentDate(currentDate);
  }, [nextPaymentDate]);

  /**
   * Saat değişikliği işleyicisi - optimize edilmiş versiyon
   * @param {Object} event - Değişiklik olayı
   * @param {Date} selectedTime - Seçilen saat
   */
  const handleTimeChange = useCallback((event, selectedTime) => {
    const currentTime = selectedTime || notificationTime;
    setShowTimePicker(Platform.OS === 'ios');
    setNotificationTime(currentTime);  }, [notificationTime]);

  /**
   * Tekrarlama tipine göre bir sonraki tarihi hesaplar - optimize edilmiş versiyon
   * @param {Date} date - Baz alınacak tarih
   * @param {string} type - Tekrarlama tipi (daily, weekly, monthly, yearly, custom)
   * @param {string|number} interval - Tekrarlama aralığı
   * @returns {Date} Hesaplanan sonraki tarih
   */
  const calculateNextDate = useCallback((date, type, interval) => {
    const intervalNum = parseInt(interval, 10) || 1;
    
    // İşlem tipine göre optimize edilmiş hesaplama - nesne map kullanarak
    const calculations = {
      daily: () => addDays(date, intervalNum),
      weekly: () => addDays(date, intervalNum * 7),
      monthly: () => addMonths(date, intervalNum),
      yearly: () => addYears(date, intervalNum),
      custom: () => addDays(date, intervalNum)
    };
    
    // Eğer tip geçerliyse ilgili hesaplamayı yap, değilse default dön
    return calculations[type]?.() || addMonths(date, 1);
  }, []);
    /**
   * Gelecek 3 ödeme tarihini hesaplar - optimize edilmiş versiyon
   * @returns {Date[]} Gelecek 3 ödeme tarihi
   */
  const calculateFuturePaymentDates = useMemo(() => {
    // Optimize edilmiş fonksiyonel yaklaşım
    const dates = [];
    let currentDate = nextPaymentDate;
    const intervalNum = parseInt(recurrenceInterval, 10) || 1;
    
    // Hesaplama işlevini bir kez tanımlayıp tekrar kullanıyoruz
    const calculations = {
      daily: (date) => addDays(date, intervalNum),
      weekly: (date) => addDays(date, intervalNum * 7),
      monthly: (date) => addMonths(date, intervalNum),
      yearly: (date) => addYears(date, intervalNum),
      custom: (date) => addDays(date, intervalNum)
    };
    
    // Kullanılacak hesaplama fonksiyonu
    const calculateDate = calculations[recurrenceType] || calculations.monthly;
    
    // Fonksiyonel yaklaşım ile tarih hesaplama
    for (let i = 0; i < 3; i++) {
      currentDate = calculateDate(currentDate);
      dates.push(currentDate);
    }
    
    return dates;
  }, [nextPaymentDate, recurrenceType, recurrenceInterval]);
  /**
   * Tekrar aralığını ayarlar - optimize edilmiş versiyon
   * @param {number} adjustment - Eklenecek/çıkarılacak değer
   */
  const adjustRecurrenceInterval = useCallback((adjustment) => {
    setRecurrenceInterval(prev => {
      const current = parseInt(prev, 10) || 1;
      const newValue = Math.max(1, Math.min(99, current + adjustment));
      return newValue.toString();
    });
    setTouched(prev => ({ ...prev, recurrenceInterval: true }));
  }, []);

  /**
   * Bildirim gününü ayarlar - optimize edilmiş versiyon
   * @param {number} adjustment - Eklenecek/çıkarılacak değer
   */
  const adjustNotificationDays = useCallback((adjustment) => {
    setNotificationDaysBefore(prev => {
      const current = parseInt(prev, 10) || 0;
      const newValue = Math.max(0, Math.min(30, current + adjustment));
      return newValue.toString();
    });
    setTouched(prev => ({ ...prev, notificationDaysBefore: true }));
  }, []);
  /**
   * Input odak durumu değişikliği işleyicisi - optimize edilmiş versiyon
   * @param {string} fieldName - Alanın adı
   */
  const handleInputFocus = useCallback((fieldName) => {
    setFocusedField(fieldName);
    
    // Dokunulmuş alanı işaretle (validation için)
    if (!touched[fieldName]) {
      setTouched(prev => ({ ...prev, [fieldName]: true }));
    }
  }, [touched]);
  /**
   * Input odak kaybı işleyicisi - optimize edilmiş versiyon
   * @param {string} fieldName - Alanın adı
   */
  const handleInputBlur = useCallback((fieldName) => {
    setFocusedField(null);
    
    // Alanı tek bir state güncellemesinde dokunulmuş olarak işaretle
    setTouched(prev => ({ ...prev, [fieldName]: true }));
    
    // Validation helpers - validation mantığını daha okunabilir yapıyoruz
    const validators = {
      title: () => {
        const isValid = title.trim().length >= 3;
        return { title: !isValid };
      },
      amount: () => {
        const amountValue = parseFloat(amount.replace(/[^\d,.]/g, '').replace(',', '.'));
        const isValid = !isNaN(amountValue) && amountValue > 0;
        return { amount: !isValid };
      },
      paymentDay: () => {
        if (recurrenceType !== 'monthly') return {};
        const day = parseInt(paymentDay, 10);
        const isValid = !isNaN(day) && day >= 1 && day <= 31;
        return { paymentDay: !isValid };
      },
      recurrenceInterval: () => {
        const interval = parseInt(recurrenceInterval, 10);
        const isValid = !isNaN(interval) && interval >= 1 && interval <= 99;
        return { recurrenceInterval: !isValid };
      },
      notificationDaysBefore: () => {
        if (!notificationEnabled) return {};
        const days = parseInt(notificationDaysBefore, 10);
        const isValid = !isNaN(days) && days >= 0 && days <= 30;
        return { notificationDaysBefore: !isValid };
      }
    };
    
    // Eğer bu alan için özel bir validator varsa çalıştır
    const validator = validators[fieldName];
    if (validator) {
      const errors = validator();
      setFieldErrors(prev => ({ ...prev, ...errors }));
    }
  }, [
    title, amount, paymentDay, recurrenceType,
    recurrenceInterval, notificationDaysBefore, notificationEnabled
  ]);/**
   * Tutar değiştirme işleyicisi - para formatlaması yapar - optimize edilmiş versiyon
   * @param {string} text - Girilen tutar
   */  
  const handleAmountChange = useCallback((text) => {
    // Format in a cleaner, chained way
    const formattedText = text.replace(/[^\d,.]/g, '').replace(',', '.').replace(/\.(?=.*\.)/g, '');
    
    // Update the state with formatted value
    setAmount(formattedText);
    
    // Mark field as touched and validate in one operation
    setTouched(prev => ({ ...prev, amount: true }));
    
    // Immediate validation with safer parsing
    const amountValue = parseSafeAmount(formattedText);
    const isValid = amountValue > 0;
    setFieldErrors(prev => ({ ...prev, amount: !isValid }));
  }, [parseSafeAmount]);
  

  /**
   * Formu doğrular - optimize edilmiş ve önbelleğe alınmış versiyon
   * @returns {boolean} Form geçerli mi?
   */
  const validateForm = useCallback(() => {
    // Validation helpers
    const validateTitle = () => {
      if (!title.trim()) return '• Başlık alanı boş olamaz';
      if (title.trim().length < 3) return '• Başlık en az 3 karakter olmalıdır';
      return null;
    };

    const validateAmount = () => {
      if (!amount.trim()) return '• Tutar alanı boş olamaz';
      const amountValue = parseFloat(amount.replace(/[^\d,.]/g, '').replace(',', '.'));
      if (isNaN(amountValue) || amountValue <= 0) return '• Geçerli bir tutar girilmelidir (> 0)';
      return null;
    };

    const validateRecurrence = () => {
      const errors = [];
      
      if (recurrenceType === 'monthly') {
        if (!paymentDay.trim()) {
          errors.push('• Aylık tekrarlada ödeme günü belirtilmelidir');
        } else {
          const day = parseInt(paymentDay, 10);
          if (isNaN(day) || day < 1 || day > 31) {
            errors.push('• Geçerli bir ödeme günü girilmelidir (1-31)');
          }
        }
      }
      
      if (!recurrenceInterval.trim()) {
        errors.push('• Tekrarlama aralığı belirtilmelidir');
      } else {
        const interval = parseInt(recurrenceInterval, 10);
        if (isNaN(interval) || interval < 1 || interval > 99) {
          errors.push('• Geçerli bir tekrarlama aralığı girilmelidir (1-99)');
        }
      }
      
      return errors;
    };

    const validateNotifications = () => {
      if (!notificationEnabled) return [];
      
      const errors = [];
      if (!notificationDaysBefore.trim()) {
        errors.push('• Bildirim gün sayısı belirtilmelidir');
      } else {
        const days = parseInt(notificationDaysBefore, 10);
        if (isNaN(days) || days < 0 || days > 30) {
          errors.push('• Geçerli bir bildirim gün sayısı girmelisiniz (0-30)');
        }
      }
      
      return errors;
    };

    // Hata alanlarını temizle
    const fieldErrors = {};
    const errors = [];
    
    // Tüm alanları dokunulmuş olarak işaretle (tam doğrulama için)
    setAllFieldsValidated(true);
    
    // Temel alanları doğrula
    const titleError = validateTitle();
    if (titleError) {
      errors.push('📋 Temel Bilgiler:');
      errors.push(titleError);
      fieldErrors.title = true;
    }
    
    const amountError = validateAmount();
    if (amountError) {
      if (errors.length === 0) errors.push('📋 Temel Bilgiler:');
      errors.push(amountError);
      fieldErrors.amount = true;
    }
    
    // Tekrarlama ayarlarını doğrula
    const recurrenceErrors = validateRecurrence();
    if (recurrenceErrors.length > 0) {
      errors.push('🔄 Tekrarlama Ayarları:');
      errors.push(...recurrenceErrors);
      
      if (recurrenceType === 'monthly' && (!paymentDay.trim() || isNaN(parseInt(paymentDay, 10)))) {
        fieldErrors.paymentDay = true;
      }
      
      if (!recurrenceInterval.trim() || isNaN(parseInt(recurrenceInterval, 10))) {
        fieldErrors.recurrenceInterval = true;
      }
    }
    
    // Bildirim ayarlarını doğrula
    const notificationErrors = validateNotifications();
    if (notificationErrors.length > 0) {
      errors.push('🔔 Bildirim Ayarları:');
      errors.push(...notificationErrors);
      fieldErrors.notificationDaysBefore = true;
    }
    
    // Early return pattern
    if (errors.length > 0) {
      setError(errors.join('\n'));
      setFieldErrors(fieldErrors);
      return false;
    }
    
    setError('');
    setFieldErrors({});
    return true;
  }, [
    title, amount, paymentDay, recurrenceType, recurrenceInterval,
    notificationEnabled, notificationDaysBefore
  ]);
  /**
   * Durum bilgilerini getir (renk, ikon ve metin) - optimize edilmiş versiyon
   * @param {string} statusValue - Durum değeri (active, paused, ended)
   * @returns {Object} Renk, ikon ve metin bilgilerini içeren nesne
   */
  const getStatusInfo = useCallback((statusValue) => {
    switch (statusValue) {
      case 'active':
        return {
          color: Colors.SUCCESS,
          icon: 'check-circle',
          text: 'Aktif',
          description: 'Gelir aktif olarak takip ediliyor ve bildirimler etkin.'
        };
      case 'paused':
        return {
          color: Colors.WARNING,
          icon: 'pause-circle-filled',
          text: 'Duraklatıldı',
          description: 'Gelir geçici olarak durduruldu. Bildirimler gönderilmeyecek.'
        };
      case 'ended':
        return {
          color: Colors.GRAY_500,
          icon: 'cancel',
          text: 'Sonlandırıldı',
          description: 'Gelir artık takip edilmiyor. Tekrar aktifleştirilebilir.'
        };      default:
        return {
          color: Colors.PRIMARY,
          icon: 'help',
          text: 'Bilinmiyor',
          description: 'Durum bilgisi belirtilmemiş.'
        };
    }
  }, []);

  /**
   * Düzenli gelir durumunu değiştir (aktif -> duraklatıldı -> sonlandırıldı -> aktif)
   * Optimize edilmiş versiyon - doğrudan değişim yapar, animasyon yok
   */
  const toggleStatus = useCallback(() => {
    // Durumu döngüsel olarak değiştir - sabit değerli obje içinde tanımlama
    const STATUS_FLOW = Object.freeze({
      active: 'paused',
      paused: 'ended',
      ended: 'active'
    });
      // Functional update to ensure we're always using the latest status
    setStatus(currentStatus => STATUS_FLOW[currentStatus] || 'active');  
  }, []);

  /**
   * Formu gönder - hızlı ve optimize edilmiş versiyon
   * Gereksiz animasyonlar ve titreşimler olmadan, doğrudan gönderim yapılır
   */
  const handleSubmit = useCallback(async () => {
    // Update the ref to point to this function
    handleSubmitRef.current = handleSubmit;
    // Early return pattern
    if (!validateForm()) {
      return;
    }
    
    setIsSubmitting(true);
    
    // Prepare form data in a single operation to avoid multiple state reads
    const incomeData = {
      title: title.trim(),
      amount: parseSafeAmount(amount),
      currency_code: currencyCode,
      payment_day: paymentDay ? parseInt(paymentDay, 10) : null,
      recurrence_type: recurrenceType,
      recurrence_interval: parseInt(recurrenceInterval || '1', 10),
      next_payment_date: format(nextPaymentDate, 'yyyy-MM-dd'),
      notification_settings: JSON.stringify({
        enabled: notificationEnabled,
        days_before: parseInt(notificationDaysBefore || '0', 10),
        time: format(notificationTime, 'HH:mm'),
      }),
      status,
      notes: notes ? notes.trim() : '',
    };
    
    try {
      // Using async/await for better readability
      if (isEditMode) {
        await updateRegularIncome({ id: incomeId, ...incomeData });
      } else {
        await addRegularIncome(incomeData);
      }
        // Navigate based on onReturn parameter or go back
      const onReturn = route.params?.onReturn;
      if (onReturn) {
        // If onReturn is specified, navigate to that tab
        navigation.navigate('Salaries', { activeTab: onReturn });
      } else {
        // Otherwise just go back
        navigation.goBack();
      }
    } catch (err) {
      console.error('Error saving income:', err);
      
      // Show simplified error message without animations
      setError(isEditMode 
        ? `Gelir güncellenirken hata oluştu: ${err.message || 'Bilinmeyen hata'}`
        : `Gelir eklenirken hata oluştu: ${err.message || 'Bilinmeyen hata'}`
      );
    } finally {
      setIsSubmitting(false);
    }  }, [
    validateForm, title, amount, currencyCode, paymentDay, recurrenceType, 
    recurrenceInterval, nextPaymentDate, notificationEnabled, notificationDaysBefore, 
    notificationTime, status, notes, isEditMode, incomeId, navigation, route.params
  ]);

  /**
   * @type {Array<{label: string, value: string}>}
   */
  const currencyOptions = useMemo(() => [
    { label: 'TRY (Türk Lirası)', value: 'TRY' },
    { label: 'USD (ABD Doları)', value: 'USD' },
    { label: 'EUR (Euro)', value: 'EUR' },
    { label: 'GBP (İngiliz Sterlini)', value: 'GBP' },
  ], []);

  /**
   * Tekrarlama tipi seçenekleri
   * @type {Array<{label: string, value: string}>}
   */
  const recurrenceTypeOptions = useMemo(() => [
    { label: 'Aylık', value: 'monthly' },
    { label: 'Haftalık', value: 'weekly' },
    { label: 'Günlük', value: 'daily' },
    { label: 'Yıllık', value: 'yearly' },
    { label: 'Özel', value: 'custom' },
  ], []);
  // Mevcut durum bilgisini al
  const statusInfo = useMemo(() => getStatusInfo(status), [status]);

  /**
   * Form alanlarının stil durumunu belirler (normal, odaklanmış, hata)
   * Modern tasarım prensipleriyle geliştirilmiş versiyon
   * @param {string} fieldName - Form alanının adı
   * @returns {Object} Stil nesnesi
   */
  const getInputStyle = useCallback((fieldName) => {
    // Base style - her zaman uygulanacak temel stil
    const baseStyle = [styles.input];
    
    // Hata durumu kontrolü - en yüksek öncelik
    if (fieldErrors?.[fieldName] || (touched[fieldName] && isFieldEmpty(fieldName))) {
      return [styles.input, styles.inputError];
    }
    
    // Odaklanma durumu kontrolü
    if (focusedField === fieldName) {
      return [styles.input, styles.inputFocused];
    }
    
    // Başarılı doğrulama durumu (opsiyonel - pozitif feedback)
    if (touched[fieldName] && !fieldErrors?.[fieldName] && !isFieldEmpty(fieldName)) {
      return [
        styles.input,
        {
          borderColor: Colors.SUCCESS,
          backgroundColor: `${Colors.SUCCESS}08`,
        }
      ];
    }
    
    return baseStyle;
  }, [fieldErrors, focusedField, touched]);

  /**
   * Alanın boş olup olmadığını kontrol eder
   * @param {string} fieldName - Kontrol edilecek alan adı
   * @returns {boolean} Alan boş mu?
   */
  const isFieldEmpty = useCallback((fieldName) => {
    const emptyChecks = {
      title: !title?.trim(),
      amount: !amount?.trim(),
      paymentDay: recurrenceType === 'monthly' && !paymentDay?.trim(),
      recurrenceInterval: !recurrenceInterval?.trim(),
      notificationDaysBefore: notificationEnabled && !notificationDaysBefore?.trim(),
    };
    
    return emptyChecks[fieldName] || false;
  }, [title, amount, paymentDay, recurrenceType, recurrenceInterval, notificationEnabled, notificationDaysBefore]);

  /**
   * Para birimini formatlar - optimize edilmiş versiyon
   * @returns {string} Formatlanmış miktar
   */
  const getFormattedAmount = useMemo(() => {
    if (!amount || isNaN(parseFloat(amount.replace(/[^\d,.]/g, '').replace(',', '.')))) {
      return '';
    }
    const amountValue = parseFloat(amount.replace(/[^\d,.]/g, '').replace(',', '.'));
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: currencyCode,
      minimumFractionDigits: 2
    }).format(amountValue);
  }, [amount, currencyCode]);
  /**
   * Belirli bir alan için hata mesajını gösterir - optimize edilmiş versiyon
   * @param {string} fieldName - Form alanının adı
   * @param {string} message - Gösterilecek hata mesajı
   * @returns {JSX.Element|null} Hata mesajı veya null
   */
  const getFieldErrorText = useCallback((fieldName, message) => {
    // Daha verimli yaklaşım - erken dönüş (early return) ile gereksiz işlemleri engelleme
    if (!touched[fieldName] && !allFieldsValidated) {
      return null;
    }
    
    // Doğrulama hatası varsa göster
    if (fieldErrors?.[fieldName]) {
      return (
        <View style={styles.fieldErrorContainer}>
          <MaterialIcons name="error-outline" size={16} color={Colors.DANGER} />
          <Text style={styles.fieldErrorText}>{message}</Text>
        </View>
      );
    }
    
    // Boş alan kontrolü için lookup tablo - switch case yerine daha verimli
    const emptyValues = {
      title: !title,
      amount: !amount,
      paymentDay: !paymentDay && recurrenceType === 'monthly',
      recurrenceInterval: !recurrenceInterval,
      notificationDaysBefore: !notificationDaysBefore && notificationEnabled,
      notes: false // Notes alanı opsiyonel olduğundan boşsa hata vermiyoruz
    };
    
    if (emptyValues[fieldName]) {
      return (
        <View style={styles.fieldErrorContainer}>
          <MaterialIcons name="error-outline" size={16} color={Colors.DANGER} />
          <Text style={styles.fieldErrorText}>
            {message || `Bu alan gereklidir`}
          </Text>
        </View>
      );
    }
    
    return null;
  }, [
    touched, allFieldsValidated, fieldErrors, title, amount, 
    paymentDay, recurrenceType, recurrenceInterval, 
    notificationDaysBefore, notificationEnabled
  ]);
  /**
   * Form durumu bilgisini döndürür - optimize edilmiş versiyon
   * @returns {Object} Form durumu bilgisi
   */
  const getFormStatusInfo = useCallback(() => {
    if (isSubmitting) {
      return {
        icon: 'hourglass-top',
        color: Colors.PRIMARY,
        text: isEditMode ? 'Güncelleniyor...' : 'Kaydediliyor...',
        description: 'İşleminiz devam ediyor, lütfen bekleyin.'
      };
    }
    
    if (error) {
      return {
        icon: 'error-outline',
        color: Colors.DANGER,
        text: 'Hatalar Var',
        description: 'Lütfen formdaki hataları düzeltin ve tekrar deneyin.'
      };
    }
    
    // Form doğrulaması için basit bir kontrol
    const isFormValid = title && amount && (
      recurrenceType !== 'monthly' || 
      (recurrenceType === 'monthly' && paymentDay)
    );
    
    if (isFormValid) {
      return {
        icon: 'check-circle-outline',
        color: Colors.SUCCESS,
        text: 'Form Hazır',
        description: isEditMode 
          ? 'Değişikliklerinizi kaydetmek için "Güncelle" tuşuna basın.' 
          : 'Bilgileri kaydetmek için "Kaydet" tuşuna basın.'
      };
    }
    
    return {
      icon: 'edit',
      color: Colors.GRAY_500,
      text: 'Form Düzenleniyor',
      description: 'Lütfen gerekli alanları doldurun.'
    };
  }, [isSubmitting, error, title, amount, recurrenceType, paymentDay, isEditMode]);
  /**
   * Form özeti bilgisini render eder - optimize edilmiş versiyon
   * @returns {JSX.Element} Form özeti
   */  // Tekrarlama tip çevirileri için sabit lookup tablo
  const RECURRENCE_TYPE_LABELS = useMemo(() => ({
    monthly: 'Aylık',
    weekly: 'Haftalık',
    daily: 'Günlük',
    yearly: 'Yıllık',
    custom: 'Özel'
  }), []);

  // Summary komponenti JSX'i - renderSummary yerine direkt olarak JSX dönüyoruz
  const summarySection = useMemo(() => {
    // Early return pattern - veri yoksa render etmeye gerek yok
    if (!title || !amount) {
      return null;
    }
    
    // Tekrarlama bilgisini hazırla
    const recurrenceLabel = recurrenceType === 'monthly' 
      ? `${RECURRENCE_TYPE_LABELS.monthly} (${paymentDay || '?'}. gün)`
      : RECURRENCE_TYPE_LABELS[recurrenceType] || 'Özel';
    
    const intervalInfo = recurrenceInterval !== '1' ? `, ${recurrenceInterval} birim arayla` : '';
    
    return (      <View style={styles.summaryContainer}>
        <View style={styles.summaryHeader}>
          <MaterialIcons name="assignment" size={20} color={Colors.PRIMARY} />
          <Text style={styles.summaryTitle}>Özet Bilgi</Text>
        </View><View style={{marginBottom: 5}}>
            <Text style={[styles.summaryText, {marginBottom: 4}]}>
              <Text style={styles.summaryLabel}>Gelir Adı:</Text> {title}
            </Text>
            <Text style={[styles.summaryText, {marginBottom: 4}]}>
              <Text style={styles.summaryLabel}>Miktar:</Text> {getFormattedAmount}
            </Text>
            <Text style={styles.summaryText}>
              <Text style={styles.summaryLabel}>Tekrarlama:</Text> {recurrenceLabel}{intervalInfo}
            </Text>
          </View>
      </View>
    );
  }, [title, amount, getFormattedAmount, recurrenceType, paymentDay, recurrenceInterval, RECURRENCE_TYPE_LABELS]);
  // Optimize edilmiş render - modern tasarım ve daha iyi performans
  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      style={styles.container}
      keyboardVerticalOffset={Platform.OS === "ios" ? 88 : 0}
    >
      {/* Background gradient overlay for modern look */}
      <View style={styles.backgroundGradient} />
      
      <ScrollView
        style={{ flex: 1 }}
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
        bounces={true}
        overScrollMode="auto"
      >
        {/* Error Display - Modern alert style */}
        {error && (
          <RegularIncomeErrorDisplay 
            message={error} 
            onDismiss={() => setError('')} 
          />
        )}

        {/* Status Display Card */}
        <RegularIncomeFormStatus
          formStatus={{
            icon: status === 'active' ? 'check-circle' : status === 'paused' ? 'pause-circle-filled' : 'cancel',
            color: status === 'active' ? Colors.SUCCESS : status === 'paused' ? Colors.WARNING : Colors.DANGER,
            text: status === 'active' ? 'Aktif' : status === 'paused' ? 'Duraklatıldı' : 'Sonlandırıldı',
            description: status === 'active' 
              ? 'Gelir aktif olarak takip ediliyor ve bildirimler etkin.' 
              : status === 'paused' 
                ? 'Gelir geçici olarak durduruldu. Bildirimler gönderilmeyecek.' 
                : 'Gelir sonlandırıldı ve artık izlenmeyecek.'
          }}
          isEditMode={isEditMode}
        />

        {/* Interactive Status Toggle */}
        <View style={styles.statusBadgeContainer}>
          <RegularIncomeStatusBadge 
            status={status}
            toggleStatus={toggleStatus}
            statusInfo={{
              active: {
                color: Colors.SUCCESS,
                icon: 'check-circle',
                text: 'Aktif',
                description: 'Gelir aktif olarak takip ediliyor ve bildirimler etkin.'
              },
              paused: {
                color: Colors.WARNING,
                icon: 'pause-circle-filled',
                text: 'Duraklatıldı',
                description: 'Gelir geçici olarak durduruldu. Bildirimler gönderilmeyecek.'
              },
              ended: {
                color: Colors.DANGER,
                icon: 'cancel',
                text: 'Sonlandırıldı',
                description: 'Gelir sonlandırıldı ve artık izlenmeyecek.'
              }
            }[status]}
          />
        </View>

        {/* Basic Information Section */}
        <RegularIncomeBasicInfo
          title={title}
          setTitle={setTitle}
          amount={amount}
          handleAmountChange={handleAmountChange}
          currencyCode={currencyCode}
          setCurrencyCode={setCurrencyCode}
          fieldErrors={fieldErrors}
          handleInputFocus={handleInputFocus}
          handleInputBlur={handleInputBlur}
          getInputStyle={getInputStyle}
          getFieldErrorText={getFieldErrorText}
          getFormattedAmount={getFormattedAmount}
        />

        {/* Recurrence Configuration Section */}
        <RegularIncomeRecurrenceSettings 
          recurrenceType={recurrenceType}
          setRecurrenceType={setRecurrenceType}
          recurrenceInterval={recurrenceInterval}
          setRecurrenceInterval={setRecurrenceInterval}
          paymentDay={paymentDay}
          setPaymentDay={setPaymentDay}
          nextPaymentDate={nextPaymentDate}
        />
        
        {/* Next payment date section - improved with card style */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <View style={styles.sectionTitleContainer}>
              <View style={styles.sectionIconContainer}>
                <MaterialIcons name="date-range" size={20} color={Colors.PRIMARY} />
              </View>
              <Text style={styles.sectionTitleText}>Sonraki Ödeme Tarihi</Text>
            </View>
            <View style={styles.sectionHeaderDivider} />
          </View>
          
          <TouchableOpacity
            style={[styles.datePickerButton, {
              borderColor: showDatePicker ? Colors.PRIMARY : Colors.GRAY_200,
              backgroundColor: showDatePicker ? `${Colors.PRIMARY}08` : Colors.WHITE,
            }]}
            onPress={() => setShowDatePicker(true)}
            activeOpacity={0.7}
          >
            <View style={styles.dateDisplayContainer}>
              <MaterialIcons name="event" size={20} color={Colors.PRIMARY} />
              <Text style={styles.dateText}>
                {format(nextPaymentDate, 'd MMMM yyyy', {locale: tr})}
              </Text>
            </View>
            <MaterialIcons name="keyboard-arrow-down" size={24} color={Colors.PRIMARY} />
          </TouchableOpacity>

          {showDatePicker && (
            <DateTimePicker
              value={nextPaymentDate}
              mode="date"
              display="default"
              onChange={handleDateChange}
              minimumDate={new Date()}
            />
          )}
          
          {/* Future payments preview */}
          <View style={styles.futureDatesContainer}>
            <View style={styles.futureDatesHeader}>
              <MaterialIcons name="update" size={16} color={Colors.PRIMARY} />
              <Text style={styles.futureDatesTitle}>Gelecek Ödemeler</Text>
            </View>
            
            <View style={styles.futureDatesList}>
              {calculateFuturePaymentDates.map((date, index) => (
                <View key={index} style={styles.futureDateItem}>
                  <MaterialIcons name="event-available" size={14} color={Colors.SUCCESS} style={{marginRight: 6}} />
                  <Text style={styles.futureDateText}>
                    {format(date, 'd MMMM yyyy', {locale: tr})}
                  </Text>
                </View>
              ))}
            </View>
          </View>
        </View>
      
        {/* Notification settings */}
        <RegularIncomeNotificationSettings
          notificationEnabled={notificationEnabled}
          setNotificationEnabled={setNotificationEnabled}
          notificationDaysBefore={notificationDaysBefore}
          setNotificationDaysBefore={setNotificationDaysBefore}
          notificationTime={notificationTime}
          setNotificationTime={setNotificationTime}
          showTimePicker={showTimePicker}
          setShowTimePicker={setShowTimePicker}
          handleTimeChange={handleTimeChange}
          getInputStyle={getInputStyle}
          handleInputFocus={handleInputFocus}
          handleInputBlur={handleInputBlur}
          getFieldErrorText={getFieldErrorText}
          fieldErrors={fieldErrors}
        />
        
        {/* Additional info (notes) */}
        <RegularIncomeAdditionalInfo
          notes={notes}
          setNotes={setNotes}
          getInputStyle={getInputStyle}
        />
        
        {/* Action buttons - only show if there's no header button */}
        {Platform.OS !== 'ios' && (
          <RegularIncomeActionButtons
            isEditMode={isEditMode}
            isSubmitting={isSubmitting}
            handleSubmit={handleSubmit}
            handleCancel={() => navigation.goBack()}
          />
        )}
        
        {/* Render loading spinner if submitting */}
        {isSubmitting && <RegularIncomeLoadingSpinner />}
      </ScrollView>

      {/* Future Payments Modal - Enhanced */}
      {showFuturePaymentsModal && (
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <View style={styles.modalHeaderContent}>
                <MaterialIcons name="date-range" size={24} color={Colors.PRIMARY} style={{marginRight: 8}} />
                <Text style={styles.modalTitle}>Gelecek Ödemeler</Text>
              </View>
              <TouchableOpacity 
                onPress={() => setShowFuturePaymentsModal(false)} 
                style={styles.closeButton}
                activeOpacity={0.7}
              >
                <MaterialIcons name="close" size={24} color={Colors.GRAY_600} />
              </TouchableOpacity>
            </View>
            
            <View style={styles.modalContent}>
              <View style={styles.currentPaymentContainer}>
                <MaterialIcons name="today" size={22} color={Colors.SUCCESS} style={styles.currentPaymentIcon} />
                <View style={styles.currentPaymentTextContainer}>
                  <Text style={styles.currentPaymentLabel}>İlk Ödeme Tarihi</Text>
                  <Text style={styles.currentPaymentDate}>
                    {format(nextPaymentDate, 'd MMMM yyyy', {locale: tr})}
                  </Text>
                </View>
              </View>
              
              <View style={styles.divider} />
              
              <Text style={styles.futurePaymentsTitle}>Gelecek Ödemeler</Text>
              
              {calculateFuturePaymentDates.length > 0 ? (
                calculateFuturePaymentDates.map((date, index) => (
                  <View key={index} style={styles.futurePaymentItem}>
                    <View style={styles.paymentDateBadge}>
                      <Text style={styles.paymentDateNumber}>{index + 1}</Text>
                    </View>
                    <View style={styles.futurePaymentDateContainer}>
                      <Text style={styles.futurePaymentDay}>{format(date, 'd', {locale: tr})}</Text>
                      <Text style={styles.futurePaymentMonthYear}>
                        {format(date, 'MMMM yyyy', {locale: tr})}
                      </Text>
                    </View>
                    <View style={styles.daysLeftContainer}>
                      <Text style={styles.daysLeftText}>
                        {Math.floor((date - new Date()) / (1000 * 60 * 60 * 24))} gün kaldı
                      </Text>
                    </View>
                  </View>
                ))
              ) : (
                <View style={styles.noPaymentsContainer}>
                  <MaterialIcons name="error-outline" size={24} color={Colors.WARNING} />
                  <Text style={styles.noPaymentsText}>Gelecek ödeme bilgisi bulunamadı.</Text>
                </View>
              )}
              
              <View style={styles.paymentNoteContainer}>
                <MaterialIcons name="info-outline" size={18} color={Colors.GRAY_600} />
                <Text style={styles.paymentNote}>
                  Ödeme tarihleri seçtiğiniz tekrarlama ayarlarına göre hesaplanmıştır.
                </Text>
              </View>
            </View>
            
            <TouchableOpacity 
              style={styles.modalCloseButton}
              onPress={() => setShowFuturePaymentsModal(false)}
              activeOpacity={0.8}
            >
              <MaterialIcons name="check" size={18} color="#fff" style={{marginRight: 8}} />
              <Text style={styles.modalButtonText}>Anladım</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}
    </KeyboardAvoidingView>
  );
});

/**
 * Modern ve responsive stil tanımları
 * Material Design 3.0 prensiplerine uygun tasarım
 * Enhanced with improved visual hierarchy and modern design elements
 */
const styles = StyleSheet.create({
  // === Temel Konteyner Stilleri ===
  container: {
    flex: 1,
    backgroundColor: Colors.GRAY_50,
  },
  backgroundGradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 160,
    backgroundColor: Colors.PRIMARY,
    opacity: 0.06,
    zIndex: 0,
  },
  contentContainer: {
    flexGrow: 1,
    paddingHorizontal: 14,
    paddingTop: 6,
    paddingBottom: Platform.OS === 'ios' ? 70 : 60,
    zIndex: 1,
  },

  // === Header Button Stilleri - Enhanced ===
  headerButtonContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    paddingHorizontal: 2,
  },
  headerIconButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: `${Colors.PRIMARY}12`,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: Colors.PRIMARY,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.12,
    shadowRadius: 3,
    elevation: 2,
    borderWidth: 1,
    borderColor: `${Colors.PRIMARY}15`,
  },
  headerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.PRIMARY,
    paddingHorizontal: 14,
    paddingVertical: 8,
    borderRadius: 18,
    shadowColor: Colors.PRIMARY,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 3,
    minWidth: 70,
  },
  headerButtonText: {
    color: Colors.WHITE,
    fontSize: 13,
    fontWeight: '600',
    marginLeft: 4,
  // === Section Container Stilleri - Enhanced ===
  section: {
    backgroundColor: Colors.WHITE,
    borderRadius: 14,
    marginBottom: 16,
    paddingVertical: 16,
    paddingHorizontal: 14,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.06,
    shadowRadius: 8,
    elevation: 4,
    borderWidth: 1,
    borderColor: Colors.GRAY_100,
  },
  sectionHeader: {
    marginBottom: 14,
  },
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sectionIconContainer: {
    width: 36,
  sectionIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: `${Colors.PRIMARY}10`,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
    shadowColor: Colors.PRIMARY,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.08,
    shadowRadius: 2,
    elevation: 1,
    borderWidth: 1,
  },
  sectionTitleText: {
    fontSize: 17,
    fontWeight: '700',
    color: Colors.GRAY_900,
    letterSpacing: 0.1,
    flex: 1,
    lineHeight: 22,
  },
  sectionHeaderDivider: {
    height: 1.5,
    backgroundColor: `${Colors.PRIMARY}12`,
    marginTop: 10,
    borderRadius: 1,
    shadowColor: Colors.PRIMARY,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.06,
    shadowRadius: 1,
    elevation: 1,
  },

  // === Input Stilleri - Enhanced ===
  input: {
    borderWidth: 1,
    borderColor: Colors.GRAY_200,
    borderRadius: 10,
    paddingHorizontal: 14,
    paddingVertical: 12,
    fontSize: 15,
    backgroundColor: Colors.WHITE,
    marginVertical: 4,
    color: Colors.GRAY_900,
    fontWeight: '500',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.03,
    shadowRadius: 2,
    elevation: 1,
  },
  inputFocused: {
    borderColor: Colors.PRIMARY,
    backgroundColor: `${Colors.PRIMARY}04`,
    shadowColor: Colors.PRIMARY,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.12,
    shadowRadius: 3,
    elevation: 2,
  },
  inputError: {
    borderColor: Colors.DANGER,
    backgroundColor: `${Colors.DANGER}04`,
    shadowColor: Colors.DANGER,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.12,
    shadowRadius: 3,
    elevation: 2,
  },

  // === Field Error Container ===
  fieldErrorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    marginBottom: 12,
    backgroundColor: `${Colors.DANGER}10`,
    paddingVertical: 10,
    paddingHorizontal: 14,
    borderRadius: 10,
    borderLeftWidth: 3,
    borderLeftColor: Colors.DANGER,
    shadowColor: Colors.DANGER,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.12,
    shadowRadius: 3,
    elevation: 2,
  },
  fieldErrorText: {
    fontSize: 14,
    color: Colors.DANGER,
    marginLeft: 10,
    fontWeight: '600',
    flex: 1,
    lineHeight: 18,
  },.DANGER,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.12,
    shadowRadius: 3,
    elevation: 2,0,
    fontWeight: '600',
    flex: 1,
    lineHeight: 18,
  },
  // === Date Picker Stilleri - Enhanced ===
  datePickerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1.2,
    borderColor: Colors.GRAY_200,
    borderRadius: 12,
    padding: 16,
    marginVertical: 8,
    backgroundColor: Colors.WHITE,
    minHeight: 52,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.06,
    shadowRadius: 4,
    elevation: 3,
  },
  dateDisplayContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  dateText: {
    fontSize: 16,
    color: Colors.GRAY_900,
    marginLeft: 12,
    fontWeight: '600',
    letterSpacing: 0.1,
  },alignItems: 'center',
    flex: 1,
  // === Future Dates Stilleri - Enhanced ===
  futureDatesContainer: {
    marginTop: 18,
    backgroundColor: Colors.PRIMARY,
    backgroundOpacity: 0.08,
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: Colors.PRIMARY,
    borderOpacity: 0.2,
    shadowColor: Colors.PRIMARY,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  futureDatesHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    paddingBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: Colors.PRIMARY,
    borderBottomOpacity: 0.15,
  },
  futureDatesTitle: {
    fontSize: 15,
    fontWeight: '700',
    color: Colors.PRIMARY,
    marginLeft: 8,
    letterSpacing: 0.1,
  },
  futureDatesList: {
    gap: 8,
  },
  futureDateItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.WHITE,
    paddingVertical: 12,
    paddingHorizontal: 14,
  futureDateItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.WHITE,
    paddingVertical: 12,
    paddingHorizontal: 14,
    borderRadius: 10,
    borderLeftWidth: 3,
    borderLeftColor: Colors.SUCCESS,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.06,
    shadowRadius: 4,
    elevation: 3,
    borderWidth: 1,
    borderColor: `${Colors.SUCCESS}15`,er Stilleri - Enhanced ===
  summaryContainer: {
    backgroundColor: Colors.SUCCESS,
    backgroundOpacity: 0.1,
    borderRadius: 14,
    padding: 18,
  summaryContainer: {
    backgroundColor: `${Colors.SUCCESS}10`,
    borderRadius: 14,
    padding: 18,
    marginVertical: 16,
    borderLeftWidth: 4,
    borderLeftColor: Colors.SUCCESS,
    shadowColor: Colors.SUCCESS,
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.12,
    shadowRadius: 6,
    elevation: 4,
    borderWidth: 1,
    borderColor: `${Colors.SUCCESS}20`,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.SUCCESS,
  summaryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 14,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: `${Colors.SUCCESS}20`,
  },
  summaryText: {
    fontSize: 15,
    color: Colors.GRAY_800,
    lineHeight: 22,
    fontWeight: '500',
  },
  summaryLabel: {
    fontWeight: '800',
  // === Status Badge Container - Enhanced ===
  statusBadgeContainer: {
    marginVertical: 20,
    paddingHorizontal: 16,
    alignItems: 'center',
  },mmaryText: {
    fontSize: 16,
  // === Modal Stilleri - Enhanced ===
  modalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.65)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  modalContainer: {
    width: '92%',
    maxWidth: 400,
    backgroundColor: Colors.WHITE,
    borderRadius: 18,
    overflow: 'hidden',
    elevation: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: Colors.PRIMARY,
    backgroundOpacity: 0.08,
    paddingVertical: 16,
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: `${Colors.PRIMARY}08`,
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: Colors.GRAY_100,
    height: 36,
    borderRadius: 18,
    backgroundColor: Colors.GRAY_100,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.08,
    shadowRadius: 2,
    elevation: 1,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '800',
    color: Colors.GRAY_900,
    marginLeft: 10,
    letterSpacing: 0.2,
  },
  modalContent: {
    padding: 20,
  },alignItems: 'center',
  // === Current Payment Container - Enhanced ===
  currentPaymentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.SUCCESS,
    backgroundOpacity: 0.12,
    borderRadius: 14,
    padding: 16,
    marginBottom: 18,
  currentPaymentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: `${Colors.SUCCESS}12`,
    borderRadius: 14,
    padding: 16,
    marginBottom: 18,
    borderWidth: 1,
    borderColor: `${Colors.SUCCESS}25`,
  },
  currentPaymentTextContainer: {
    flex: 1,
  },
  currentPaymentLabel: {
    fontSize: 14,
    color: Colors.SUCCESS,
    marginBottom: 4,
    fontWeight: '700',
    letterSpacing: 0.1,
  },
  currentPaymentDate: {
    fontSize: 17,
    fontWeight: '800',
    color: Colors.GRAY_900,
    letterSpacing: 0.1,
  // === Divider - Enhanced ===
  divider: {
    height: 1.5,
    backgroundColor: Colors.GRAY_200,
    marginVertical: 18,
    borderRadius: 1,
  },

  // === Future Payments Modal - Enhanced ===
  futurePaymentsTitle: {
    fontSize: 16,
    fontWeight: '800',
    color: Colors.GRAY_900,
    marginBottom: 16,
    letterSpacing: 0.2,
  },
  futurePaymentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 14,
    backgroundColor: Colors.GRAY_50,
    borderRadius: 12,
    padding: 16,
    borderLeftWidth: 3,
    borderLeftColor: Colors.PRIMARY,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.06,
    shadowRadius: 4,
    elevation: 3,
    borderWidth: 1,
    borderColor: Colors.PRIMARY,
    borderOpacity: 0.15,
  },
  paymentDateBadge: {
    width: 32,
    height: 32,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: `${Colors.PRIMARY}15`,,
    marginRight: 14,
    shadowColor: Colors.PRIMARY,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 2,
  },
  paymentDateNumber: {
    color: Colors.WHITE,
    fontWeight: '800',
    fontSize: 14,
  },
  futurePaymentDateContainer: {
    flex: 1,
  },
  futurePaymentDay: {
    fontSize: 16,
    fontWeight: '800',
    color: Colors.GRAY_900,
    marginBottom: 2,
    letterSpacing: 0.1,
  },
  futurePaymentMonthYear: {
    fontSize: 14,
    color: Colors.GRAY_600,
    fontWeight: '600',
    letterSpacing: 0.05,
  },
  daysLeftContainer: {
    backgroundColor: Colors.PRIMARY,
    backgroundOpacity: 0.2,
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 12,
    shadowColor: Colors.PRIMARY,
    shadowOffset: { width: 0, height: 1 },
  daysLeftContainer: {
    backgroundColor: `${Colors.PRIMARY}20`,
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 12,
    shadowColor: Colors.PRIMARY,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.08,
    shadowRadius: 1,
    elevation: 1,
  },backgroundColor: Colors.WARNING,
    backgroundOpacity: 0.12,
    borderRadius: 12,
    padding: 18,
    marginVertical: 16,
    borderWidth: 1,
    borderColor: Colors.WARNING,
  // === No Payments Container - Enhanced ===
  noPaymentsContainer: {
    alignItems: 'center',
    backgroundColor: `${Colors.WARNING}12`,
    borderRadius: 12,
    padding: 18,
    marginVertical: 16,
    borderWidth: 1,
    borderColor: `${Colors.WARNING}25`,
    shadowColor: Colors.WARNING,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  // === Payment Note Container - Enhanced ===
  paymentNoteContainer: {
    flexDirection: 'row',
    backgroundColor: Colors.INFO,
    backgroundOpacity: 0.1,
    padding: 16,
    marginTop: 18,
    borderRadius: 12,
    alignItems: 'flex-start',
    borderLeftWidth: 3,
    borderLeftColor: Colors.INFO,
    shadowColor: Colors.INFO,
  // === Payment Note Container - Enhanced ===
  paymentNoteContainer: {
    flexDirection: 'row',
    backgroundColor: `${Colors.INFO}10`,
    padding: 16,
    marginTop: 18,
    borderRadius: 12,
    alignItems: 'flex-start',
    borderLeftWidth: 3,
    borderLeftColor: Colors.INFO,
    shadowColor: Colors.INFO,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.08,
    shadowRadius: 3,
    elevation: 2,
    borderWidth: 1,
    borderColor: `${Colors.INFO}20`,
  },dalCloseButton: {
    backgroundColor: Colors.PRIMARY,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 20,
    shadowColor: Colors.PRIMARY,
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.25,
    shadowRadius: 6,
    elevation: 4,
  },
  modalButtonText: {
    color: Colors.WHITE,
    fontSize: 16,
    fontWeight: '800',
  // === Additional Enhanced Utility Styles ===
  buttonDisabled: {
    opacity: 0.6,
    transform: [{ scale: 0.98 }],
  },
  buttonText: {
    color: Colors.WHITE,
    fontSize: 16,
    fontWeight: '800',
    marginLeft: 8,
    letterSpacing: 0.3,
  },
  errorIndicatorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    backgroundColor: Colors.DANGER,
    backgroundOpacity: 0.1,
    paddingVertical: 10,
    paddingHorizontal: 14,
    borderRadius: 10,
    borderLeftWidth: 3,
    borderLeftColor: Colors.DANGER,
    shadowColor: Colors.DANGER,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.12,
  errorIndicatorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    backgroundColor: `${Colors.DANGER}10`,
    paddingVertical: 10,
    paddingHorizontal: 14,
    borderRadius: 10,
    borderLeftWidth: 3,
    borderLeftColor: Colors.DANGER,
    shadowColor: Colors.DANGER,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.12,
    shadowRadius: 3,
    elevation: 2,
  },rorIndicator: {
    fontSize: 15,
    color: Colors.DANGER,
    marginLeft: 12,
    fontWeight: '600',
    flex: 1,
    lineHeight: 22,
  },

  // === Background Gradient - Enhanced ===
  backgroundGradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 200,
    backgroundColor: `linear-gradient(135deg, ${Colors.PRIMARY}15 0%, ${Colors.SUCCESS}10 100%)`,
    opacity: 0.3,
    zIndex: 0,
  },
});

export default RegularIncomeFormScreen;
  // === Background Gradient - Enhanced ===
  backgroundGradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 200,
    backgroundColor: `${Colors.PRIMARY}08`,
    zIndex: 0,
  },