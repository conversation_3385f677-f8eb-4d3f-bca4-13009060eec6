import React, { useState, useCallback, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  RefreshControl,
  ActivityIndicator,
  TextInput
} from 'react-native';
import { useSQLiteContext } from 'expo-sqlite';
import { useFocusEffect } from '@react-navigation/native';
import { MaterialIcons } from '@expo/vector-icons';
import { format, parseISO, differenceInDays } from 'date-fns';
import { tr } from 'date-fns/locale';
import { Colors } from '../constants/colors';
import * as reminderService from '../services/reminderService';
import * as reminderTagService from '../services/reminderTagService';
import * as reminderTemplateService from '../services/reminderTemplateService';
import * as expenseReminderService from '../services/expenseReminderService';
import * as salaryReminderService from '../services/salaryReminderService';

/**
 * Hatırlatıcılar Ekranı
 *
 * @param {Object} props - Bileşen özellikleri
 * @param {Object} props.navigation - Navigation nesnesi
 * @returns {JSX.Element} Hatırlatıcılar Ekranı
 */
export default function RemindersScreen({ navigation }) {
  const db = useSQLiteContext();

  // Durum
  const [reminders, setReminders] = useState([]);
  const [upcomingReminders, setUpcomingReminders] = useState([]);
  const [salaryReminders, setSalaryReminders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState('upcoming'); // 'upcoming', 'all', 'past', 'expenses', 'salaries'

  // Maaş hatırlatıcılarını ayrıca yükle
  const loadSalaryReminders = useCallback(async () => {
    try {
      const salaries = await salaryReminderService.getSalaryReminders(db);
      setSalaryReminders(salaries);
    } catch (error) {
      console.error('Maaş hatırlatıcıları yüklenirken hata:', error);
      setSalaryReminders([]);
    }
  }, [db]);

  // Filtreleme ve arama durumu
  const [searchText, setSearchText] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [priorityFilter, setPriorityFilter] = useState(null); // 'low', 'normal', 'high', null
  const [categoryFilter, setCategoryFilter] = useState(null);
  const [categories, setCategories] = useState([]);
  const [tagFilter, setTagFilter] = useState(null);
  const [tags, setTags] = useState([]);
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [filteredReminders, setFilteredReminders] = useState([]);

  // Verileri yükle
  const loadData = useCallback(async () => {
    try {
      setLoading(true);

      // Yaklaşan hatırlatıcıları getir
      const upcoming = await reminderService.getUpcomingReminders(db, 7);
      setUpcomingReminders(upcoming);

      // Harcama hatırlatıcılarını getir
      if (activeTab === 'expenses') {
        try {
          // Harcama hatırlatıcılarını getir
          const expenses = await expenseReminderService.getExpenseReminders(db);
          setReminders(expenses);
          setFilteredReminders(expenses); // Filtrelenmiş listeyi de güncelle
        } catch (error) {
          console.error('Harcama hatırlatıcıları yüklenirken hata:', error);
          Alert.alert('Hata', 'Harcama hatırlatıcıları yüklenirken bir hata oluştu.');
          setReminders([]);
          setFilteredReminders([]);
        }
        setLoading(false);
        return;
      }

      // Maaş hatırlatıcılarını getir
      if (activeTab === 'salaries') {
        try {
          // Maaş hatırlatıcılarını getir
          const salaries = await salaryReminderService.getSalaryReminders(db);
          setReminders(salaries);
          setFilteredReminders(salaries); // Filtrelenmiş listeyi de güncelle
        } catch (error) {
          console.error('Maaş hatırlatıcıları yüklenirken hata:', error);
          Alert.alert('Hata', 'Maaş hatırlatıcıları yüklenirken bir hata oluştu.');
          setReminders([]);
          setFilteredReminders([]);
        }
        setLoading(false);
        return;
      }

      // Tüm hatırlatıcıları getir
      const options = {
        sortBy: 'scheduled_at',
        sortOrder: activeTab === 'past' ? 'desc' : 'asc'
      };

      if (activeTab === 'upcoming') {
        options.status = 'pending';
      } else if (activeTab === 'past') {
        // Geçmiş hatırlatıcılar için özel sorgu
        const pastReminders = await db.getAllAsync(`
          SELECT * FROM notifications
          WHERE related_type = 'user_reminder'
          AND (status = 'sent' OR status = 'read' OR
              (status = 'pending' AND datetime(scheduled_at) < datetime('now')))
          ORDER BY scheduled_at DESC
          LIMIT 100
        `);

        // Sonuçları işle
        const processedReminders = pastReminders.map(reminder => {
          // Tarih ve saat bilgilerini ayır
          const scheduledDate = parseISO(reminder.scheduled_at);
          const date = format(scheduledDate, 'yyyy-MM-dd');
          const time = format(scheduledDate, 'HH:mm');

          // Tekrarlama bilgilerini parse et
          let repeatDays = null;
          let repeatMonths = null;
          let data = {};

          try {
            if (reminder.repeat_days) repeatDays = JSON.parse(reminder.repeat_days);
            if (reminder.repeat_months) repeatMonths = JSON.parse(reminder.repeat_months);
            if (reminder.data) data = JSON.parse(reminder.data);
          } catch (parseError) {
            console.error('JSON parse hatası:', parseError);
          }

          return {
            ...reminder,
            date,
            time,
            repeat_days: repeatDays,
            repeat_months: repeatMonths,
            data
          };
        });

        setReminders(processedReminders);
        setFilteredReminders(processedReminders); // Filtrelenmiş listeyi de güncelle
        setLoading(false);
        return;
      }

      const allReminders = await reminderService.getReminders(db, options);
      setReminders(allReminders);
      setFilteredReminders(allReminders); // Filtrelenmiş listeyi de güncelle

      setLoading(false);
    } catch (error) {
      console.error('Hatırlatıcıları yükleme hatası:', error);
      Alert.alert('Hata', 'Hatırlatıcılar yüklenirken bir hata oluştu.');
      setLoading(false);
    }
  }, [db, activeTab]);

  // Kategorileri yükle
  const loadCategories = useCallback(async () => {
    try {
      const result = await db.getAllAsync(`
        SELECT * FROM categories
        ORDER BY name ASC
      `);

      setCategories(result);
    } catch (error) {
      console.error('Kategorileri yükleme hatası:', error);
    }
  }, [db]);

  // Etiketleri yükle
  const loadTags = useCallback(async () => {
    try {
      const result = await reminderTagService.getAllTags(db);
      setTags(result);
    } catch (error) {
      console.error('Etiketleri yükleme hatası:', error);
    }
  }, [db]);

  // Ekran odaklandığında verileri yükle
  useFocusEffect(
    useCallback(() => {
      loadData();
      loadCategories();
      loadTags();
      loadSalaryReminders(); // Maaş hatırlatıcılarını da yükle
    }, [loadData, loadCategories, loadTags, loadSalaryReminders])
  );

  // Hatırlatıcıları filtrele
  useEffect(() => {
    if (!reminders.length) {
      setFilteredReminders([]);
      return;
    }

    let filtered = [...reminders];

    // Metin araması
    if (searchText) {
      const searchLower = searchText.toLowerCase();
      filtered = filtered.filter(reminder =>
        reminder.title.toLowerCase().includes(searchLower) ||
        (reminder.message && reminder.message.toLowerCase().includes(searchLower))
      );
    }

    // Öncelik filtresi
    if (priorityFilter) {
      filtered = filtered.filter(reminder => reminder.priority === priorityFilter);
    }

    // Kategori filtresi
    if (categoryFilter) {
      filtered = filtered.filter(reminder => reminder.category_id === categoryFilter);
    }

    // Etiket filtresi
    if (tagFilter) {
      filtered = filtered.filter(reminder => {
        if (!reminder.tags) return false;
        return reminder.tags.some(tag => tag.id === tagFilter);
      });
    }

    setFilteredReminders(filtered);
  }, [reminders, searchText, priorityFilter, categoryFilter, tagFilter]);

  // Yenileme işlemi
  const handleRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  // Hatırlatıcı detayına git
  const goToReminderDetail = (reminderId) => {
    navigation.navigate('ReminderDetail', { reminderId });
  };

  // Yeni hatırlatıcı ekle
  const addNewReminder = () => {
    Alert.alert(
      'Hatırlatıcı Ekle',
      'Nasıl bir hatırlatıcı eklemek istersiniz?',
      [
        {
          text: 'Yeni Hatırlatıcı',
          onPress: () => navigation.navigate('ReminderForm')
        },
        {
          text: 'Harcama Hatırlatıcısı',
          onPress: () => navigation.navigate('ExpenseReminderForm')
        },
        {
          text: 'Maaş Hatırlatıcısı',
          onPress: () => navigation.navigate('SalaryReminderForm')
        },
        {
          text: 'Şablondan Oluştur',
          onPress: () => navigation.navigate('ReminderTemplates')
        },
        {
          text: 'İptal',
          style: 'cancel'
        }
      ]
    );
  };

  // Hatırlatıcıyı etkinleştir/devre dışı bırak
  const toggleReminderEnabled = async (reminderId, currentState) => {
    try {
      const newState = !currentState;
      await reminderService.toggleReminderEnabled(db, reminderId, newState);

      // Listeyi güncelle
      setReminders(reminders.map(reminder => {
        if (reminder.id === reminderId) {
          return {
            ...reminder,
            is_enabled: newState ? 1 : 0
          };
        }
        return reminder;
      }));

      // Yaklaşan hatırlatıcıları da güncelle
      setUpcomingReminders(upcomingReminders.map(reminder => {
        if (reminder.id === reminderId) {
          return {
            ...reminder,
            is_enabled: newState ? 1 : 0
          };
        }
        return reminder;
      }));
    } catch (error) {
      console.error('Hatırlatıcı etkinleştirme hatası:', error);
      Alert.alert('Hata', 'Hatırlatıcı durumu değiştirilirken bir hata oluştu.');
    }
  };

  // Hatırlatıcıyı sil
  const deleteReminder = (reminderId) => {
    Alert.alert(
      'Hatırlatıcıyı Sil',
      'Bu hatırlatıcıyı silmek istediğinizden emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: async () => {
            try {
              await reminderService.deleteReminder(db, reminderId);

              // Listeyi güncelle
              setReminders(reminders.filter(reminder => reminder.id !== reminderId));
              setUpcomingReminders(upcomingReminders.filter(reminder => reminder.id !== reminderId));

              Alert.alert('Başarılı', 'Hatırlatıcı silindi.');
            } catch (error) {
              console.error('Hatırlatıcı silme hatası:', error);
              Alert.alert('Hata', 'Hatırlatıcı silinirken bir hata oluştu.');
            }
          }
        }
      ]
    );
  };

  // Hatırlatıcı öğesi
  const renderReminderItem = ({ item }) => {
    // Hatırlatıcı türünü kontrol et
    const isExpenseReminder = item.data && item.data.isExpenseReminder;
    const isSalaryReminder = item.data && item.data.isSalaryReminder;

    // Tarih ve saat bilgilerini ayır
    const scheduledDate = parseISO(item.scheduled_at);
    const formattedDate = format(scheduledDate, 'dd MMM yyyy', { locale: tr });
    const formattedTime = format(scheduledDate, 'HH:mm');

    // Kalan gün hesapla
    const today = new Date();
    const daysRemaining = differenceInDays(scheduledDate, today);

    // Öncelik rengi
    let priorityColor = Colors.WARNING;
    if (item.priority === 'low') {
      priorityColor = Colors.SUCCESS;
    } else if (item.priority === 'high') {
      priorityColor = Colors.DANGER;
    }

    // Harcama hatırlatıcısı için özel renk
    if (isExpenseReminder) {
      const expenseType = item.data.expenseType;
      if (expenseType === 'bill') {
        priorityColor = '#e74c3c'; // Kırmızı
      } else if (expenseType === 'subscription') {
        priorityColor = '#3498db'; // Mavi
      } else if (expenseType === 'regular') {
        priorityColor = '#2ecc71'; // Yeşil
      }
    }

    // Maaş hatırlatıcısı için özel renk
    if (isSalaryReminder) {
      const salaryType = item.data.salaryType;
      if (salaryType === 'regular') {
        priorityColor = '#2ecc71'; // Yeşil
      } else if (salaryType === 'bonus') {
        priorityColor = '#3498db'; // Mavi
      } else if (salaryType === 'overtime') {
        priorityColor = '#e74c3c'; // Kırmızı
      }
    }

    // Tekrarlama ikonu
    let repeatIcon = null;
    if (item.repeat_type !== 'once') {
      repeatIcon = (
        <MaterialIcons
          name={
            item.repeat_type === 'daily' ? 'today' :
            item.repeat_type === 'weekly' ? 'view-week' :
            item.repeat_type === 'monthly' ? 'date-range' :
            'event-repeat'
          }
          size={16}
          color={Colors.TEXT_LIGHT}
        />
      );
    }

    return (
      <TouchableOpacity
        style={styles.reminderItem}
        onPress={() => goToReminderDetail(item.id)}
      >
        <View style={styles.reminderHeader}>
          <View style={[styles.priorityIndicator, { backgroundColor: priorityColor }]} />
          <Text style={styles.reminderTitle} numberOfLines={1}>
            {item.title}
          </Text>
          <View style={styles.reminderActions}>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => toggleReminderEnabled(item.id, item.is_enabled === 1)}
            >
              <MaterialIcons
                name={item.is_enabled === 1 ? 'notifications-active' : 'notifications-off'}
                size={20}
                color={item.is_enabled === 1 ? Colors.PRIMARY : Colors.GRAY_500}
              />
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => deleteReminder(item.id)}
            >
              <MaterialIcons name="delete" size={20} color={Colors.DANGER} />
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.reminderContent}>
          {item.message && (
            <Text style={styles.reminderMessage} numberOfLines={2}>
              {item.message}
            </Text>
          )}

          <View style={styles.reminderInfo}>
            <View style={styles.reminderInfoItem}>
              <MaterialIcons name="event" size={16} color={Colors.TEXT_LIGHT} />
              <Text style={styles.reminderInfoText}>{formattedDate}</Text>
            </View>

            <View style={styles.reminderInfoItem}>
              <MaterialIcons name="access-time" size={16} color={Colors.TEXT_LIGHT} />
              <Text style={styles.reminderInfoText}>{formattedTime}</Text>
            </View>

            {repeatIcon && (
              <View style={styles.reminderInfoItem}>
                {repeatIcon}
                <Text style={styles.reminderInfoText}>Tekrarlı</Text>
              </View>
            )}

            {item.data && item.data.isExpenseReminder && (
              <View style={styles.reminderInfoItem}>
                <MaterialIcons
                  name={
                    item.data.expenseType === 'bill' ? 'receipt' :
                    item.data.expenseType === 'subscription' ? 'subscriptions' :
                    'attach-money'
                  }
                  size={16}
                  color={Colors.TEXT_LIGHT}
                />
                <Text style={styles.reminderInfoText}>
                  {new Intl.NumberFormat('tr-TR', {
                    style: 'currency',
                    currency: item.data.currency || 'TRY'
                  }).format(item.data.amount)}
                </Text>
              </View>
            )}

            {item.data && item.data.isSalaryReminder && (
              <View style={styles.reminderInfoItem}>
                <MaterialIcons
                  name={
                    item.data.salaryType === 'bonus' ? 'card-giftcard' :
                    item.data.salaryType === 'overtime' ? 'schedule' :
                    'account-balance-wallet'
                  }
                  size={16}
                  color={Colors.TEXT_LIGHT}
                />
                <Text style={styles.reminderInfoText}>
                  {new Intl.NumberFormat('tr-TR', {
                    style: 'currency',
                    currency: item.data.currency || 'TRY'
                  }).format(item.data.amount)}
                </Text>
              </View>
            )}
          </View>
        </View>

        {daysRemaining >= 0 ? (
          <View style={[
            styles.reminderFooter,
            daysRemaining === 0 ? styles.todayFooter :
            daysRemaining <= 1 ? styles.urgentFooter :
            daysRemaining <= 3 ? styles.soonFooter :
            styles.normalFooter
          ]}>
            <MaterialIcons
              name={
                daysRemaining === 0 ? 'today' :
                daysRemaining <= 1 ? 'priority-high' :
                'event-available'
              }
              size={16}
              color="#fff"
            />
            <Text style={styles.footerText}>
              {daysRemaining === 0 ? 'Bugün' :
               daysRemaining === 1 ? 'Yarın' :
               `${daysRemaining} gün sonra`}
            </Text>
          </View>
        ) : (
          <View style={styles.pastFooter}>
            <MaterialIcons name="event-busy" size={16} color="#fff" />
            <Text style={styles.footerText}>
              {Math.abs(daysRemaining) === 1 ? 'Dün' :
               `${Math.abs(daysRemaining)} gün önce`}
            </Text>
          </View>
        )}
      </TouchableOpacity>
    );
  };

  // Boş durum
  const renderEmptyState = () => {
    return (
      <View style={styles.emptyContainer}>
        <MaterialIcons name="notifications-none" size={64} color={Colors.GRAY_300} />
        <Text style={styles.emptyTitle}>Hatırlatıcı Bulunamadı</Text>
        <Text style={styles.emptyText}>
          {activeTab === 'upcoming'
            ? 'Yaklaşan hatırlatıcınız bulunmuyor. Yeni bir hatırlatıcı ekleyin.'
            : activeTab === 'past'
            ? 'Geçmiş hatırlatıcınız bulunmuyor.'
            : 'Henüz hatırlatıcı eklenmemiş. Yeni bir hatırlatıcı ekleyin.'}
        </Text>
        <TouchableOpacity
          style={styles.emptyButton}
          onPress={addNewReminder}
        >
          <MaterialIcons name="add" size={20} color="#fff" />
          <Text style={styles.emptyButtonText}>Yeni Hatırlatıcı</Text>
        </TouchableOpacity>
      </View>
    );
  };

  // Yaklaşan hatırlatıcılar
  const renderUpcomingReminders = () => {
    if (upcomingReminders.length === 0) {
      return null;
    }

    return (
      <View style={styles.upcomingContainer}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Yaklaşan Hatırlatıcılar</Text>
        </View>

        {upcomingReminders.map(reminder => {
          // Tarih ve saat bilgilerini ayır
          const scheduledDate = parseISO(reminder.scheduled_at);
          const formattedDate = format(scheduledDate, 'dd MMM', { locale: tr });
          const formattedTime = format(scheduledDate, 'HH:mm');

          // Kalan gün hesapla
          const today = new Date();
          const daysRemaining = differenceInDays(scheduledDate, today);

          // Öncelik rengi
          let priorityColor = Colors.WARNING;
          if (reminder.priority === 'low') {
            priorityColor = Colors.SUCCESS;
          } else if (reminder.priority === 'high') {
            priorityColor = Colors.DANGER;
          }

          return (
            <TouchableOpacity
              key={reminder.id}
              style={styles.upcomingItem}
              onPress={() => goToReminderDetail(reminder.id)}
            >
              <View style={[styles.upcomingPriority, { backgroundColor: priorityColor }]} />
              <View style={styles.upcomingContent}>
                <Text style={styles.upcomingTitle} numberOfLines={1}>
                  {reminder.title}
                </Text>
                <View style={styles.upcomingInfo}>
                  <Text style={styles.upcomingDate}>
                    {formattedDate} {formattedTime}
                  </Text>
                  <Text style={[
                    styles.upcomingDays,
                    daysRemaining === 0 ? styles.upcomingToday :
                    daysRemaining <= 1 ? styles.upcomingUrgent :
                    styles.upcomingNormal
                  ]}>
                    {daysRemaining === 0 ? 'Bugün' :
                     daysRemaining === 1 ? 'Yarın' :
                     `${daysRemaining} gün`}
                  </Text>
                </View>
              </View>
            </TouchableOpacity>
          );
        })}
      </View>
    );
  };

  // Yükleniyor durumu
  if (loading && !refreshing) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={Colors.PRIMARY} />
        <Text style={styles.loadingText}>Yükleniyor...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Başlık */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Hatırlatıcılar</Text>
        <View style={styles.headerActions}>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => navigation.navigate('ReminderStats')}
          >
            <MaterialIcons name="bar-chart" size={24} color={Colors.PRIMARY} />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => navigation.navigate('ReminderGroups')}
          >
            <MaterialIcons name="folder" size={24} color={Colors.PRIMARY} />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => navigation.navigate('ReminderTags')}
          >
            <MaterialIcons name="label" size={24} color={Colors.PRIMARY} />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => setShowFilters(!showFilters)}
          >
            <MaterialIcons
              name="filter-list"
              size={24}
              color={showFilters || priorityFilter || categoryFilter || tagFilter ? Colors.PRIMARY : Colors.GRAY_500}
            />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.addButton}
            onPress={addNewReminder}
          >
            <MaterialIcons name="add" size={24} color="#fff" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Arama Çubuğu */}
      <View style={styles.searchContainer}>
        <View style={styles.searchInputContainer}>
          <MaterialIcons name="search" size={20} color={Colors.GRAY_500} />
          <TextInput
            style={styles.searchInput}
            placeholder="Hatırlatıcı ara..."
            value={searchText}
            onChangeText={setSearchText}
            returnKeyType="search"
          />
          {searchText ? (
            <TouchableOpacity
              style={styles.clearButton}
              onPress={() => setSearchText('')}
            >
              <MaterialIcons name="close" size={20} color={Colors.GRAY_500} />
            </TouchableOpacity>
          ) : null}
        </View>
      </View>

      {/* Filtreler */}
      {showFilters && (
        <View style={styles.filtersContainer}>
          {/* Öncelik Filtreleri */}
          <View style={styles.filterSection}>
            <Text style={styles.filterTitle}>Öncelik</Text>
            <View style={styles.filterOptions}>
              <TouchableOpacity
                style={[
                  styles.filterOption,
                  priorityFilter === null && styles.filterOptionActive
                ]}
                onPress={() => setPriorityFilter(null)}
              >
                <Text style={[
                  styles.filterOptionText,
                  priorityFilter === null && styles.filterOptionTextActive
                ]}>
                  Tümü
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.filterOption,
                  priorityFilter === 'low' && styles.filterOptionActive,
                  { backgroundColor: priorityFilter === 'low' ? Colors.SUCCESS : 'transparent' }
                ]}
                onPress={() => setPriorityFilter('low')}
              >
                <MaterialIcons
                  name="arrow-downward"
                  size={16}
                  color={priorityFilter === 'low' ? '#fff' : Colors.TEXT_DARK}
                />
                <Text style={[
                  styles.filterOptionText,
                  priorityFilter === 'low' && styles.filterOptionTextActive
                ]}>
                  Düşük
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.filterOption,
                  priorityFilter === 'normal' && styles.filterOptionActive,
                  { backgroundColor: priorityFilter === 'normal' ? Colors.WARNING : 'transparent' }
                ]}
                onPress={() => setPriorityFilter('normal')}
              >
                <MaterialIcons
                  name="remove"
                  size={16}
                  color={priorityFilter === 'normal' ? '#fff' : Colors.TEXT_DARK}
                />
                <Text style={[
                  styles.filterOptionText,
                  priorityFilter === 'normal' && styles.filterOptionTextActive
                ]}>
                  Normal
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.filterOption,
                  priorityFilter === 'high' && styles.filterOptionActive,
                  { backgroundColor: priorityFilter === 'high' ? Colors.DANGER : 'transparent' }
                ]}
                onPress={() => setPriorityFilter('high')}
              >
                <MaterialIcons
                  name="arrow-upward"
                  size={16}
                  color={priorityFilter === 'high' ? '#fff' : Colors.TEXT_DARK}
                />
                <Text style={[
                  styles.filterOptionText,
                  priorityFilter === 'high' && styles.filterOptionTextActive
                ]}>
                  Yüksek
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Kategori Filtreleri */}
          {categories.length > 0 && (
            <View style={styles.filterSection}>
              <Text style={styles.filterTitle}>Kategori</Text>
              <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                style={styles.categoryFilters}
              >
                <TouchableOpacity
                  style={[
                    styles.categoryFilterOption,
                    categoryFilter === null && styles.categoryFilterOptionActive
                  ]}
                  onPress={() => setCategoryFilter(null)}
                >
                  <Text style={[
                    styles.categoryFilterText,
                    categoryFilter === null && styles.categoryFilterTextActive
                  ]}>
                    Tümü
                  </Text>
                </TouchableOpacity>
                {categories.map(category => (
                  <TouchableOpacity
                    key={category.id}
                    style={[
                      styles.categoryFilterOption,
                      categoryFilter === category.id && styles.categoryFilterOptionActive,
                      { backgroundColor: categoryFilter === category.id ? category.color : 'transparent' }
                    ]}
                    onPress={() => setCategoryFilter(category.id)}
                  >
                    <Text style={[
                      styles.categoryFilterText,
                      categoryFilter === category.id && styles.categoryFilterTextActive
                    ]}>
                      {category.name}
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>
          )}

          {/* Etiket Filtreleri */}
          {tags.length > 0 && (
            <View style={styles.filterSection}>
              <Text style={styles.filterTitle}>Etiket</Text>
              <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                style={styles.categoryFilters}
              >
                <TouchableOpacity
                  style={[
                    styles.categoryFilterOption,
                    tagFilter === null && styles.categoryFilterOptionActive
                  ]}
                  onPress={() => setTagFilter(null)}
                >
                  <Text style={[
                    styles.categoryFilterText,
                    tagFilter === null && styles.categoryFilterTextActive
                  ]}>
                    Tümü
                  </Text>
                </TouchableOpacity>
                {tags.map(tag => (
                  <TouchableOpacity
                    key={tag.id}
                    style={[
                      styles.categoryFilterOption,
                      tagFilter === tag.id && styles.categoryFilterOptionActive,
                      { backgroundColor: tagFilter === tag.id ? tag.color : 'transparent' }
                    ]}
                    onPress={() => setTagFilter(tag.id)}
                  >
                    <Text style={[
                      styles.categoryFilterText,
                      tagFilter === tag.id && styles.categoryFilterTextActive
                    ]}>
                      {tag.name}
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>
          )}

          {/* Filtreleri Temizle */}
          {(priorityFilter !== null || categoryFilter !== null || tagFilter !== null) && (
            <TouchableOpacity
              style={styles.clearFiltersButton}
              onPress={() => {
                setPriorityFilter(null);
                setCategoryFilter(null);
                setTagFilter(null);
              }}
            >
              <MaterialIcons name="clear-all" size={16} color="#fff" />
              <Text style={styles.clearFiltersText}>Filtreleri Temizle</Text>
            </TouchableOpacity>
          )}
        </View>
      )}

      {/* Sekmeler */}
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[
            styles.tabButton,
            activeTab === 'upcoming' && styles.activeTabButton
          ]}
          onPress={() => setActiveTab('upcoming')}
        >
          <MaterialIcons
            name="notifications-active"
            size={20}
            color={activeTab === 'upcoming' ? Colors.PRIMARY : Colors.GRAY_500}
          />
          <Text style={[
            styles.tabText,
            activeTab === 'upcoming' && styles.activeTabText
          ]}>
            Yaklaşan
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.tabButton,
            activeTab === 'all' && styles.activeTabButton
          ]}
          onPress={() => setActiveTab('all')}
        >
          <MaterialIcons
            name="list"
            size={20}
            color={activeTab === 'all' ? Colors.PRIMARY : Colors.GRAY_500}
          />
          <Text style={[
            styles.tabText,
            activeTab === 'all' && styles.activeTabText
          ]}>
            Tümü
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.tabButton,
            activeTab === 'expenses' && styles.activeTabButton
          ]}
          onPress={() => setActiveTab('expenses')}
        >
          <MaterialIcons
            name="attach-money"
            size={20}
            color={activeTab === 'expenses' ? Colors.PRIMARY : Colors.GRAY_500}
          />
          <Text style={[
            styles.tabText,
            activeTab === 'expenses' && styles.activeTabText
          ]}>
            Harcamalar
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.tabButton,
            activeTab === 'salaries' && styles.activeTabButton
          ]}
          onPress={() => setActiveTab('salaries')}
        >
          <MaterialIcons
            name="account-balance-wallet"
            size={20}
            color={activeTab === 'salaries' ? Colors.PRIMARY : Colors.GRAY_500}
          />
          <Text style={[
            styles.tabText,
            activeTab === 'salaries' && styles.activeTabText
          ]}>
            Maaşlar
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.tabButton,
            activeTab === 'past' && styles.activeTabButton
          ]}
          onPress={() => setActiveTab('past')}
        >
          <MaterialIcons
            name="history"
            size={20}
            color={activeTab === 'past' ? Colors.PRIMARY : Colors.GRAY_500}
          />
          <Text style={[
            styles.tabText,
            activeTab === 'past' && styles.activeTabText
          ]}>
            Geçmiş
          </Text>
        </TouchableOpacity>
      </View>

      {/* İçerik */}
      <FlatList
        data={filteredReminders.length > 0 || searchText || priorityFilter || categoryFilter || tagFilter ? filteredReminders : reminders}
        renderItem={renderReminderItem}
        keyExtractor={item => item.id.toString()}
        contentContainerStyle={styles.listContent}
        ListEmptyComponent={renderEmptyState}
        ListHeaderComponent={activeTab === 'upcoming' && !searchText && !priorityFilter && !categoryFilter && !tagFilter ? renderUpcomingReminders : null}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[Colors.PRIMARY]}
          />
        }
      />

      {/* Yeni Hatırlatıcı Butonu */}
      <TouchableOpacity
        style={styles.floatingButton}
        onPress={addNewReminder}
      >
        <MaterialIcons name="add" size={24} color="#fff" />
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: Colors.PRIMARY,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.GRAY_200,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.TEXT_DARK,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.PRIMARY,
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: Colors.GRAY_200,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.GRAY_100,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    marginLeft: 8,
    color: Colors.TEXT_DARK,
  },
  clearButton: {
    padding: 4,
  },
  filtersContainer: {
    padding: 16,
    backgroundColor: Colors.GRAY_100,
    borderBottomWidth: 1,
    borderBottomColor: Colors.GRAY_200,
  },
  filterSection: {
    marginBottom: 16,
  },
  filterTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.TEXT_DARK,
    marginBottom: 8,
  },
  filterOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  filterOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: Colors.GRAY_300,
    marginRight: 8,
    marginBottom: 8,
  },
  filterOptionActive: {
    backgroundColor: Colors.PRIMARY,
    borderColor: Colors.PRIMARY,
  },
  filterOptionText: {
    fontSize: 14,
    color: Colors.TEXT_DARK,
    marginLeft: 4,
  },
  filterOptionTextActive: {
    color: '#fff',
    fontWeight: 'bold',
  },
  categoryFilters: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  categoryFilterOption: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: Colors.GRAY_300,
    marginRight: 8,
  },
  categoryFilterOptionActive: {
    backgroundColor: Colors.PRIMARY,
    borderColor: Colors.PRIMARY,
  },
  categoryFilterText: {
    fontSize: 14,
    color: Colors.TEXT_DARK,
  },
  categoryFilterTextActive: {
    color: '#fff',
    fontWeight: 'bold',
  },
  clearFiltersButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.PRIMARY,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignSelf: 'center',
  },
  clearFiltersText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  tabContainer: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: Colors.GRAY_200,
  },
  tabButton: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 12,
  },
  activeTabButton: {
    borderBottomWidth: 2,
    borderBottomColor: Colors.PRIMARY,
  },
  tabText: {
    fontSize: 14,
    color: Colors.GRAY_500,
    marginLeft: 4,
  },
  activeTabText: {
    color: Colors.PRIMARY,
    fontWeight: 'bold',
  },
  listContent: {
    padding: 16,
    paddingBottom: 80,
  },
  reminderItem: {
    backgroundColor: '#fff',
    borderRadius: 8,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    overflow: 'hidden',
  },
  reminderHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
  },
  priorityIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  reminderTitle: {
    flex: 1,
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.TEXT_DARK,
  },
  reminderActions: {
    flexDirection: 'row',
  },
  actionButton: {
    marginLeft: 12,
  },
  reminderContent: {
    padding: 12,
    paddingTop: 0,
  },
  reminderMessage: {
    fontSize: 14,
    color: Colors.TEXT_LIGHT,
    marginBottom: 8,
  },
  reminderInfo: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  reminderInfoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
    marginBottom: 4,
  },
  reminderInfoText: {
    fontSize: 12,
    color: Colors.TEXT_LIGHT,
    marginLeft: 4,
  },
  reminderFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    justifyContent: 'center',
  },
  todayFooter: {
    backgroundColor: Colors.DANGER,
  },
  urgentFooter: {
    backgroundColor: Colors.WARNING,
  },
  soonFooter: {
    backgroundColor: Colors.INFO,
  },
  normalFooter: {
    backgroundColor: Colors.SUCCESS,
  },
  pastFooter: {
    backgroundColor: Colors.GRAY_500,
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    justifyContent: 'center',
  },
  footerText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
    marginLeft: 4,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
    marginTop: 40,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.TEXT_DARK,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 14,
    color: Colors.TEXT_LIGHT,
    textAlign: 'center',
    marginBottom: 24,
  },
  emptyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.PRIMARY,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  emptyButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  upcomingContainer: {
    marginBottom: 24,
    backgroundColor: Colors.GRAY_100,
    borderRadius: 8,
    padding: 12,
  },
  sectionHeader: {
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.TEXT_DARK,
  },
  upcomingItem: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderRadius: 8,
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
    overflow: 'hidden',
  },
  upcomingPriority: {
    width: 4,
    backgroundColor: Colors.PRIMARY,
  },
  upcomingContent: {
    flex: 1,
    padding: 12,
  },
  upcomingTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: Colors.TEXT_DARK,
    marginBottom: 4,
  },
  upcomingInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  upcomingDate: {
    fontSize: 12,
    color: Colors.TEXT_LIGHT,
  },
  upcomingDays: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  upcomingToday: {
    color: Colors.DANGER,
  },
  upcomingUrgent: {
    color: Colors.WARNING,
  },
  upcomingNormal: {
    color: Colors.SUCCESS,
  },
  floatingButton: {
    position: 'absolute',
    right: 16,
    bottom: 16,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: Colors.PRIMARY,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
});
