import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  ActivityIndicator,
  Dimensions,
  Alert,
  Modal
} from 'react-native';
import { useSQLiteContext } from 'expo-sqlite';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useFocusEffect } from '@react-navigation/native';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors } from '../constants/colors';
import { useAppContext } from '../context/AppContext';
import { formatCurrency } from '../utils/formatters';
import { <PERSON><PERSON><PERSON>, Bar<PERSON>hart, PieChart } from 'react-native-chart-kit';
import * as Print from 'expo-print';
import * as Sharing from 'expo-sharing';
import * as FileSystem from 'expo-file-system';

const { width: screenWidth } = Dimensions.get('window');

/**
 * Gelişmiş Raporlar Ekranı
 * Finansal verilerin detaylı analizi ve karşılaştırması
 */
export default function ReportsScreen({ navigation }) {
  const db = useSQLiteContext();
  const insets = useSafeAreaInsets();
  const { theme } = useAppContext();

  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedPeriod, setSelectedPeriod] = useState('month');
  const [activeTab, setActiveTab] = useState('overview');
  const [showExportModal, setShowExportModal] = useState(false);
  const [showFilterModal, setShowFilterModal] = useState(false);
  const [showComparisonModal, setShowComparisonModal] = useState(false);
  const [exporting, setExporting] = useState(false);
  
  const [reportData, setReportData] = useState({
    summary: { income: 0, expense: 0, balance: 0, incomeCount: 0, expenseCount: 0 },
    categoryBreakdown: [],
    monthlyTrend: [],
    dailyTrend: [],
    comparison: { previous: {}, current: {}, growth: {} },
    insights: []
  });

  const chartRef = useRef();

  // Verileri yükle
  const loadReportData = useCallback(async () => {
    try {
      setLoading(true);

      const getPeriodDays = () => {
        switch (selectedPeriod) {
          case 'week': return 7;
          case 'month': return 30;
          case 'year': return 365;
          default: return 30;
        }
      };

      const days = getPeriodDays();

      // Mevcut periyot özet veriler
      const summaryResult = await db.getAllAsync(`
        SELECT 
          type,
          SUM(amount) as total,
          COUNT(*) as count,
          AVG(amount) as average
        FROM transactions 
        WHERE date >= date('now', '-${days} days')
        GROUP BY type
      `);

      const summary = {
        income: summaryResult.find(r => r.type === 'income')?.total || 0,
        expense: summaryResult.find(r => r.type === 'expense')?.total || 0,
        balance: 0,
        incomeCount: summaryResult.find(r => r.type === 'income')?.count || 0,
        expenseCount: summaryResult.find(r => r.type === 'expense')?.count || 0,
        avgIncome: summaryResult.find(r => r.type === 'income')?.average || 0,
        avgExpense: summaryResult.find(r => r.type === 'expense')?.average || 0
      };
      summary.balance = summary.income - summary.expense;

      // Önceki periyot karşılaştırması
      const previousSummaryResult = await db.getAllAsync(`
        SELECT 
          type,
          SUM(amount) as total,
          COUNT(*) as count
        FROM transactions 
        WHERE date >= date('now', '-${days * 2} days') 
        AND date < date('now', '-${days} days')
        GROUP BY type
      `);

      const previousSummary = {
        income: previousSummaryResult.find(r => r.type === 'income')?.total || 0,
        expense: previousSummaryResult.find(r => r.type === 'expense')?.total || 0,
        incomeCount: previousSummaryResult.find(r => r.type === 'income')?.count || 0,
        expenseCount: previousSummaryResult.find(r => r.type === 'expense')?.count || 0
      };
      previousSummary.balance = previousSummary.income - previousSummary.expense;

      // Büyüme oranları hesapla
      const calculateGrowth = (current, previous) => {
        if (previous === 0) return current > 0 ? 100 : 0;
        return ((current - previous) / previous) * 100;
      };

      const growth = {
        income: calculateGrowth(summary.income, previousSummary.income),
        expense: calculateGrowth(summary.expense, previousSummary.expense),
        balance: calculateGrowth(summary.balance, previousSummary.balance),
        incomeCount: calculateGrowth(summary.incomeCount, previousSummary.incomeCount),
        expenseCount: calculateGrowth(summary.expenseCount, previousSummary.expenseCount)
      };

      // Kategori dağılımı
      const categoryResult = await db.getAllAsync(`
        SELECT 
          c.name as category,
          c.color,
          SUM(t.amount) as total,
          COUNT(*) as transaction_count,
          AVG(t.amount) as average_amount
        FROM transactions t
        JOIN categories c ON t.category_id = c.id
        WHERE t.date >= date('now', '-${days} days') AND t.type = 'expense'
        GROUP BY c.id, c.name, c.color
        ORDER BY total DESC
        LIMIT 10
      `);

      // Aylık trend
      const monthlyTrendResult = await db.getAllAsync(`
        SELECT 
          strftime('%Y-%m', date) as month,
          type,
          SUM(amount) as total,
          COUNT(*) as count
        FROM transactions 
        WHERE date >= date('now', '-${Math.max(days * 2, 180)} days')
        GROUP BY month, type
        ORDER BY month
      `);

      // Günlük trend
      const dailyTrendResult = await db.getAllAsync(`
        SELECT 
          date,
          type,
          SUM(amount) as total,
          COUNT(*) as count
        FROM transactions 
        WHERE date >= date('now', '-${days} days')
        GROUP BY date, type
        ORDER BY date
      `);

      // İçgörüler oluştur
      const insights = generateInsights(summary, previousSummary, growth, categoryResult);

      setReportData({
        summary,
        categoryBreakdown: categoryResult,
        monthlyTrend: monthlyTrendResult,
        dailyTrend: dailyTrendResult,
        comparison: { previous: previousSummary, current: summary, growth },
        insights
      });

      setLoading(false);
    } catch (error) {
      console.error('Rapor verileri yükleme hatası:', error);
      setLoading(false);
    }
  }, [db, selectedPeriod]);

  // İçgörüler oluştur
  const generateInsights = (current, previous, growth, categories) => {
    const insights = [];

    // Gelir artışı
    if (growth.income > 10) {
      insights.push({
        type: 'positive',
        title: 'Gelir Artışı',
        description: `Geliriniz önceki döneme göre %${growth.income.toFixed(1)} arttı!`,
        icon: 'trending-up'
      });
    } else if (growth.income < -10) {
      insights.push({
        type: 'warning',
        title: 'Gelir Azalışı',
        description: `Geliriniz önceki döneme göre %${Math.abs(growth.income).toFixed(1)} azaldı.`,
        icon: 'trending-down'
      });
    }

    // Gider kontrolü
    if (growth.expense < -5) {
      insights.push({
        type: 'positive',
        title: 'Gider Kontrolü',
        description: `Giderlerinizi %${Math.abs(growth.expense).toFixed(1)} azalttınız!`,
        icon: 'savings'
      });
    } else if (growth.expense > 15) {
      insights.push({
        type: 'warning',
        title: 'Gider Artışı',
        description: `Giderleriniz %${growth.expense.toFixed(1)} arttı. Kontrol etmeyi düşünün.`,
        icon: 'warning'
      });
    }

    // En yüksek kategori
    if (categories.length > 0) {
      const topCategory = categories[0];
      insights.push({
        type: 'info',
        title: 'En Yüksek Harcama',
        description: `En çok harcama yaptığınız kategori: ${topCategory.category} (${formatCurrency(topCategory.total, 'TRY')})`,
        icon: 'pie-chart'
      });
    }

    // Tasarruf oranı
    const savingsRate = current.income > 0 ? ((current.balance / current.income) * 100) : 0;
    if (savingsRate > 20) {
      insights.push({
        type: 'positive',
        title: 'Harika Tasarruf',
        description: `Tasarruf oranınız %${savingsRate.toFixed(1)}! Mükemmel finansal disiplin.`,
        icon: 'account-balance'
      });
    } else if (savingsRate < 0) {
      insights.push({
        type: 'warning',
        title: 'Negatif Bakiye',
        description: 'Giderleriniz gelirinizi aşıyor. Bütçe planlaması yapmanızı öneririz.',
        icon: 'warning'
      });
    }

    return insights;
  };

  // Yenileme işlemi
  const onRefresh = async () => {
    setRefreshing(true);
    await loadReportData();
    setRefreshing(false);
  };

  // PDF Export
  const exportToPDF = async () => {
    try {
      setExporting(true);
      
      const htmlContent = `
        <html>
          <head>
            <meta charset="utf-8">
            <title>Detaylı Finansal Rapor</title>
            <style>
              body { font-family: Arial, sans-serif; margin: 20px; color: #333; }
              .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #007AFF; padding-bottom: 20px; }
              .summary { display: flex; justify-content: space-around; margin-bottom: 30px; }
              .summary-card { text-align: center; padding: 20px; border: 1px solid #ddd; border-radius: 8px; background: #f9f9f9; }
              .comparison { margin: 20px 0; padding: 20px; background: #f0f8ff; border-radius: 8px; }
              .insights { margin: 20px 0; }
              .insight { padding: 15px; margin: 10px 0; border-left: 4px solid #007AFF; background: #f9f9f9; }
              .category-list { margin-top: 20px; }
              .category-item { display: flex; justify-content: space-between; padding: 10px; border-bottom: 1px solid #eee; }
              .amount { font-weight: bold; }
              .income { color: #4CAF50; }
              .expense { color: #F44336; }
              .growth-positive { color: #4CAF50; }
              .growth-negative { color: #F44336; }
            </style>
          </head>
          <body>
            <div class="header">
              <h1>Detaylı Finansal Rapor</h1>
              <p>Periyot: ${selectedPeriod === 'week' ? 'Son 7 Gün' : selectedPeriod === 'month' ? 'Son 30 Gün' : 'Son 1 Yıl'}</p>
              <p>Tarih: ${new Date().toLocaleDateString('tr-TR')}</p>
            </div>
            
            <div class="summary">
              <div class="summary-card">
                <h3>Toplam Gelir</h3>
                <p class="amount income">${formatCurrency(reportData.summary.income, 'TRY')}</p>
                <small class="growth-${reportData.comparison.growth.income >= 0 ? 'positive' : 'negative'}">
                  ${reportData.comparison.growth.income >= 0 ? '+' : ''}${reportData.comparison.growth.income.toFixed(1)}%
                </small>
              </div>
              <div class="summary-card">
                <h3>Toplam Gider</h3>
                <p class="amount expense">${formatCurrency(reportData.summary.expense, 'TRY')}</p>
                <small class="growth-${reportData.comparison.growth.expense >= 0 ? 'positive' : 'negative'}">
                  ${reportData.comparison.growth.expense >= 0 ? '+' : ''}${reportData.comparison.growth.expense.toFixed(1)}%
                </small>
              </div>
              <div class="summary-card">
                <h3>Net Bakiye</h3>
                <p class="amount ${reportData.summary.balance >= 0 ? 'income' : 'expense'}">${formatCurrency(reportData.summary.balance, 'TRY')}</p>
                <small class="growth-${reportData.comparison.growth.balance >= 0 ? 'positive' : 'negative'}">
                  ${reportData.comparison.growth.balance >= 0 ? '+' : ''}${reportData.comparison.growth.balance.toFixed(1)}%
                </small>
              </div>
            </div>

            <div class="comparison">
              <h2>Periyot Karşılaştırması</h2>
              <p><strong>Önceki Periyot:</strong> Gelir ${formatCurrency(reportData.comparison.previous.income, 'TRY')}, Gider ${formatCurrency(reportData.comparison.previous.expense, 'TRY')}</p>
              <p><strong>Mevcut Periyot:</strong> Gelir ${formatCurrency(reportData.comparison.current.income, 'TRY')}, Gider ${formatCurrency(reportData.comparison.current.expense, 'TRY')}</p>
            </div>

            <div class="insights">
              <h2>Finansal İçgörüler</h2>
              ${reportData.insights.map(insight => `
                <div class="insight">
                  <h4>${insight.title}</h4>
                  <p>${insight.description}</p>
                </div>
              `).join('')}
            </div>

            <div class="category-list">
              <h2>En Çok Harcama Yapılan Kategoriler</h2>
              ${reportData.categoryBreakdown.map((category, index) => `
                <div class="category-item">
                  <span>#${index + 1} ${category.category}</span>
                  <span class="amount">${formatCurrency(category.total, 'TRY')} (${category.transaction_count} işlem)</span>
                </div>
              `).join('')}
            </div>
          </body>
        </html>
      `;

      const { uri } = await Print.printToFileAsync({ html: htmlContent });
      await Sharing.shareAsync(uri);
      
      Alert.alert('Başarılı', 'Detaylı PDF raporu başarıyla oluşturuldu ve paylaşıldı.');
    } catch (error) {
      console.error('PDF export hatası:', error);
      Alert.alert('Hata', 'PDF oluşturulurken bir hata oluştu.');
    } finally {
      setExporting(false);
      setShowExportModal(false);
    }
  };

  // Excel Export
  const exportToExcel = async () => {
    try {
      setExporting(true);
      
      let csvContent = 'Kategori,Tutar,İşlem Sayısı,Ortalama Tutar\n';
      reportData.categoryBreakdown.forEach(category => {
        csvContent += `"${category.category}","${category.total}","${category.transaction_count}","${category.average_amount}"\n`;
      });

      // Karşılaştırma verileri ekle
      csvContent += '\n\nKarşılaştırma Verileri\n';
      csvContent += 'Metrik,Önceki Periyot,Mevcut Periyot,Değişim %\n';
      csvContent += `Gelir,"${reportData.comparison.previous.income}","${reportData.comparison.current.income}","${reportData.comparison.growth.income.toFixed(2)}"\n`;
      csvContent += `Gider,"${reportData.comparison.previous.expense}","${reportData.comparison.current.expense}","${reportData.comparison.growth.expense.toFixed(2)}"\n`;
      csvContent += `Bakiye,"${reportData.comparison.previous.balance}","${reportData.comparison.current.balance}","${reportData.comparison.growth.balance.toFixed(2)}"\n`;

      const fileName = `detayli_finansal_rapor_${new Date().toISOString().split('T')[0]}.csv`;
      const fileUri = FileSystem.documentDirectory + fileName;
      
      await FileSystem.writeAsStringAsync(fileUri, csvContent, {
        encoding: FileSystem.EncodingType.UTF8,
      });
      
      await Sharing.shareAsync(fileUri);
      
      Alert.alert('Başarılı', 'Detaylı Excel raporu başarıyla oluşturuldu ve paylaşıldı.');
    } catch (error) {
      console.error('Excel export hatası:', error);
      Alert.alert('Hata', 'Excel dosyası oluşturulurken bir hata oluştu.');
    } finally {
      setExporting(false);
      setShowExportModal(false);
    }
  };

  // Focus effect
  useFocusEffect(
    useCallback(() => {
      loadReportData();
    }, [loadReportData])
  );

  if (loading) {
    return (
      <View style={[styles.container, styles.centerContent, { backgroundColor: theme.BACKGROUND }]}>
        <ActivityIndicator size="large" color={theme.PRIMARY} />
        <Text style={[styles.loadingText, { color: theme.TEXT_PRIMARY }]}>Gelişmiş raporlar yükleniyor...</Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { paddingTop: insets.top, backgroundColor: theme.BACKGROUND }]}>
      <View style={[styles.header, { backgroundColor: theme.PRIMARY }]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <MaterialIcons name="arrow-back" size={24} color={theme.WHITE} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: theme.WHITE }]}>Gelişmiş Raporlar</Text>
        <View style={styles.headerRight}>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => setShowFilterModal(true)}
          >
            <MaterialIcons name="filter-list" size={24} color={theme.WHITE} />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => setShowComparisonModal(true)}
          >
            <MaterialIcons name="compare-arrows" size={24} color={theme.WHITE} />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => setShowExportModal(true)}
          >
            <MaterialIcons name="share" size={24} color={theme.WHITE} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Tab Seçici */}
      <View style={[styles.tabSelector, { backgroundColor: theme.CARD, borderBottomColor: theme.BORDER }]}>
        {[
          { key: 'overview', label: 'Genel', icon: 'dashboard' },
          { key: 'comparison', label: 'Karşılaştırma', icon: 'compare-arrows' },
          { key: 'insights', label: 'İçgörüler', icon: 'lightbulb-outline' },
          { key: 'trends', label: 'Trendler', icon: 'trending-up' }
        ].map((tab) => (
          <TouchableOpacity
            key={tab.key}
            style={[
              styles.tabButton,
              activeTab === tab.key && { borderBottomColor: theme.PRIMARY }
            ]}
            onPress={() => setActiveTab(tab.key)}
          >
            <MaterialIcons 
              name={tab.icon} 
              size={18} 
              color={activeTab === tab.key ? theme.PRIMARY : theme.TEXT_SECONDARY} 
            />
            <Text style={[
              styles.tabButtonText,
              { color: activeTab === tab.key ? theme.PRIMARY : theme.TEXT_SECONDARY }
            ]}>
              {tab.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Overview Tab */}
        {activeTab === 'overview' && (
          <View>
            {/* Özet Kartları */}
            <View style={styles.summaryContainer}>
              <View style={[styles.summaryCard, { backgroundColor: theme.CARD }]}>
                <MaterialIcons name="trending-up" size={24} color={Colors.SUCCESS} />
                <Text style={[styles.summaryLabel, { color: theme.TEXT_SECONDARY }]}>Toplam Gelir</Text>
                <Text style={[styles.summaryValue, { color: Colors.SUCCESS }]}>
                  {formatCurrency(reportData.summary.income, 'TRY')}
                </Text>
                <Text style={[styles.growthText, {
                  color: reportData.comparison.growth.income >= 0 ? Colors.SUCCESS : Colors.DANGER
                }]}>
                  {reportData.comparison.growth.income >= 0 ? '+' : ''}{reportData.comparison.growth.income.toFixed(1)}%
                </Text>
              </View>

              <View style={[styles.summaryCard, { backgroundColor: theme.CARD }]}>
                <MaterialIcons name="trending-down" size={24} color={Colors.DANGER} />
                <Text style={[styles.summaryLabel, { color: theme.TEXT_SECONDARY }]}>Toplam Gider</Text>
                <Text style={[styles.summaryValue, { color: Colors.DANGER }]}>
                  {formatCurrency(reportData.summary.expense, 'TRY')}
                </Text>
                <Text style={[styles.growthText, {
                  color: reportData.comparison.growth.expense >= 0 ? Colors.DANGER : Colors.SUCCESS
                }]}>
                  {reportData.comparison.growth.expense >= 0 ? '+' : ''}{reportData.comparison.growth.expense.toFixed(1)}%
                </Text>
              </View>

              <View style={[styles.summaryCard, { backgroundColor: theme.CARD }]}>
                <MaterialIcons name="account-balance" size={24} color={theme.PRIMARY} />
                <Text style={[styles.summaryLabel, { color: theme.TEXT_SECONDARY }]}>Net Bakiye</Text>
                <Text style={[styles.summaryValue, {
                  color: reportData.summary.balance >= 0 ? Colors.SUCCESS : Colors.DANGER
                }]}>
                  {formatCurrency(reportData.summary.balance, 'TRY')}
                </Text>
                <Text style={[styles.growthText, {
                  color: reportData.comparison.growth.balance >= 0 ? Colors.SUCCESS : Colors.DANGER
                }]}>
                  {reportData.comparison.growth.balance >= 0 ? '+' : ''}{reportData.comparison.growth.balance.toFixed(1)}%
                </Text>
              </View>
            </View>

            {/* Hızlı İstatistikler */}
            <View style={[styles.quickStats, { backgroundColor: theme.CARD }]}>
              <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>Hızlı İstatistikler</Text>
              <View style={styles.statsGrid}>
                <View style={styles.statItem}>
                  <Text style={[styles.statValue, { color: theme.PRIMARY }]}>
                    {reportData.summary.incomeCount + reportData.summary.expenseCount}
                  </Text>
                  <Text style={[styles.statLabel, { color: theme.TEXT_SECONDARY }]}>Toplam İşlem</Text>
                </View>
                <View style={styles.statItem}>
                  <Text style={[styles.statValue, { color: Colors.SUCCESS }]}>
                    {formatCurrency(reportData.summary.avgIncome, 'TRY')}
                  </Text>
                  <Text style={[styles.statLabel, { color: theme.TEXT_SECONDARY }]}>Ort. Gelir</Text>
                </View>
                <View style={styles.statItem}>
                  <Text style={[styles.statValue, { color: Colors.DANGER }]}>
                    {formatCurrency(reportData.summary.avgExpense, 'TRY')}
                  </Text>
                  <Text style={[styles.statLabel, { color: theme.TEXT_SECONDARY }]}>Ort. Gider</Text>
                </View>
                <View style={styles.statItem}>
                  <Text style={[styles.statValue, {
                    color: reportData.summary.income > 0 && ((reportData.summary.balance / reportData.summary.income) * 100) >= 0 ? Colors.SUCCESS : Colors.DANGER
                  }]}>
                    %{reportData.summary.income > 0 ? ((reportData.summary.balance / reportData.summary.income) * 100).toFixed(1) : '0.0'}
                  </Text>
                  <Text style={[styles.statLabel, { color: theme.TEXT_SECONDARY }]}>Tasarruf Oranı</Text>
                </View>
              </View>
            </View>

            {/* En Çok Harcama Yapılan Kategoriler */}
            <View style={[styles.chartContainer, { backgroundColor: theme.CARD }]}>
              <Text style={[styles.chartTitle, { color: theme.TEXT_PRIMARY }]}>En Çok Harcama Yapılan Kategoriler</Text>
              {reportData.categoryBreakdown.length > 0 ? (
                reportData.categoryBreakdown.slice(0, 5).map((category, index) => (
                  <View key={index} style={styles.categoryRankItem}>
                    <View style={styles.categoryRankLeft}>
                      <Text style={[styles.categoryRankNumber, { color: theme.PRIMARY }]}>#{index + 1}</Text>
                      <View style={[styles.categoryColorDot, { backgroundColor: category.color || theme.PRIMARY }]} />
                      <Text style={[styles.categoryRankName, { color: theme.TEXT_PRIMARY }]}>{category.category}</Text>
                    </View>
                    <View style={styles.categoryRankRight}>
                      <Text style={[styles.categoryRankAmount, { color: theme.TEXT_PRIMARY }]}>
                        {formatCurrency(category.total, 'TRY')}
                      </Text>
                      <Text style={[styles.categoryRankCount, { color: theme.TEXT_SECONDARY }]}>
                        {category.transaction_count} işlem
                      </Text>
                    </View>
                  </View>
                ))
              ) : (
                <View style={styles.emptyChart}>
                  <MaterialIcons name="bar-chart" size={48} color={theme.TEXT_SECONDARY} />
                  <Text style={[styles.emptyChartText, { color: theme.TEXT_PRIMARY }]}>
                    Henüz kategori verisi bulunmuyor
                  </Text>
                </View>
              )}
            </View>
          </View>
        )}

        {/* Comparison Tab */}
        {activeTab === 'comparison' && (
          <View>
            {/* Periyot Karşılaştırması */}
            <View style={[styles.comparisonContainer, { backgroundColor: theme.CARD }]}>
              <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>Periyot Karşılaştırması</Text>

              <View style={styles.comparisonRow}>
                <Text style={[styles.comparisonLabel, { color: theme.TEXT_SECONDARY }]}>Önceki Periyot</Text>
                <Text style={[styles.comparisonLabel, { color: theme.TEXT_SECONDARY }]}>Mevcut Periyot</Text>
                <Text style={[styles.comparisonLabel, { color: theme.TEXT_SECONDARY }]}>Değişim</Text>
              </View>

              <View style={styles.comparisonRow}>
                <View style={styles.comparisonItem}>
                  <Text style={[styles.comparisonTitle, { color: theme.TEXT_PRIMARY }]}>Gelir</Text>
                  <Text style={[styles.comparisonValue, { color: Colors.SUCCESS }]}>
                    {formatCurrency(reportData.comparison.previous.income, 'TRY')}
                  </Text>
                </View>
                <View style={styles.comparisonItem}>
                  <Text style={[styles.comparisonValue, { color: Colors.SUCCESS }]}>
                    {formatCurrency(reportData.comparison.current.income, 'TRY')}
                  </Text>
                </View>
                <View style={styles.comparisonItem}>
                  <Text style={[styles.comparisonGrowth, {
                    color: reportData.comparison.growth.income >= 0 ? Colors.SUCCESS : Colors.DANGER
                  }]}>
                    {reportData.comparison.growth.income >= 0 ? '+' : ''}{reportData.comparison.growth.income.toFixed(1)}%
                  </Text>
                </View>
              </View>

              <View style={styles.comparisonRow}>
                <View style={styles.comparisonItem}>
                  <Text style={[styles.comparisonTitle, { color: theme.TEXT_PRIMARY }]}>Gider</Text>
                  <Text style={[styles.comparisonValue, { color: Colors.DANGER }]}>
                    {formatCurrency(reportData.comparison.previous.expense, 'TRY')}
                  </Text>
                </View>
                <View style={styles.comparisonItem}>
                  <Text style={[styles.comparisonValue, { color: Colors.DANGER }]}>
                    {formatCurrency(reportData.comparison.current.expense, 'TRY')}
                  </Text>
                </View>
                <View style={styles.comparisonItem}>
                  <Text style={[styles.comparisonGrowth, {
                    color: reportData.comparison.growth.expense >= 0 ? Colors.DANGER : Colors.SUCCESS
                  }]}>
                    {reportData.comparison.growth.expense >= 0 ? '+' : ''}{reportData.comparison.growth.expense.toFixed(1)}%
                  </Text>
                </View>
              </View>

              <View style={styles.comparisonRow}>
                <View style={styles.comparisonItem}>
                  <Text style={[styles.comparisonTitle, { color: theme.TEXT_PRIMARY }]}>Bakiye</Text>
                  <Text style={[styles.comparisonValue, {
                    color: reportData.comparison.previous.balance >= 0 ? Colors.SUCCESS : Colors.DANGER
                  }]}>
                    {formatCurrency(reportData.comparison.previous.balance, 'TRY')}
                  </Text>
                </View>
                <View style={styles.comparisonItem}>
                  <Text style={[styles.comparisonValue, {
                    color: reportData.comparison.current.balance >= 0 ? Colors.SUCCESS : Colors.DANGER
                  }]}>
                    {formatCurrency(reportData.comparison.current.balance, 'TRY')}
                  </Text>
                </View>
                <View style={styles.comparisonItem}>
                  <Text style={[styles.comparisonGrowth, {
                    color: reportData.comparison.growth.balance >= 0 ? Colors.SUCCESS : Colors.DANGER
                  }]}>
                    {reportData.comparison.growth.balance >= 0 ? '+' : ''}{reportData.comparison.growth.balance.toFixed(1)}%
                  </Text>
                </View>
              </View>
            </View>

            {/* İşlem Sayısı Karşılaştırması */}
            <View style={[styles.comparisonContainer, { backgroundColor: theme.CARD }]}>
              <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>İşlem Sayısı Karşılaştırması</Text>

              <View style={styles.comparisonRow}>
                <View style={styles.comparisonItem}>
                  <Text style={[styles.comparisonTitle, { color: theme.TEXT_PRIMARY }]}>Gelir İşlemleri</Text>
                  <Text style={[styles.comparisonValue, { color: theme.PRIMARY }]}>
                    {reportData.comparison.previous.incomeCount} → {reportData.comparison.current.incomeCount}
                  </Text>
                </View>
                <View style={styles.comparisonItem}>
                  <Text style={[styles.comparisonGrowth, {
                    color: reportData.comparison.growth.incomeCount >= 0 ? Colors.SUCCESS : Colors.DANGER
                  }]}>
                    {reportData.comparison.growth.incomeCount >= 0 ? '+' : ''}{reportData.comparison.growth.incomeCount.toFixed(1)}%
                  </Text>
                </View>
              </View>

              <View style={styles.comparisonRow}>
                <View style={styles.comparisonItem}>
                  <Text style={[styles.comparisonTitle, { color: theme.TEXT_PRIMARY }]}>Gider İşlemleri</Text>
                  <Text style={[styles.comparisonValue, { color: theme.PRIMARY }]}>
                    {reportData.comparison.previous.expenseCount} → {reportData.comparison.current.expenseCount}
                  </Text>
                </View>
                <View style={styles.comparisonItem}>
                  <Text style={[styles.comparisonGrowth, {
                    color: reportData.comparison.growth.expenseCount >= 0 ? Colors.DANGER : Colors.SUCCESS
                  }]}>
                    {reportData.comparison.growth.expenseCount >= 0 ? '+' : ''}{reportData.comparison.growth.expenseCount.toFixed(1)}%
                  </Text>
                </View>
              </View>
            </View>
          </View>
        )}

        {/* Insights Tab */}
        {activeTab === 'insights' && (
          <View>
            <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY, marginBottom: 20 }]}>Finansal İçgörüler</Text>

            {reportData.insights.length > 0 ? (
              reportData.insights.map((insight, index) => (
                <View key={index} style={[styles.insightCard, {
                  backgroundColor: theme.CARD,
                  borderLeftColor: insight.type === 'positive' ? Colors.SUCCESS :
                                  insight.type === 'warning' ? Colors.WARNING :
                                  Colors.INFO
                }]}>
                  <View style={styles.insightHeader}>
                    <MaterialIcons
                      name={insight.icon}
                      size={24}
                      color={insight.type === 'positive' ? Colors.SUCCESS :
                             insight.type === 'warning' ? Colors.WARNING :
                             Colors.INFO}
                    />
                    <Text style={[styles.insightTitle, { color: theme.TEXT_PRIMARY }]}>
                      {insight.title}
                    </Text>
                  </View>
                  <Text style={[styles.insightDescription, { color: theme.TEXT_SECONDARY }]}>
                    {insight.description}
                  </Text>
                </View>
              ))
            ) : (
              <View style={[styles.emptyInsights, { backgroundColor: theme.CARD }]}>
                <MaterialIcons name="lightbulb-outline" size={64} color={theme.TEXT_SECONDARY} />
                <Text style={[styles.emptyText, { color: theme.TEXT_PRIMARY }]}>
                  Henüz yeterli veri bulunmuyor
                </Text>
                <Text style={[styles.emptySubtext, { color: theme.TEXT_SECONDARY }]}>
                  Daha fazla işlem ekleyerek kişiselleştirilmiş içgörüler alabilirsiniz
                </Text>
              </View>
            )}

            {/* Finansal Sağlık Skoru */}
            <View style={[styles.healthScoreContainer, { backgroundColor: theme.CARD }]}>
              <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>Finansal Sağlık Skoru</Text>

              {(() => {
                const savingsRate = reportData.summary.income > 0 ? ((reportData.summary.balance / reportData.summary.income) * 100) : 0;
                const transactionFrequency = reportData.summary.incomeCount + reportData.summary.expenseCount;
                const categoryDiversity = reportData.categoryBreakdown.length;

                // Basit skor hesaplama
                let score = 0;
                if (savingsRate > 20) score += 40;
                else if (savingsRate > 10) score += 25;
                else if (savingsRate > 0) score += 15;

                if (transactionFrequency > 20) score += 30;
                else if (transactionFrequency > 10) score += 20;
                else if (transactionFrequency > 5) score += 10;

                if (categoryDiversity > 5) score += 20;
                else if (categoryDiversity > 3) score += 15;
                else if (categoryDiversity > 1) score += 10;

                if (reportData.comparison.growth.expense < 0) score += 10;

                const scoreColor = score >= 80 ? Colors.SUCCESS : score >= 60 ? Colors.WARNING : Colors.DANGER;
                const scoreText = score >= 80 ? 'Mükemmel' : score >= 60 ? 'İyi' : score >= 40 ? 'Orta' : 'Geliştirilmeli';

                return (
                  <View style={styles.healthScore}>
                    <View style={styles.scoreCircle}>
                      <Text style={[styles.scoreValue, { color: scoreColor }]}>{score}</Text>
                      <Text style={[styles.scoreMax, { color: theme.TEXT_SECONDARY }]}>/100</Text>
                    </View>
                    <View style={styles.scoreDetails}>
                      <Text style={[styles.scoreStatus, { color: scoreColor }]}>{scoreText}</Text>
                      <Text style={[styles.scoreDescription, { color: theme.TEXT_SECONDARY }]}>
                        Finansal durumunuz {scoreText.toLowerCase()} seviyede
                      </Text>
                    </View>
                  </View>
                );
              })()}
            </View>
          </View>
        )}

        {/* Trends Tab */}
        {activeTab === 'trends' && (
          <View>
            {/* Aylık Trend */}
            {reportData.monthlyTrend.length > 0 && (
              <View style={[styles.chartContainer, { backgroundColor: theme.CARD }]} ref={chartRef}>
                <Text style={[styles.chartTitle, { color: theme.TEXT_PRIMARY }]}>Aylık Gelir-Gider Trendi</Text>
                {(() => {
                  const monthlyData = {};
                  reportData.monthlyTrend.forEach(item => {
                    if (!monthlyData[item.month]) {
                      monthlyData[item.month] = { income: 0, expense: 0 };
                    }
                    monthlyData[item.month][item.type] = item.total;
                  });

                  const months = Object.keys(monthlyData).slice(-6);
                  if (months.length === 0) return null;

                  const lineChartData = {
                    labels: months.map(month => {
                      const [, monthNum] = month.split('-');
                      const monthNames = ['Oca', 'Şub', 'Mar', 'Nis', 'May', 'Haz', 'Tem', 'Ağu', 'Eyl', 'Eki', 'Kas', 'Ara'];
                      return monthNames[parseInt(monthNum) - 1];
                    }),
                    datasets: [
                      {
                        data: months.map(month => monthlyData[month].income || 0),
                        color: (opacity = 1) => `rgba(76, 175, 80, ${opacity})`,
                        strokeWidth: 3
                      },
                      {
                        data: months.map(month => monthlyData[month].expense || 0),
                        color: (opacity = 1) => `rgba(244, 67, 54, ${opacity})`,
                        strokeWidth: 3
                      }
                    ]
                  };

                  const chartConfig = {
                    backgroundColor: theme.CARD,
                    backgroundGradientFrom: theme.CARD,
                    backgroundGradientTo: theme.CARD,
                    decimalPlaces: 0,
                    color: (opacity = 1) => theme.TEXT_SECONDARY,
                    labelColor: (opacity = 1) => theme.TEXT_SECONDARY,
                    style: { borderRadius: 16 },
                    propsForDots: {
                      r: "4",
                      strokeWidth: "2",
                      stroke: theme.PRIMARY
                    }
                  };

                  return (
                    <>
                      <LineChart
                        data={lineChartData}
                        width={screenWidth - 64}
                        height={220}
                        chartConfig={chartConfig}
                        bezier
                        style={{ marginVertical: 8, borderRadius: 16 }}
                      />
                      <View style={styles.legendContainer}>
                        <View style={styles.legendItem}>
                          <View style={[styles.legendDot, { backgroundColor: 'rgba(76, 175, 80, 1)' }]} />
                          <Text style={[styles.legendText, { color: theme.TEXT_SECONDARY }]}>Gelir</Text>
                        </View>
                        <View style={styles.legendItem}>
                          <View style={[styles.legendDot, { backgroundColor: 'rgba(244, 67, 54, 1)' }]} />
                          <Text style={[styles.legendText, { color: theme.TEXT_SECONDARY }]}>Gider</Text>
                        </View>
                      </View>
                    </>
                  );
                })()}
              </View>
            )}

            {/* Kategori Dağılımı Pie Chart */}
            {reportData.categoryBreakdown.length > 0 && (
              <View style={[styles.chartContainer, { backgroundColor: theme.CARD }]}>
                <Text style={[styles.chartTitle, { color: theme.TEXT_PRIMARY }]}>Kategori Dağılımı</Text>
                {(() => {
                  const colors = ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF', '#4BC0C0', '#FF6384'];
                  const pieChartData = reportData.categoryBreakdown.slice(0, 8).map((item, index) => ({
                    name: item.category,
                    population: item.total,
                    color: item.color || colors[index % colors.length],
                    legendFontColor: theme.TEXT_PRIMARY,
                    legendFontSize: 12
                  }));

                  const chartConfig = {
                    backgroundColor: theme.CARD,
                    backgroundGradientFrom: theme.CARD,
                    backgroundGradientTo: theme.CARD,
                    color: (opacity = 1) => theme.TEXT_SECONDARY,
                    labelColor: (opacity = 1) => theme.TEXT_SECONDARY,
                  };

                  return (
                    <PieChart
                      data={pieChartData}
                      width={screenWidth - 64}
                      height={220}
                      chartConfig={chartConfig}
                      accessor="population"
                      backgroundColor="transparent"
                      paddingLeft="15"
                      absolute
                    />
                  );
                })()}
              </View>
            )}

            {/* Trend verisi yoksa */}
            {reportData.monthlyTrend.length === 0 && reportData.categoryBreakdown.length === 0 && (
              <View style={[styles.emptyContainer, { backgroundColor: theme.CARD }]}>
                <MaterialIcons name="show-chart" size={64} color={theme.TEXT_SECONDARY} />
                <Text style={[styles.emptyText, { color: theme.TEXT_PRIMARY }]}>
                  Henüz trend verisi bulunmuyor
                </Text>
                <Text style={[styles.emptySubtext, { color: theme.TEXT_SECONDARY }]}>
                  Daha fazla işlem ekleyerek trend analizini görüntüleyebilirsiniz
                </Text>
              </View>
            )}
          </View>
        )}
      </ScrollView>

      {/* Export Modal */}
      <Modal
        visible={showExportModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowExportModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: theme.CARD }]}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: theme.TEXT_PRIMARY }]}>Raporu Dışa Aktar</Text>
              <TouchableOpacity
                onPress={() => setShowExportModal(false)}
                style={styles.modalCloseButton}
              >
                <MaterialIcons name="close" size={24} color={theme.TEXT_SECONDARY} />
              </TouchableOpacity>
            </View>

            <View style={styles.exportOptions}>
              <TouchableOpacity
                style={[styles.exportOption, { backgroundColor: theme.BACKGROUND }]}
                onPress={exportToPDF}
                disabled={exporting}
              >
                <MaterialIcons name="picture-as-pdf" size={32} color="#F44336" />
                <View style={styles.exportOptionContent}>
                  <Text style={[styles.exportOptionText, { color: theme.TEXT_PRIMARY }]}>Detaylı PDF Raporu</Text>
                  <Text style={[styles.exportOptionDesc, { color: theme.TEXT_SECONDARY }]}>
                    Karşılaştırma ve içgörüler dahil
                  </Text>
                </View>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.exportOption, { backgroundColor: theme.BACKGROUND }]}
                onPress={exportToExcel}
                disabled={exporting}
              >
                <MaterialIcons name="table-chart" size={32} color="#4CAF50" />
                <View style={styles.exportOptionContent}>
                  <Text style={[styles.exportOptionText, { color: theme.TEXT_PRIMARY }]}>Excel Analiz Dosyası</Text>
                  <Text style={[styles.exportOptionDesc, { color: theme.TEXT_SECONDARY }]}>
                    Karşılaştırma verileri ile CSV
                  </Text>
                </View>
              </TouchableOpacity>
            </View>

            {exporting && (
              <View style={styles.exportingIndicator}>
                <ActivityIndicator size="small" color={theme.PRIMARY} />
                <Text style={[styles.exportingText, { color: theme.TEXT_PRIMARY }]}>
                  Dışa aktarılıyor...
                </Text>
              </View>
            )}
          </View>
        </View>
      </Modal>

      {/* Filter Modal */}
      <Modal
        visible={showFilterModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowFilterModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: theme.CARD }]}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: theme.TEXT_PRIMARY }]}>Analiz Filtresi</Text>
              <TouchableOpacity
                onPress={() => setShowFilterModal(false)}
                style={styles.modalCloseButton}
              >
                <MaterialIcons name="close" size={24} color={theme.TEXT_SECONDARY} />
              </TouchableOpacity>
            </View>

            <View style={styles.filterOptions}>
              <Text style={[styles.filterLabel, { color: theme.TEXT_PRIMARY }]}>Analiz Periyodu</Text>

              <View style={styles.periodButtons}>
                {[
                  { key: 'week', label: 'Son 7 Gün', desc: 'Haftalık analiz' },
                  { key: 'month', label: 'Son 30 Gün', desc: 'Aylık analiz' },
                  { key: 'year', label: 'Son 1 Yıl', desc: 'Yıllık analiz' }
                ].map((period) => (
                  <TouchableOpacity
                    key={period.key}
                    style={[
                      styles.periodButton,
                      {
                        backgroundColor: selectedPeriod === period.key ? theme.PRIMARY : theme.BACKGROUND,
                        borderColor: theme.BORDER
                      }
                    ]}
                    onPress={() => setSelectedPeriod(period.key)}
                  >
                    <Text style={[
                      styles.periodButtonText,
                      { color: selectedPeriod === period.key ? theme.WHITE : theme.TEXT_PRIMARY }
                    ]}>
                      {period.label}
                    </Text>
                    <Text style={[
                      styles.periodButtonDesc,
                      { color: selectedPeriod === period.key ? theme.WHITE : theme.TEXT_SECONDARY }
                    ]}>
                      {period.desc}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>

              <TouchableOpacity
                style={[styles.applyFilterButton, { backgroundColor: theme.PRIMARY }]}
                onPress={() => {
                  setShowFilterModal(false);
                  loadReportData();
                }}
              >
                <Text style={[styles.applyFilterText, { color: theme.WHITE }]}>Analizi Güncelle</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Comparison Modal */}
      <Modal
        visible={showComparisonModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowComparisonModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: theme.CARD }]}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: theme.TEXT_PRIMARY }]}>Detaylı Karşılaştırma</Text>
              <TouchableOpacity
                onPress={() => setShowComparisonModal(false)}
                style={styles.modalCloseButton}
              >
                <MaterialIcons name="close" size={24} color={theme.TEXT_SECONDARY} />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.comparisonModalContent}>
              <View style={styles.comparisonDetail}>
                <Text style={[styles.comparisonDetailTitle, { color: theme.TEXT_PRIMARY }]}>
                  Periyot Karşılaştırması ({selectedPeriod === 'week' ? 'Haftalık' : selectedPeriod === 'month' ? 'Aylık' : 'Yıllık'})
                </Text>

                <View style={styles.comparisonMetric}>
                  <Text style={[styles.metricLabel, { color: theme.TEXT_SECONDARY }]}>Gelir Değişimi</Text>
                  <Text style={[styles.metricValue, {
                    color: reportData.comparison.growth.income >= 0 ? Colors.SUCCESS : Colors.DANGER
                  }]}>
                    {formatCurrency(reportData.comparison.previous.income, 'TRY')} → {formatCurrency(reportData.comparison.current.income, 'TRY')}
                  </Text>
                  <Text style={[styles.metricGrowth, {
                    color: reportData.comparison.growth.income >= 0 ? Colors.SUCCESS : Colors.DANGER
                  }]}>
                    {reportData.comparison.growth.income >= 0 ? '+' : ''}{reportData.comparison.growth.income.toFixed(2)}%
                  </Text>
                </View>

                <View style={styles.comparisonMetric}>
                  <Text style={[styles.metricLabel, { color: theme.TEXT_SECONDARY }]}>Gider Değişimi</Text>
                  <Text style={[styles.metricValue, {
                    color: reportData.comparison.growth.expense >= 0 ? Colors.DANGER : Colors.SUCCESS
                  }]}>
                    {formatCurrency(reportData.comparison.previous.expense, 'TRY')} → {formatCurrency(reportData.comparison.current.expense, 'TRY')}
                  </Text>
                  <Text style={[styles.metricGrowth, {
                    color: reportData.comparison.growth.expense >= 0 ? Colors.DANGER : Colors.SUCCESS
                  }]}>
                    {reportData.comparison.growth.expense >= 0 ? '+' : ''}{reportData.comparison.growth.expense.toFixed(2)}%
                  </Text>
                </View>

                <View style={styles.comparisonMetric}>
                  <Text style={[styles.metricLabel, { color: theme.TEXT_SECONDARY }]}>Net Bakiye Değişimi</Text>
                  <Text style={[styles.metricValue, {
                    color: reportData.comparison.growth.balance >= 0 ? Colors.SUCCESS : Colors.DANGER
                  }]}>
                    {formatCurrency(reportData.comparison.previous.balance, 'TRY')} → {formatCurrency(reportData.comparison.current.balance, 'TRY')}
                  </Text>
                  <Text style={[styles.metricGrowth, {
                    color: reportData.comparison.growth.balance >= 0 ? Colors.SUCCESS : Colors.DANGER
                  }]}>
                    {reportData.comparison.growth.balance >= 0 ? '+' : ''}{reportData.comparison.growth.balance.toFixed(2)}%
                  </Text>
                </View>
              </View>
            </ScrollView>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  backButton: {
    padding: 8,
    marginRight: 8,
  },
  headerTitle: {
    flex: 1,
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerButton: {
    padding: 8,
    marginLeft: 8,
  },
  tabSelector: {
    flexDirection: 'row',
    paddingHorizontal: 8,
    borderBottomWidth: 1,
  },
  tabButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  tabButtonText: {
    fontSize: 11,
    marginTop: 4,
    fontWeight: '500',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  summaryContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  summaryCard: {
    flex: 1,
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    marginHorizontal: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  summaryLabel: {
    fontSize: 12,
    marginTop: 8,
    marginBottom: 4,
    textAlign: 'center',
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 4,
  },
  growthText: {
    fontSize: 12,
    fontWeight: '600',
  },
  quickStats: {
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
    paddingHorizontal: 8,
  },
  statValue: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    textAlign: 'center',
  },
  chartContainer: {
    borderRadius: 16,
    padding: 16,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  chartTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  categoryRankItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  categoryRankLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  categoryRankNumber: {
    fontSize: 16,
    fontWeight: 'bold',
    width: 30,
  },
  categoryColorDot: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginRight: 12,
  },
  categoryRankName: {
    fontSize: 16,
    flex: 1,
  },
  categoryRankRight: {
    alignItems: 'flex-end',
  },
  categoryRankAmount: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  categoryRankCount: {
    fontSize: 12,
    marginTop: 2,
  },
  emptyChart: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyChartText: {
    fontSize: 16,
    marginTop: 12,
    textAlign: 'center',
  },
  comparisonContainer: {
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  comparisonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  comparisonLabel: {
    fontSize: 14,
    fontWeight: '600',
    flex: 1,
    textAlign: 'center',
  },
  comparisonItem: {
    flex: 1,
    alignItems: 'center',
  },
  comparisonTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  comparisonValue: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  comparisonGrowth: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  insightCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderLeftWidth: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  insightHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  insightTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 12,
  },
  insightDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  emptyInsights: {
    alignItems: 'center',
    padding: 32,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    textAlign: 'center',
  },
  emptySubtext: {
    fontSize: 14,
    marginTop: 8,
    textAlign: 'center',
  },
  emptyContainer: {
    alignItems: 'center',
    padding: 32,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  healthScoreContainer: {
    borderRadius: 16,
    padding: 16,
    marginTop: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  healthScore: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  scoreCircle: {
    width: 80,
    height: 80,
    borderRadius: 40,
    borderWidth: 3,
    borderColor: '#e0e0e0',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 20,
  },
  scoreValue: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  scoreMax: {
    fontSize: 12,
  },
  scoreDetails: {
    flex: 1,
  },
  scoreStatus: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  scoreDescription: {
    fontSize: 14,
  },
  legendContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 16,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 16,
  },
  legendDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  legendText: {
    fontSize: 14,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    maxWidth: 400,
    borderRadius: 16,
    padding: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  modalCloseButton: {
    padding: 4,
  },
  exportOptions: {
    gap: 16,
  },
  exportOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  exportOptionContent: {
    marginLeft: 16,
    flex: 1,
  },
  exportOptionText: {
    fontSize: 16,
    fontWeight: '600',
  },
  exportOptionDesc: {
    fontSize: 12,
    marginTop: 4,
  },
  exportingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 20,
    padding: 16,
  },
  exportingText: {
    marginLeft: 12,
    fontSize: 16,
  },
  filterOptions: {
    gap: 20,
  },
  filterLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  periodButtons: {
    gap: 12,
  },
  periodButton: {
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    alignItems: 'center',
  },
  periodButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  periodButtonDesc: {
    fontSize: 12,
    marginTop: 4,
  },
  applyFilterButton: {
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginTop: 20,
  },
  applyFilterText: {
    fontSize: 16,
    fontWeight: '600',
  },
  comparisonModalContent: {
    maxHeight: 400,
  },
  comparisonDetail: {
    gap: 16,
  },
  comparisonDetailTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  comparisonMetric: {
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  metricLabel: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  metricValue: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  metricGrowth: {
    fontSize: 14,
    fontWeight: 'bold',
  },
});
