import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  ActivityIndicator,
  Dimensions
} from 'react-native';
import { useSQLiteContext } from 'expo-sqlite';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useFocusEffect } from '@react-navigation/native';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors } from '../constants/colors';
import { useAppContext } from '../context/AppContext';
import { formatCurrency } from '../utils/formatters';
import { Line<PERSON><PERSON>, BarChart, PieChart } from 'react-native-chart-kit';

const { width: screenWidth } = Dimensions.get('window');

/**
 * Raporlar Ekranı
 * Finansal verilerin analizi
 */
export default function ReportsScreen({ navigation }) {
  const db = useSQLiteContext();
  const insets = useSafeAreaInsets();
  const { theme } = useAppContext();

  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedPeriod, setSelectedPeriod] = useState('month'); // week, month, year
  const [activeTab, setActiveTab] = useState('overview'); // overview, trends, categories
  const [reportData, setReportData] = useState({
    summary: { income: 0, expense: 0, balance: 0, incomeCount: 0, expenseCount: 0 },
    categoryBreakdown: [],
    monthlyTrend: [],
    dailyTrend: []
  });

  // Verileri yükle
  const loadReportData = useCallback(async () => {
    try {
      setLoading(true);

      // Özet veriler (son 30 gün)
      const summaryResult = await db.getAllAsync(`
        SELECT
          type,
          SUM(amount) as total,
          COUNT(*) as count
        FROM transactions
        WHERE date >= date('now', '-30 days')
        GROUP BY type
      `);

      const summary = {
        income: summaryResult.find(r => r.type === 'income')?.total || 0,
        expense: summaryResult.find(r => r.type === 'expense')?.total || 0,
        balance: 0,
        incomeCount: summaryResult.find(r => r.type === 'income')?.count || 0,
        expenseCount: summaryResult.find(r => r.type === 'expense')?.count || 0
      };
      summary.balance = summary.income - summary.expense;

      // Kategori dağılımı
      const categoryResult = await db.getAllAsync(`
        SELECT
          c.name as category,
          c.color,
          SUM(t.amount) as total,
          COUNT(*) as transaction_count
        FROM transactions t
        JOIN categories c ON t.category_id = c.id
        WHERE t.date >= date('now', '-30 days') AND t.type = 'expense'
        GROUP BY c.id, c.name, c.color
        ORDER BY total DESC
        LIMIT 5
      `);

      // Aylık trend (son 6 ay)
      const monthlyTrendResult = await db.getAllAsync(`
        SELECT
          strftime('%Y-%m', date) as month,
          type,
          SUM(amount) as total
        FROM transactions
        WHERE date >= date('now', '-6 months')
        GROUP BY month, type
        ORDER BY month
      `);

      // Günlük trend (son 30 gün)
      const dailyTrendResult = await db.getAllAsync(`
        SELECT
          date,
          type,
          SUM(amount) as total
        FROM transactions
        WHERE date >= date('now', '-30 days')
        GROUP BY date, type
        ORDER BY date
      `);

      setReportData({
        summary,
        categoryBreakdown: categoryResult,
        monthlyTrend: monthlyTrendResult,
        dailyTrend: dailyTrendResult
      });

      setLoading(false);
    } catch (error) {
      console.error('Rapor verileri yükleme hatası:', error);
      setLoading(false);
    }
  }, [db]);

  // Ekran odaklandığında verileri yükle
  useFocusEffect(
    useCallback(() => {
      loadReportData();
    }, [loadReportData])
  );

  // Yenileme işlemi
  const onRefresh = async () => {
    setRefreshing(true);
    await loadReportData();
    setRefreshing(false);
  };

  // Grafik konfigürasyonu
  const chartConfig = {
    backgroundColor: theme.CARD,
    backgroundGradientFrom: theme.CARD,
    backgroundGradientTo: theme.CARD,
    decimalPlaces: 0,
    color: (opacity = 1) => `rgba(108, 92, 231, ${opacity})`,
    labelColor: (opacity = 1) => theme.TEXT_SECONDARY,
    style: { borderRadius: 16 },
    propsForDots: {
      r: "4",
      strokeWidth: "2",
      stroke: theme.PRIMARY
    }
  };

  // Pie chart verisi
  const pieChartData = reportData.categoryBreakdown.map((item, index) => {
    const colors = ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF', '#FF9F40'];
    return {
      name: item.category,
      population: item.total,
      color: item.color || colors[index % colors.length],
      legendFontColor: theme.TEXT_PRIMARY,
      legendFontSize: 12
    };
  });



  if (loading) {
    return (
      <View style={[styles.container, styles.centerContent, { backgroundColor: theme.BACKGROUND }]}>
        <ActivityIndicator size="large" color={theme.PRIMARY} />
        <Text style={[styles.loadingText, { color: theme.TEXT_PRIMARY }]}>Raporlar yükleniyor...</Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { paddingTop: insets.top, backgroundColor: theme.BACKGROUND }]}>
      <View style={[styles.header, { backgroundColor: theme.PRIMARY }]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <MaterialIcons name="arrow-back" size={24} color={theme.WHITE} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: theme.WHITE }]}>Raporlar</Text>
        <View style={styles.headerRight} />
      </View>

      {/* Tab Seçici */}
      <View style={[styles.tabSelector, { backgroundColor: theme.CARD, borderBottomColor: theme.BORDER }]}>
        {[
          { key: 'overview', label: 'Genel', icon: 'dashboard' },
          { key: 'trends', label: 'Trendler', icon: 'trending-up' },
          { key: 'categories', label: 'Kategoriler', icon: 'pie-chart' }
        ].map((tab) => (
          <TouchableOpacity
            key={tab.key}
            style={[
              styles.tabButton,
              activeTab === tab.key && { borderBottomColor: theme.PRIMARY }
            ]}
            onPress={() => setActiveTab(tab.key)}
          >
            <MaterialIcons
              name={tab.icon}
              size={20}
              color={activeTab === tab.key ? theme.PRIMARY : theme.TEXT_SECONDARY}
            />
            <Text style={[
              styles.tabButtonText,
              { color: activeTab === tab.key ? theme.PRIMARY : theme.TEXT_SECONDARY }
            ]}>
              {tab.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Tab İçeriği */}
        {activeTab === 'overview' && (
          <View>
            {/* Özet Kartları */}
            <View style={styles.summaryContainer}>
              <View style={[styles.summaryCard, { backgroundColor: theme.CARD }]}>
                <MaterialIcons name="trending-up" size={24} color={Colors.SUCCESS} />
                <Text style={[styles.summaryLabel, { color: theme.TEXT_SECONDARY }]}>Toplam Gelir</Text>
                <Text style={[styles.summaryValue, { color: Colors.SUCCESS }]}>
                  {formatCurrency(reportData.summary.income, 'TRY')}
                </Text>
              </View>

              <View style={[styles.summaryCard, { backgroundColor: theme.CARD }]}>
                <MaterialIcons name="trending-down" size={24} color={Colors.DANGER} />
                <Text style={[styles.summaryLabel, { color: theme.TEXT_SECONDARY }]}>Toplam Gider</Text>
                <Text style={[styles.summaryValue, { color: Colors.DANGER }]}>
                  {formatCurrency(reportData.summary.expense, 'TRY')}
                </Text>
              </View>

              <View style={[styles.summaryCard, { backgroundColor: theme.CARD }]}>
                <MaterialIcons name="account-balance" size={24} color={theme.PRIMARY} />
                <Text style={[styles.summaryLabel, { color: theme.TEXT_SECONDARY }]}>Net Bakiye</Text>
                <Text style={[styles.summaryValue, {
                  color: reportData.summary.balance >= 0 ? Colors.SUCCESS : Colors.DANGER
                }]}>
                  {formatCurrency(reportData.summary.balance, 'TRY')}
                </Text>
              </View>
            </View>

            {/* İstatistikler */}
            <View style={[styles.statsContainer, { backgroundColor: theme.CARD }]}>
              <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>İstatistikler</Text>

              <View style={styles.statsGrid}>
                <View style={styles.statItem}>
                  <Text style={[styles.statValue, { color: theme.PRIMARY }]}>
                    {reportData.summary.incomeCount + reportData.summary.expenseCount}
                  </Text>
                  <Text style={[styles.statLabel, { color: theme.TEXT_SECONDARY }]}>Toplam İşlem</Text>
                </View>

                <View style={styles.statItem}>
                  <Text style={[styles.statValue, { color: Colors.SUCCESS }]}>
                    {reportData.summary.incomeCount}
                  </Text>
                  <Text style={[styles.statLabel, { color: theme.TEXT_SECONDARY }]}>Gelir İşlemi</Text>
                </View>

                <View style={styles.statItem}>
                  <Text style={[styles.statValue, { color: Colors.DANGER }]}>
                    {reportData.summary.expenseCount}
                  </Text>
                  <Text style={[styles.statLabel, { color: theme.TEXT_SECONDARY }]}>Gider İşlemi</Text>
                </View>

                <View style={styles.statItem}>
                  <Text style={[styles.statValue, {
                    color: reportData.summary.balance >= 0 ? Colors.SUCCESS : Colors.DANGER
                  }]}>
                    %{reportData.summary.income > 0 ? ((reportData.summary.balance / reportData.summary.income) * 100).toFixed(1) : '0.0'}
                  </Text>
                  <Text style={[styles.statLabel, { color: theme.TEXT_SECONDARY }]}>Tasarruf Oranı</Text>
                </View>
              </View>
            </View>

            {/* Kategori Dağılımı Pie Chart */}
            {pieChartData.length > 0 && (
              <View style={[styles.chartContainer, { backgroundColor: theme.CARD }]}>
                <Text style={[styles.chartTitle, { color: theme.TEXT_PRIMARY }]}>Kategori Dağılımı</Text>
                <PieChart
                  data={pieChartData}
                  width={screenWidth - 64}
                  height={220}
                  chartConfig={chartConfig}
                  accessor="population"
                  backgroundColor="transparent"
                  paddingLeft="15"
                  absolute
                />
              </View>
            )}
          </View>
        )}

        {/* Trends Tab */}
        {activeTab === 'trends' && (
          <View>
            {/* Aylık Trend Line Chart */}
            {reportData.monthlyTrend.length > 0 && (
              <View style={[styles.chartContainer, { backgroundColor: theme.CARD }]}>
                <Text style={[styles.chartTitle, { color: theme.TEXT_PRIMARY }]}>Aylık Gelir-Gider Trendi</Text>
                {(() => {
                  // Aylık trend verilerini hazırla
                  const monthlyData = {};
                  reportData.monthlyTrend.forEach(item => {
                    if (!monthlyData[item.month]) {
                      monthlyData[item.month] = { income: 0, expense: 0 };
                    }
                    monthlyData[item.month][item.type] = item.total;
                  });

                  const months = Object.keys(monthlyData).slice(-6); // Son 6 ay
                  if (months.length === 0) return null;

                  const lineChartData = {
                    labels: months.map(month => {
                      const [year, monthNum] = month.split('-');
                      const monthNames = ['Oca', 'Şub', 'Mar', 'Nis', 'May', 'Haz', 'Tem', 'Ağu', 'Eyl', 'Eki', 'Kas', 'Ara'];
                      return monthNames[parseInt(monthNum) - 1];
                    }),
                    datasets: [
                      {
                        data: months.map(month => monthlyData[month].income || 0),
                        color: (opacity = 1) => `rgba(76, 175, 80, ${opacity})`, // Yeşil - Gelir
                        strokeWidth: 3
                      },
                      {
                        data: months.map(month => monthlyData[month].expense || 0),
                        color: (opacity = 1) => `rgba(244, 67, 54, ${opacity})`, // Kırmızı - Gider
                        strokeWidth: 3
                      }
                    ]
                  };

                  return (
                    <>
                      <LineChart
                        data={lineChartData}
                        width={screenWidth - 64}
                        height={220}
                        chartConfig={chartConfig}
                        bezier
                        style={{ marginVertical: 8, borderRadius: 16 }}
                      />
                      {/* Legend */}
                      <View style={styles.legendContainer}>
                        <View style={styles.legendItem}>
                          <View style={[styles.legendDot, { backgroundColor: 'rgba(76, 175, 80, 1)' }]} />
                          <Text style={[styles.legendText, { color: theme.TEXT_SECONDARY }]}>Gelir</Text>
                        </View>
                        <View style={styles.legendItem}>
                          <View style={[styles.legendDot, { backgroundColor: 'rgba(244, 67, 54, 1)' }]} />
                          <Text style={[styles.legendText, { color: theme.TEXT_SECONDARY }]}>Gider</Text>
                        </View>
                      </View>
                    </>
                  );
                })()}
              </View>
            )}

            {/* Günlük Trend Bar Chart */}
            {reportData.dailyTrend.length > 0 && (
              <View style={[styles.chartContainer, { backgroundColor: theme.CARD }]}>
                <Text style={[styles.chartTitle, { color: theme.TEXT_PRIMARY }]}>Son 7 Günlük Trend</Text>
                {(() => {
                  // Son 7 günün verilerini hazırla
                  const dailyData = {};
                  reportData.dailyTrend.forEach(item => {
                    if (!dailyData[item.date]) {
                      dailyData[item.date] = { income: 0, expense: 0 };
                    }
                    dailyData[item.date][item.type] = item.total;
                  });

                  const last7Days = Object.keys(dailyData).slice(-7);
                  if (last7Days.length === 0) return null;

                  const barChartData = {
                    labels: last7Days.map(date => {
                      const d = new Date(date);
                      return d.getDate().toString();
                    }),
                    datasets: [
                      {
                        data: last7Days.map(date => dailyData[date].expense || 0),
                        color: (opacity = 1) => `rgba(244, 67, 54, ${opacity})` // Kırmızı - Gider
                      }
                    ]
                  };

                  return (
                    <BarChart
                      data={barChartData}
                      width={screenWidth - 64}
                      height={220}
                      chartConfig={chartConfig}
                      style={{ marginVertical: 8, borderRadius: 16 }}
                    />
                  );
                })()}
              </View>
            )}

            {/* Trend verisi yoksa */}
            {reportData.monthlyTrend.length === 0 && reportData.dailyTrend.length === 0 && (
              <View style={[styles.emptyContainer, { backgroundColor: theme.CARD }]}>
                <MaterialIcons name="show-chart" size={64} color={theme.TEXT_SECONDARY} />
                <Text style={[styles.emptyText, { color: theme.TEXT_PRIMARY }]}>
                  Henüz trend verisi bulunmuyor
                </Text>
                <Text style={[styles.emptySubtext, { color: theme.TEXT_SECONDARY }]}>
                  Daha fazla işlem ekleyerek trend analizini görüntüleyebilirsiniz
                </Text>
              </View>
            )}
          </View>
        )}

        {/* Categories Tab */}
        {activeTab === 'categories' && (
          <View>
            {/* Kategori Dağılımı Pie Chart */}
            {pieChartData.length > 0 && (
              <View style={[styles.chartContainer, { backgroundColor: theme.CARD }]}>
                <Text style={[styles.chartTitle, { color: theme.TEXT_PRIMARY }]}>Kategori Dağılımı</Text>
                <PieChart
                  data={pieChartData}
                  width={screenWidth - 64}
                  height={220}
                  chartConfig={chartConfig}
                  accessor="population"
                  backgroundColor="transparent"
                  paddingLeft="15"
                  absolute
                />
              </View>
            )}

            {/* En Çok Harcama Yapılan Kategoriler */}
            <View style={[styles.chartContainer, { backgroundColor: theme.CARD }]}>
              <Text style={[styles.chartTitle, { color: theme.TEXT_PRIMARY }]}>En Çok Harcama Yapılan Kategoriler</Text>
              {reportData.categoryBreakdown.length > 0 ? (
                reportData.categoryBreakdown.map((category, index) => (
                  <View key={index} style={styles.categoryRankItem}>
                    <View style={styles.categoryRankLeft}>
                      <Text style={[styles.categoryRankNumber, { color: theme.PRIMARY }]}>#{index + 1}</Text>
                      <View style={[styles.categoryColorDot, { backgroundColor: category.color || theme.PRIMARY }]} />
                      <Text style={[styles.categoryRankName, { color: theme.TEXT_PRIMARY }]}>{category.category}</Text>
                    </View>
                    <View style={styles.categoryRankRight}>
                      <Text style={[styles.categoryRankAmount, { color: theme.TEXT_PRIMARY }]}>
                        {formatCurrency(category.total, 'TRY')}
                      </Text>
                      <Text style={[styles.categoryRankCount, { color: theme.TEXT_SECONDARY }]}>
                        {category.transaction_count} işlem
                      </Text>
                    </View>
                  </View>
                ))
              ) : (
                <View style={styles.emptyChart}>
                  <MaterialIcons name="bar-chart" size={48} color={theme.TEXT_SECONDARY} />
                  <Text style={[styles.emptyChartText, { color: theme.TEXT_PRIMARY }]}>
                    Henüz kategori verisi bulunmuyor
                  </Text>
                </View>
              )}
            </View>
          </View>
        )}

        {/* Boş durum */}
        {reportData.summary.incomeCount === 0 && reportData.summary.expenseCount === 0 && (
          <View style={[styles.emptyContainer, { backgroundColor: theme.CARD }]}>
            <MaterialIcons name="bar-chart" size={64} color={theme.TEXT_SECONDARY} />
            <Text style={[styles.emptyText, { color: theme.TEXT_PRIMARY }]}>
              Henüz rapor verisi bulunmuyor
            </Text>
            <Text style={[styles.emptySubtext, { color: theme.TEXT_SECONDARY }]}>
              İşlem ekleyerek raporlarınızı görüntüleyebilirsiniz
            </Text>
          </View>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  backButton: {
    padding: 8,
    marginRight: 8,
  },
  headerTitle: {
    flex: 1,
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  headerRight: {
    width: 40,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  summaryContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  summaryCard: {
    flex: 1,
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    marginHorizontal: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  summaryLabel: {
    fontSize: 12,
    marginTop: 8,
    marginBottom: 4,
    textAlign: 'center',
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  chartContainer: {
    borderRadius: 16,
    padding: 16,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  chartTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  emptyContainer: {
    alignItems: 'center',
    padding: 32,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    textAlign: 'center',
  },
  emptySubtext: {
    fontSize: 14,
    marginTop: 8,
    textAlign: 'center',
  },
  statsContainer: {
    borderRadius: 16,
    padding: 16,
    marginHorizontal: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
    paddingHorizontal: 8,
  },
  statValue: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    textAlign: 'center',
  },
  emptyChart: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyChartText: {
    fontSize: 16,
    marginTop: 12,
    textAlign: 'center',
  },
  categoryRankItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  categoryRankLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  categoryRankNumber: {
    fontSize: 16,
    fontWeight: 'bold',
    width: 30,
  },
  categoryColorDot: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginRight: 12,
  },
  categoryRankName: {
    fontSize: 16,
    flex: 1,
  },
  categoryRankRight: {
    alignItems: 'flex-end',
  },
  categoryRankAmount: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  categoryRankCount: {
    fontSize: 12,
    marginTop: 2,
  },
  tabSelector: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    borderBottomWidth: 1,
  },
  tabButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  tabButtonText: {
    fontSize: 12,
    marginTop: 4,
    fontWeight: '500',
  },
  legendContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 16,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 16,
  },
  legendDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  legendText: {
    fontSize: 14,
  },
});
