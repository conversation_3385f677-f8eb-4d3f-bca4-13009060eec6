import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  ActivityIndicator,
  Dimensions
} from 'react-native';
import { useSQLiteContext } from 'expo-sqlite';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useFocusEffect } from '@react-navigation/native';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors } from '../constants/colors';
import { useAppContext } from '../context/AppContext';
import { formatCurrency } from '../utils/formatters';
import { Line<PERSON><PERSON>, BarChart, PieChart } from 'react-native-chart-kit';

const { width: screenWidth } = Dimensions.get('window');

/**
 * Raporlar Ekranı
 * Finansal verilerin grafiksel analizi
 */
export default function ReportsScreen({ navigation }) {
  const db = useSQLiteContext();
  const insets = useSafeAreaInsets();
  const { theme } = useAppContext();

  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedPeriod, setSelectedPeriod] = useState('month'); // week, month, year
  const [reportData, setReportData] = useState({
    summary: { income: 0, expense: 0, balance: 0 },
    monthlyTrend: [],
    categoryBreakdown: [],
    dailyTransactions: []
  });

  // Verileri yükle
  const loadReportData = useCallback(async () => {
    try {
      setLoading(true);

      // Özet veriler
      const summaryResult = await db.getAllAsync(`
        SELECT 
          type,
          SUM(amount) as total
        FROM transactions 
        WHERE date >= date('now', '-1 month')
        GROUP BY type
      `);

      const summary = {
        income: summaryResult.find(r => r.type === 'income')?.total || 0,
        expense: summaryResult.find(r => r.type === 'expense')?.total || 0,
        balance: 0
      };
      summary.balance = summary.income - summary.expense;

      // Aylık trend
      const monthlyTrendResult = await db.getAllAsync(`
        SELECT 
          strftime('%Y-%m', date) as month,
          type,
          SUM(amount) as total
        FROM transactions 
        WHERE date >= date('now', '-6 months')
        GROUP BY month, type
        ORDER BY month
      `);

      // Kategori dağılımı
      const categoryResult = await db.getAllAsync(`
        SELECT 
          c.name as category,
          c.color,
          SUM(t.amount) as total
        FROM transactions t
        JOIN categories c ON t.category_id = c.id
        WHERE t.date >= date('now', '-1 month')
        GROUP BY c.id, c.name, c.color
        ORDER BY total DESC
        LIMIT 10
      `);

      // Günlük işlemler
      const dailyResult = await db.getAllAsync(`
        SELECT 
          date,
          type,
          SUM(amount) as total
        FROM transactions 
        WHERE date >= date('now', '-30 days')
        GROUP BY date, type
        ORDER BY date
      `);

      setReportData({
        summary,
        monthlyTrend: monthlyTrendResult,
        categoryBreakdown: categoryResult,
        dailyTransactions: dailyResult
      });

      setLoading(false);
    } catch (error) {
      console.error('Rapor verileri yükleme hatası:', error);
      setLoading(false);
    }
  }, [db, selectedPeriod]);

  // Ekran odaklandığında verileri yükle
  useFocusEffect(
    useCallback(() => {
      loadReportData();
    }, [loadReportData])
  );

  // Yenileme işlemi
  const onRefresh = async () => {
    setRefreshing(true);
    await loadReportData();
    setRefreshing(false);
  };

  // Grafik konfigürasyonu
  const chartConfig = {
    backgroundColor: theme.CARD,
    backgroundGradientFrom: theme.CARD,
    backgroundGradientTo: theme.CARD,
    decimalPlaces: 0,
    color: (opacity = 1) => `rgba(108, 92, 231, ${opacity})`,
    labelColor: (opacity = 1) => theme.TEXT_PRIMARY,
    style: {
      borderRadius: 16
    },
    propsForDots: {
      r: "6",
      strokeWidth: "2",
      stroke: theme.PRIMARY
    }
  };

  // Kategori pie chart verisi
  const pieChartData = reportData.categoryBreakdown.map((item, index) => ({
    name: item.category,
    population: item.total,
    color: item.color || Colors.PRIMARY,
    legendFontColor: theme.TEXT_PRIMARY,
    legendFontSize: 12
  }));

  if (loading) {
    return (
      <View style={[styles.container, styles.centerContent, { backgroundColor: theme.BACKGROUND }]}>
        <ActivityIndicator size="large" color={theme.PRIMARY} />
        <Text style={[styles.loadingText, { color: theme.TEXT_PRIMARY }]}>Raporlar yükleniyor...</Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { paddingTop: insets.top, backgroundColor: theme.BACKGROUND }]}>
      <View style={[styles.header, { backgroundColor: theme.PRIMARY }]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <MaterialIcons name="arrow-back" size={24} color={theme.WHITE} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: theme.WHITE }]}>Raporlar</Text>
        <View style={styles.headerRight} />
      </View>

      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Özet Kartları */}
        <View style={styles.summaryContainer}>
          <View style={[styles.summaryCard, { backgroundColor: theme.CARD }]}>
            <MaterialIcons name="trending-up" size={24} color={Colors.SUCCESS} />
            <Text style={[styles.summaryLabel, { color: theme.TEXT_SECONDARY }]}>Toplam Gelir</Text>
            <Text style={[styles.summaryValue, { color: Colors.SUCCESS }]}>
              {formatCurrency(reportData.summary.income, 'TRY')}
            </Text>
          </View>

          <View style={[styles.summaryCard, { backgroundColor: theme.CARD }]}>
            <MaterialIcons name="trending-down" size={24} color={Colors.DANGER} />
            <Text style={[styles.summaryLabel, { color: theme.TEXT_SECONDARY }]}>Toplam Gider</Text>
            <Text style={[styles.summaryValue, { color: Colors.DANGER }]}>
              {formatCurrency(reportData.summary.expense, 'TRY')}
            </Text>
          </View>

          <View style={[styles.summaryCard, { backgroundColor: theme.CARD }]}>
            <MaterialIcons name="account-balance" size={24} color={theme.PRIMARY} />
            <Text style={[styles.summaryLabel, { color: theme.TEXT_SECONDARY }]}>Net Bakiye</Text>
            <Text style={[styles.summaryValue, { 
              color: reportData.summary.balance >= 0 ? Colors.SUCCESS : Colors.DANGER 
            }]}>
              {formatCurrency(reportData.summary.balance, 'TRY')}
            </Text>
          </View>
        </View>

        {/* Kategori Dağılımı */}
        {pieChartData.length > 0 && (
          <View style={[styles.chartContainer, { backgroundColor: theme.CARD }]}>
            <Text style={[styles.chartTitle, { color: theme.TEXT_PRIMARY }]}>Kategori Dağılımı</Text>
            <PieChart
              data={pieChartData}
              width={screenWidth - 64}
              height={220}
              chartConfig={chartConfig}
              accessor="population"
              backgroundColor="transparent"
              paddingLeft="15"
              absolute
            />
          </View>
        )}

        {/* Boş durum */}
        {reportData.categoryBreakdown.length === 0 && (
          <View style={[styles.emptyContainer, { backgroundColor: theme.CARD }]}>
            <MaterialIcons name="bar-chart" size={64} color={theme.TEXT_SECONDARY} />
            <Text style={[styles.emptyText, { color: theme.TEXT_PRIMARY }]}>
              Henüz rapor verisi bulunmuyor
            </Text>
            <Text style={[styles.emptySubtext, { color: theme.TEXT_SECONDARY }]}>
              İşlem ekleyerek raporlarınızı görüntüleyebilirsiniz
            </Text>
          </View>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  backButton: {
    padding: 8,
    marginRight: 8,
  },
  headerTitle: {
    flex: 1,
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  headerRight: {
    width: 40,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  summaryContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  summaryCard: {
    flex: 1,
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    marginHorizontal: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  summaryLabel: {
    fontSize: 12,
    marginTop: 8,
    marginBottom: 4,
    textAlign: 'center',
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  chartContainer: {
    borderRadius: 16,
    padding: 16,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  chartTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  emptyContainer: {
    alignItems: 'center',
    padding: 32,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    textAlign: 'center',
  },
  emptySubtext: {
    fontSize: 14,
    marginTop: 8,
    textAlign: 'center',
  },
});
