import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  ActivityIndicator,

  Alert,
  Modal
} from 'react-native';
import { useSQLiteContext } from 'expo-sqlite';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useFocusEffect } from '@react-navigation/native';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors } from '../constants/colors';
import { useAppContext } from '../context/AppContext';
import { formatCurrency } from '../utils/formatters';
import * as Print from 'expo-print';
import * as Sharing from 'expo-sharing';

/**
 * Temel Raporlar Ekranı - İlk Sürüm
 * Basit ama etkili finansal raporlama
 */
export default function ReportsScreen({ navigation }) {
  const db = useSQLiteContext();
  const insets = useSafeAreaInsets();
  const { theme } = useAppContext();

  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedPeriod, setSelectedPeriod] = useState('month');
  const [showExportModal, setShowExportModal] = useState(false);
  const [showComingSoonModal, setShowComingSoonModal] = useState(false);
  const [exporting, setExporting] = useState(false);

  const [reportData, setReportData] = useState({
    summary: { income: 0, expense: 0, balance: 0, incomeCount: 0, expenseCount: 0 },
    categoryBreakdown: [],
    recentTransactions: []
  });

  // Yakında gelecek özellikler listesi
  const comingSoonFeatures = [
    {
      title: 'Gelişmiş Grafik Analizi',
      description: 'Line chart, bar chart ve pie chart ile detaylı trend analizi',
      icon: 'show-chart',
      eta: 'Sonraki Güncelleme'
    },
    {
      title: 'Karşılaştırmalı Raporlar',
      description: 'Önceki dönemlerle karşılaştırmalı analiz ve büyüme oranları',
      icon: 'compare-arrows',
      eta: 'Sonraki Güncelleme'
    },
    {
      title: 'Akıllı İçgörüler',
      description: 'AI destekli finansal öneriler ve kişiselleştirilmiş tavsiyeleri',
      icon: 'lightbulb-outline',
      eta: 'Yakında'
    },
    {
      title: 'Finansal Sağlık Skoru',
      description: '100 üzerinden finansal durumunuzun değerlendirilmesi',
      icon: 'favorite',
      eta: 'Yakında'
    },
    {
      title: 'Gelişmiş Export Seçenekleri',
      description: 'PDF, Excel ve grafik görüntülerini paylaşma özellikleri',
      icon: 'file-download',
      eta: 'Yakında'
    }
  ];



  // Temel verileri yükle
  const loadReportData = useCallback(async () => {
    try {
      setLoading(true);

      const getPeriodDays = () => {
        switch (selectedPeriod) {
          case 'week': return 7;
          case 'month': return 30;
          case 'year': return 365;
          default: return 30;
        }
      };

      const days = getPeriodDays();

      // Özet veriler
      const summaryResult = await db.getAllAsync(`
        SELECT
          type,
          SUM(amount) as total,
          COUNT(*) as count
        FROM transactions
        WHERE date >= date('now', '-${days} days')
        GROUP BY type
      `);

      const summary = {
        income: summaryResult.find(r => r.type === 'income')?.total || 0,
        expense: summaryResult.find(r => r.type === 'expense')?.total || 0,
        balance: 0,
        incomeCount: summaryResult.find(r => r.type === 'income')?.count || 0,
        expenseCount: summaryResult.find(r => r.type === 'expense')?.count || 0
      };
      summary.balance = summary.income - summary.expense;

      // En çok harcama yapılan kategoriler (basit)
      const categoryResult = await db.getAllAsync(`
        SELECT
          c.name as category,
          c.color,
          SUM(t.amount) as total,
          COUNT(*) as transaction_count
        FROM transactions t
        JOIN categories c ON t.category_id = c.id
        WHERE t.date >= date('now', '-${days} days') AND t.type = 'expense'
        GROUP BY c.id, c.name, c.color
        ORDER BY total DESC
        LIMIT 5
      `);

      // Son işlemler
      const recentTransactions = await db.getAllAsync(`
        SELECT
          t.id,
          t.amount,
          t.description,
          t.type,
          t.date,
          c.name as category,
          c.color as category_color
        FROM transactions t
        JOIN categories c ON t.category_id = c.id
        WHERE t.date >= date('now', '-${days} days')
        ORDER BY t.date DESC, t.id DESC
        LIMIT 10
      `);

      setReportData({
        summary,
        categoryBreakdown: categoryResult,
        recentTransactions
      });

      setLoading(false);
    } catch (error) {
      console.error('Rapor verileri yükleme hatası:', error);
      setLoading(false);
    }
  }, [db, selectedPeriod]);

  // Yenileme işlemi
  const onRefresh = async () => {
    setRefreshing(true);
    await loadReportData();
    setRefreshing(false);
  };

  // Basit PDF Export
  const exportToPDF = async () => {
    try {
      setExporting(true);

      const periodText = selectedPeriod === 'week' ? 'Son 7 Gün' :
                        selectedPeriod === 'month' ? 'Son 30 Gün' : 'Son 1 Yıl';

      const htmlContent = `
        <html>
          <head>
            <meta charset="utf-8">
            <title>Finansal Rapor</title>
            <style>
              body { font-family: Arial, sans-serif; margin: 20px; color: #333; }
              .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #007AFF; padding-bottom: 20px; }
              .summary { display: flex; justify-content: space-around; margin-bottom: 30px; }
              .summary-card { text-align: center; padding: 20px; border: 1px solid #ddd; border-radius: 8px; background: #f9f9f9; }
              .category-list { margin-top: 20px; }
              .category-item { display: flex; justify-content: space-between; padding: 10px; border-bottom: 1px solid #eee; }
              .amount { font-weight: bold; }
              .income { color: #4CAF50; }
              .expense { color: #F44336; }
              .stats { margin: 20px 0; padding: 20px; background: #f0f8ff; border-radius: 8px; }
            </style>
          </head>
          <body>
            <div class="header">
              <h1>Finansal Rapor</h1>
              <p>Periyot: ${periodText}</p>
              <p>Tarih: ${new Date().toLocaleDateString('tr-TR')}</p>
            </div>

            <div class="summary">
              <div class="summary-card">
                <h3>Toplam Gelir</h3>
                <p class="amount income">${formatCurrency(reportData.summary.income, 'TRY')}</p>
                <small>${reportData.summary.incomeCount} işlem</small>
              </div>
              <div class="summary-card">
                <h3>Toplam Gider</h3>
                <p class="amount expense">${formatCurrency(reportData.summary.expense, 'TRY')}</p>
                <small>${reportData.summary.expenseCount} işlem</small>
              </div>
              <div class="summary-card">
                <h3>Net Bakiye</h3>
                <p class="amount ${reportData.summary.balance >= 0 ? 'income' : 'expense'}">${formatCurrency(reportData.summary.balance, 'TRY')}</p>
                <small>Tasarruf Oranı: %${reportData.summary.income > 0 ? ((reportData.summary.balance / reportData.summary.income) * 100).toFixed(1) : '0.0'}</small>
              </div>
            </div>

            <div class="stats">
              <h2>Özet İstatistikler</h2>
              <p><strong>Toplam İşlem:</strong> ${reportData.summary.incomeCount + reportData.summary.expenseCount}</p>
              <p><strong>Ortalama Gelir:</strong> ${reportData.summary.incomeCount > 0 ? formatCurrency(reportData.summary.income / reportData.summary.incomeCount, 'TRY') : '0 TL'}</p>
              <p><strong>Ortalama Gider:</strong> ${reportData.summary.expenseCount > 0 ? formatCurrency(reportData.summary.expense / reportData.summary.expenseCount, 'TRY') : '0 TL'}</p>
            </div>

            <div class="category-list">
              <h2>En Çok Harcama Yapılan Kategoriler</h2>
              ${reportData.categoryBreakdown.map((category, index) => `
                <div class="category-item">
                  <span>#${index + 1} ${category.category}</span>
                  <span class="amount">${formatCurrency(category.total, 'TRY')} (${category.transaction_count} işlem)</span>
                </div>
              `).join('')}
            </div>
          </body>
        </html>
      `;

      const { uri } = await Print.printToFileAsync({ html: htmlContent });
      await Sharing.shareAsync(uri);

      Alert.alert('Başarılı', 'PDF raporu başarıyla oluşturuldu ve paylaşıldı.');
    } catch (error) {
      console.error('PDF export hatası:', error);
      Alert.alert('Hata', 'PDF oluşturulurken bir hata oluştu.');
    } finally {
      setExporting(false);
      setShowExportModal(false);
    }
  };


  // Focus effect
  useFocusEffect(
    useCallback(() => {
      loadReportData();
    }, [loadReportData])
  );

  if (loading) {
    return (
      <View style={[styles.container, styles.centerContent, { backgroundColor: theme.BACKGROUND }]}>
        <ActivityIndicator size="large" color={theme.PRIMARY} />
        <Text style={[styles.loadingText, { color: theme.TEXT_PRIMARY }]}>Raporlar yükleniyor...</Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { paddingTop: insets.top, backgroundColor: theme.BACKGROUND }]}>
      <View style={[styles.header, { backgroundColor: theme.PRIMARY }]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <MaterialIcons name="arrow-back" size={24} color={theme.WHITE} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: theme.WHITE }]}>Raporlar</Text>
        <View style={styles.headerRight}>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => setShowComingSoonModal(true)}
          >
            <MaterialIcons name="auto-awesome" size={24} color={theme.WHITE} />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => setShowExportModal(true)}
          >
            <MaterialIcons name="share" size={24} color={theme.WHITE} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Periyot Seçici */}
      <View style={[styles.periodSelector, { backgroundColor: theme.CARD, borderBottomColor: theme.BORDER }]}>
        {[
          { key: 'week', label: 'Son 7 Gün' },
          { key: 'month', label: 'Son 30 Gün' },
          { key: 'year', label: 'Son 1 Yıl' }
        ].map((period) => (
          <TouchableOpacity
            key={period.key}
            style={[
              styles.periodButton,
              { backgroundColor: selectedPeriod === period.key ? theme.PRIMARY : 'transparent' }
            ]}
            onPress={() => setSelectedPeriod(period.key)}
          >
            <Text style={[
              styles.periodButtonText,
              { color: selectedPeriod === period.key ? theme.WHITE : theme.TEXT_PRIMARY }
            ]}>
              {period.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Özet Kartları */}
        <View style={styles.summaryContainer}>
          <View style={[styles.summaryCard, { backgroundColor: theme.CARD }]}>
            <MaterialIcons name="trending-up" size={24} color={Colors.SUCCESS} />
            <Text style={[styles.summaryLabel, { color: theme.TEXT_SECONDARY }]}>Toplam Gelir</Text>
            <Text style={[styles.summaryValue, { color: Colors.SUCCESS }]}>
              {formatCurrency(reportData.summary.income, 'TRY')}
            </Text>
            <Text style={[styles.summaryCount, { color: theme.TEXT_SECONDARY }]}>
              {reportData.summary.incomeCount} işlem
            </Text>
          </View>

          <View style={[styles.summaryCard, { backgroundColor: theme.CARD }]}>
            <MaterialIcons name="trending-down" size={24} color={Colors.DANGER} />
            <Text style={[styles.summaryLabel, { color: theme.TEXT_SECONDARY }]}>Toplam Gider</Text>
            <Text style={[styles.summaryValue, { color: Colors.DANGER }]}>
              {formatCurrency(reportData.summary.expense, 'TRY')}
            </Text>
            <Text style={[styles.summaryCount, { color: theme.TEXT_SECONDARY }]}>
              {reportData.summary.expenseCount} işlem
            </Text>
          </View>

          <View style={[styles.summaryCard, { backgroundColor: theme.CARD }]}>
            <MaterialIcons name="account-balance" size={24} color={theme.PRIMARY} />
            <Text style={[styles.summaryLabel, { color: theme.TEXT_SECONDARY }]}>Net Bakiye</Text>
            <Text style={[styles.summaryValue, {
              color: reportData.summary.balance >= 0 ? Colors.SUCCESS : Colors.DANGER
            }]}>
              {formatCurrency(reportData.summary.balance, 'TRY')}
            </Text>
            <Text style={[styles.summaryCount, { color: theme.TEXT_SECONDARY }]}>
              %{reportData.summary.income > 0 ? ((reportData.summary.balance / reportData.summary.income) * 100).toFixed(1) : '0.0'} tasarruf
            </Text>
          </View>
        </View>

        {/* Hızlı İstatistikler */}
        <View style={[styles.quickStats, { backgroundColor: theme.CARD }]}>
          <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>Özet İstatistikler</Text>
          <View style={styles.statsGrid}>
            <View style={styles.statItem}>
              <Text style={[styles.statValue, { color: theme.PRIMARY }]}>
                {reportData.summary.incomeCount + reportData.summary.expenseCount}
              </Text>
              <Text style={[styles.statLabel, { color: theme.TEXT_SECONDARY }]}>Toplam İşlem</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={[styles.statValue, { color: Colors.SUCCESS }]}>
                {reportData.summary.incomeCount > 0 ? formatCurrency(reportData.summary.income / reportData.summary.incomeCount, 'TRY') : '0 TL'}
              </Text>
              <Text style={[styles.statLabel, { color: theme.TEXT_SECONDARY }]}>Ort. Gelir</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={[styles.statValue, { color: Colors.DANGER }]}>
                {reportData.summary.expenseCount > 0 ? formatCurrency(reportData.summary.expense / reportData.summary.expenseCount, 'TRY') : '0 TL'}
              </Text>
              <Text style={[styles.statLabel, { color: theme.TEXT_SECONDARY }]}>Ort. Gider</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={[styles.statValue, {
                color: reportData.summary.income > 0 && ((reportData.summary.balance / reportData.summary.income) * 100) >= 0 ? Colors.SUCCESS : Colors.DANGER
              }]}>
                %{reportData.summary.income > 0 ? ((reportData.summary.balance / reportData.summary.income) * 100).toFixed(1) : '0.0'}
              </Text>
              <Text style={[styles.statLabel, { color: theme.TEXT_SECONDARY }]}>Tasarruf Oranı</Text>
            </View>
          </View>
        </View>

        {/* En Çok Harcama Yapılan Kategoriler */}
        <View style={[styles.chartContainer, { backgroundColor: theme.CARD }]}>
          <Text style={[styles.chartTitle, { color: theme.TEXT_PRIMARY }]}>En Çok Harcama Yapılan Kategoriler</Text>
          {reportData.categoryBreakdown.length > 0 ? (
            reportData.categoryBreakdown.map((category, index) => (
              <View key={index} style={styles.categoryRankItem}>
                <View style={styles.categoryRankLeft}>
                  <Text style={[styles.categoryRankNumber, { color: theme.PRIMARY }]}>#{index + 1}</Text>
                  <View style={[styles.categoryColorDot, { backgroundColor: category.color || theme.PRIMARY }]} />
                  <Text style={[styles.categoryRankName, { color: theme.TEXT_PRIMARY }]}>{category.category}</Text>
                </View>
                <View style={styles.categoryRankRight}>
                  <Text style={[styles.categoryRankAmount, { color: theme.TEXT_PRIMARY }]}>
                    {formatCurrency(category.total, 'TRY')}
                  </Text>
                  <Text style={[styles.categoryRankCount, { color: theme.TEXT_SECONDARY }]}>
                    {category.transaction_count} işlem
                  </Text>
                </View>
              </View>
            ))
          ) : (
            <View style={styles.emptyChart}>
              <MaterialIcons name="bar-chart" size={48} color={theme.TEXT_SECONDARY} />
              <Text style={[styles.emptyChartText, { color: theme.TEXT_PRIMARY }]}>
                Henüz kategori verisi bulunmuyor
              </Text>
            </View>
          )}
        </View>

        {/* Son İşlemler */}
        <View style={[styles.chartContainer, { backgroundColor: theme.CARD }]}>
          <Text style={[styles.chartTitle, { color: theme.TEXT_PRIMARY }]}>Son İşlemler</Text>
          {reportData.recentTransactions.length > 0 ? (
            reportData.recentTransactions.map((transaction, index) => (
              <View key={index} style={styles.transactionItem}>
                <View style={styles.transactionLeft}>
                  <View style={[styles.categoryColorDot, { backgroundColor: transaction.category_color || theme.PRIMARY }]} />
                  <View style={styles.transactionDetails}>
                    <Text style={[styles.transactionDescription, { color: theme.TEXT_PRIMARY }]}>
                      {transaction.description}
                    </Text>
                    <Text style={[styles.transactionCategory, { color: theme.TEXT_SECONDARY }]}>
                      {transaction.category} • {new Date(transaction.date).toLocaleDateString('tr-TR')}
                    </Text>
                  </View>
                </View>
                <Text style={[
                  styles.transactionAmount,
                  { color: transaction.type === 'income' ? Colors.SUCCESS : Colors.DANGER }
                ]}>
                  {transaction.type === 'income' ? '+' : '-'}{formatCurrency(transaction.amount, 'TRY')}
                </Text>
              </View>
            ))
          ) : (
            <View style={styles.emptyChart}>
              <MaterialIcons name="receipt" size={48} color={theme.TEXT_SECONDARY} />
              <Text style={[styles.emptyChartText, { color: theme.TEXT_PRIMARY }]}>
                Henüz işlem bulunmuyor
              </Text>
            </View>
          )}
        </View>

        {/* Yakında Gelecek Özellikler Teaser */}
        <View style={[styles.comingSoonTeaser, { backgroundColor: theme.CARD }]}>
          <View style={styles.comingSoonHeader}>
            <MaterialIcons name="auto-awesome" size={24} color={theme.PRIMARY} />
            <Text style={[styles.comingSoonTitle, { color: theme.TEXT_PRIMARY }]}>Yakında Gelecek Özellikler</Text>
          </View>
          <Text style={[styles.comingSoonDescription, { color: theme.TEXT_SECONDARY }]}>
            Gelişmiş grafik analizi, karşılaştırmalı raporlar, akıllı içgörüler ve daha fazlası...
          </Text>
          <TouchableOpacity
            style={[styles.comingSoonButton, { backgroundColor: theme.PRIMARY }]}
            onPress={() => setShowComingSoonModal(true)}
          >
            <Text style={[styles.comingSoonButtonText, { color: theme.WHITE }]}>Detayları Gör</Text>
          </TouchableOpacity>
        </View>

        {/* Boş durum */}
        {reportData.summary.incomeCount === 0 && reportData.summary.expenseCount === 0 && (
          <View style={[styles.emptyContainer, { backgroundColor: theme.CARD }]}>
            <MaterialIcons name="bar-chart" size={64} color={theme.TEXT_SECONDARY} />
            <Text style={[styles.emptyText, { color: theme.TEXT_PRIMARY }]}>
              Henüz rapor verisi bulunmuyor
            </Text>
            <Text style={[styles.emptySubtext, { color: theme.TEXT_SECONDARY }]}>
              İşlem ekleyerek raporlarınızı görüntüleyebilirsiniz
            </Text>
          </View>
        )}






      </ScrollView>

      {/* Export Modal */}
      <Modal
        visible={showExportModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowExportModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: theme.CARD }]}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: theme.TEXT_PRIMARY }]}>Raporu Paylaş</Text>
              <TouchableOpacity
                onPress={() => setShowExportModal(false)}
                style={styles.modalCloseButton}
              >
                <MaterialIcons name="close" size={24} color={theme.TEXT_SECONDARY} />
              </TouchableOpacity>
            </View>

            <View style={styles.exportOptions}>
              <TouchableOpacity
                style={[styles.exportOption, { backgroundColor: theme.BACKGROUND }]}
                onPress={exportToPDF}
                disabled={exporting}
              >
                <MaterialIcons name="picture-as-pdf" size={32} color="#F44336" />
                <View style={styles.exportOptionContent}>
                  <Text style={[styles.exportOptionText, { color: theme.TEXT_PRIMARY }]}>PDF Raporu</Text>
                  <Text style={[styles.exportOptionDesc, { color: theme.TEXT_SECONDARY }]}>
                    Temel finansal rapor
                  </Text>
                </View>
              </TouchableOpacity>
            </View>

            {exporting && (
              <View style={styles.exportingIndicator}>
                <ActivityIndicator size="small" color={theme.PRIMARY} />
                <Text style={[styles.exportingText, { color: theme.TEXT_PRIMARY }]}>
                  PDF oluşturuluyor...
                </Text>
              </View>
            )}
          </View>
        </View>
      </Modal>

      {/* Coming Soon Modal */}
      <Modal
        visible={showComingSoonModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowComingSoonModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: theme.CARD }]}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: theme.TEXT_PRIMARY }]}>Yakında Gelecek Özellikler</Text>
              <TouchableOpacity
                onPress={() => setShowComingSoonModal(false)}
                style={styles.modalCloseButton}
              >
                <MaterialIcons name="close" size={24} color={theme.TEXT_SECONDARY} />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.comingSoonList}>
              {comingSoonFeatures.map((feature, index) => (
                <View key={index} style={[styles.featureItem, { borderBottomColor: theme.BORDER }]}>
                  <View style={styles.featureHeader}>
                    <MaterialIcons name={feature.icon} size={24} color={theme.PRIMARY} />
                    <Text style={[styles.featureTitle, { color: theme.TEXT_PRIMARY }]}>
                      {feature.title}
                    </Text>
                    <View style={[styles.etaBadge, { backgroundColor: theme.PRIMARY }]}>
                      <Text style={[styles.etaText, { color: theme.WHITE }]}>
                        {feature.eta}
                      </Text>
                    </View>
                  </View>
                  <Text style={[styles.featureDescription, { color: theme.TEXT_SECONDARY }]}>
                    {feature.description}
                  </Text>
                </View>
              ))}
            </ScrollView>

            <View style={styles.comingSoonFooter}>
              <Text style={[styles.comingSoonFooterText, { color: theme.TEXT_SECONDARY }]}>
                Bu özellikler yakında uygulamaya eklenecek. Güncellemeleri kaçırmayın!
              </Text>
            </View>
          </View>
        </View>
      </Modal>

      {/* Filter Modal */}
      <Modal
        visible={showFilterModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowFilterModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: theme.CARD }]}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: theme.TEXT_PRIMARY }]}>Analiz Filtresi</Text>
              <TouchableOpacity
                onPress={() => setShowFilterModal(false)}
                style={styles.modalCloseButton}
              >
                <MaterialIcons name="close" size={24} color={theme.TEXT_SECONDARY} />
              </TouchableOpacity>
            </View>

            <View style={styles.filterOptions}>
              <Text style={[styles.filterLabel, { color: theme.TEXT_PRIMARY }]}>Analiz Periyodu</Text>

              <View style={styles.periodButtons}>
                {[
                  { key: 'week', label: 'Son 7 Gün', desc: 'Haftalık analiz' },
                  { key: 'month', label: 'Son 30 Gün', desc: 'Aylık analiz' },
                  { key: 'year', label: 'Son 1 Yıl', desc: 'Yıllık analiz' }
                ].map((period) => (
                  <TouchableOpacity
                    key={period.key}
                    style={[
                      styles.periodButton,
                      {
                        backgroundColor: selectedPeriod === period.key ? theme.PRIMARY : theme.BACKGROUND,
                        borderColor: theme.BORDER
                      }
                    ]}
                    onPress={() => setSelectedPeriod(period.key)}
                  >
                    <Text style={[
                      styles.periodButtonText,
                      { color: selectedPeriod === period.key ? theme.WHITE : theme.TEXT_PRIMARY }
                    ]}>
                      {period.label}
                    </Text>
                    <Text style={[
                      styles.periodButtonDesc,
                      { color: selectedPeriod === period.key ? theme.WHITE : theme.TEXT_SECONDARY }
                    ]}>
                      {period.desc}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>

              <TouchableOpacity
                style={[styles.applyFilterButton, { backgroundColor: theme.PRIMARY }]}
                onPress={() => {
                  setShowFilterModal(false);
                  loadReportData();
                }}
              >
                <Text style={[styles.applyFilterText, { color: theme.WHITE }]}>Analizi Güncelle</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Comparison Modal */}
      <Modal
        visible={showComparisonModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowComparisonModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: theme.CARD }]}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: theme.TEXT_PRIMARY }]}>Detaylı Karşılaştırma</Text>
              <TouchableOpacity
                onPress={() => setShowComparisonModal(false)}
                style={styles.modalCloseButton}
              >
                <MaterialIcons name="close" size={24} color={theme.TEXT_SECONDARY} />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.comparisonModalContent}>
              <View style={styles.comparisonDetail}>
                <Text style={[styles.comparisonDetailTitle, { color: theme.TEXT_PRIMARY }]}>
                  Periyot Karşılaştırması ({selectedPeriod === 'week' ? 'Haftalık' : selectedPeriod === 'month' ? 'Aylık' : 'Yıllık'})
                </Text>

                <View style={styles.comparisonMetric}>
                  <Text style={[styles.metricLabel, { color: theme.TEXT_SECONDARY }]}>Gelir Değişimi</Text>
                  <Text style={[styles.metricValue, {
                    color: reportData.comparison.growth.income >= 0 ? Colors.SUCCESS : Colors.DANGER
                  }]}>
                    {formatCurrency(reportData.comparison.previous.income, 'TRY')} → {formatCurrency(reportData.comparison.current.income, 'TRY')}
                  </Text>
                  <Text style={[styles.metricGrowth, {
                    color: reportData.comparison.growth.income >= 0 ? Colors.SUCCESS : Colors.DANGER
                  }]}>
                    {reportData.comparison.growth.income >= 0 ? '+' : ''}{reportData.comparison.growth.income.toFixed(2)}%
                  </Text>
                </View>

                <View style={styles.comparisonMetric}>
                  <Text style={[styles.metricLabel, { color: theme.TEXT_SECONDARY }]}>Gider Değişimi</Text>
                  <Text style={[styles.metricValue, {
                    color: reportData.comparison.growth.expense >= 0 ? Colors.DANGER : Colors.SUCCESS
                  }]}>
                    {formatCurrency(reportData.comparison.previous.expense, 'TRY')} → {formatCurrency(reportData.comparison.current.expense, 'TRY')}
                  </Text>
                  <Text style={[styles.metricGrowth, {
                    color: reportData.comparison.growth.expense >= 0 ? Colors.DANGER : Colors.SUCCESS
                  }]}>
                    {reportData.comparison.growth.expense >= 0 ? '+' : ''}{reportData.comparison.growth.expense.toFixed(2)}%
                  </Text>
                </View>

                <View style={styles.comparisonMetric}>
                  <Text style={[styles.metricLabel, { color: theme.TEXT_SECONDARY }]}>Net Bakiye Değişimi</Text>
                  <Text style={[styles.metricValue, {
                    color: reportData.comparison.growth.balance >= 0 ? Colors.SUCCESS : Colors.DANGER
                  }]}>
                    {formatCurrency(reportData.comparison.previous.balance, 'TRY')} → {formatCurrency(reportData.comparison.current.balance, 'TRY')}
                  </Text>
                  <Text style={[styles.metricGrowth, {
                    color: reportData.comparison.growth.balance >= 0 ? Colors.SUCCESS : Colors.DANGER
                  }]}>
                    {reportData.comparison.growth.balance >= 0 ? '+' : ''}{reportData.comparison.growth.balance.toFixed(2)}%
                  </Text>
                </View>
              </View>
            </ScrollView>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  backButton: {
    padding: 8,
    marginRight: 8,
  },
  headerTitle: {
    flex: 1,
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerButton: {
    padding: 8,
    marginLeft: 8,
  },
  tabSelector: {
    flexDirection: 'row',
    paddingHorizontal: 8,
    borderBottomWidth: 1,
  },
  tabButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  tabButtonText: {
    fontSize: 11,
    marginTop: 4,
    fontWeight: '500',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  summaryContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  summaryCard: {
    flex: 1,
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    marginHorizontal: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  summaryLabel: {
    fontSize: 12,
    marginTop: 8,
    marginBottom: 4,
    textAlign: 'center',
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 4,
  },
  summaryCount: {
    fontSize: 12,
    textAlign: 'center',
  },
  periodSelector: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  periodButton: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    marginHorizontal: 4,
    alignItems: 'center',
  },
  periodButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  quickStats: {
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
    paddingHorizontal: 8,
  },
  statValue: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    textAlign: 'center',
  },
  chartContainer: {
    borderRadius: 16,
    padding: 16,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  chartTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  categoryRankItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  categoryRankLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  categoryRankNumber: {
    fontSize: 16,
    fontWeight: 'bold',
    width: 30,
  },
  categoryColorDot: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginRight: 12,
  },
  categoryRankName: {
    fontSize: 16,
    flex: 1,
  },
  categoryRankRight: {
    alignItems: 'flex-end',
  },
  categoryRankAmount: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  categoryRankCount: {
    fontSize: 12,
    marginTop: 2,
  },
  emptyChart: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyChartText: {
    fontSize: 16,
    marginTop: 12,
    textAlign: 'center',
  },
  comparisonContainer: {
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  comparisonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  comparisonLabel: {
    fontSize: 14,
    fontWeight: '600',
    flex: 1,
    textAlign: 'center',
  },
  comparisonItem: {
    flex: 1,
    alignItems: 'center',
  },
  comparisonTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  comparisonValue: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  comparisonGrowth: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  insightCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderLeftWidth: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  insightHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  insightTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 12,
  },
  insightDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  emptyInsights: {
    alignItems: 'center',
    padding: 32,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    textAlign: 'center',
  },
  emptySubtext: {
    fontSize: 14,
    marginTop: 8,
    textAlign: 'center',
  },
  emptyContainer: {
    alignItems: 'center',
    padding: 32,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  healthScoreContainer: {
    borderRadius: 16,
    padding: 16,
    marginTop: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  healthScore: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  scoreCircle: {
    width: 80,
    height: 80,
    borderRadius: 40,
    borderWidth: 3,
    borderColor: '#e0e0e0',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 20,
  },
  scoreValue: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  scoreMax: {
    fontSize: 12,
  },
  scoreDetails: {
    flex: 1,
  },
  scoreStatus: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  scoreDescription: {
    fontSize: 14,
  },
  legendContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 16,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 16,
  },
  legendDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  legendText: {
    fontSize: 14,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    maxWidth: 400,
    borderRadius: 16,
    padding: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  modalCloseButton: {
    padding: 4,
  },
  exportOptions: {
    gap: 16,
  },
  exportOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  exportOptionContent: {
    marginLeft: 16,
    flex: 1,
  },
  exportOptionText: {
    fontSize: 16,
    fontWeight: '600',
  },
  exportOptionDesc: {
    fontSize: 12,
    marginTop: 4,
  },
  exportingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 20,
    padding: 16,
  },
  exportingText: {
    marginLeft: 12,
    fontSize: 16,
  },
  filterOptions: {
    gap: 20,
  },
  filterLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  periodButtons: {
    gap: 12,
  },
  periodButton: {
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    alignItems: 'center',
  },
  periodButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  periodButtonDesc: {
    fontSize: 12,
    marginTop: 4,
  },
  applyFilterButton: {
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginTop: 20,
  },
  applyFilterText: {
    fontSize: 16,
    fontWeight: '600',
  },
  comparisonModalContent: {
    maxHeight: 400,
  },
  comparisonDetail: {
    gap: 16,
  },
  comparisonDetailTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  comparisonMetric: {
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  metricLabel: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  metricValue: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  metricGrowth: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  transactionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  transactionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  transactionDetails: {
    marginLeft: 12,
    flex: 1,
  },
  transactionDescription: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 2,
  },
  transactionCategory: {
    fontSize: 12,
  },
  transactionAmount: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  comingSoonTeaser: {
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  comingSoonHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  comingSoonTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginLeft: 12,
  },
  comingSoonDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 16,
  },
  comingSoonButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: 'center',
  },
  comingSoonButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  comingSoonList: {
    maxHeight: 400,
  },
  featureItem: {
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  featureHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 12,
    flex: 1,
  },
  etaBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  etaText: {
    fontSize: 10,
    fontWeight: '600',
  },
  featureDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginLeft: 36,
  },
  comingSoonFooter: {
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    marginTop: 16,
  },
  comingSoonFooterText: {
    fontSize: 12,
    textAlign: 'center',
    fontStyle: 'italic',
  },
});
