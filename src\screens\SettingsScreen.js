import React, { useState } from 'react';
import { View, StyleSheet, Text, ScrollView, TouchableOpacity, Alert, ActivityIndicator } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { MaterialIcons } from '@expo/vector-icons';
import { useSQLiteContext } from 'expo-sqlite';
import { Colors } from '../constants/colors';
import { useAppContext } from '../context/AppContext';
import { useBackupService } from '../services/backupService';

/**
 * Ayarlar ekranı
 *
 * @returns {JSX.Element} Ayarlar ekranı
 */
export default function SettingsScreen({ navigation }) {
  const insets = useSafeAreaInsets();
  const db = useSQLiteContext();
  const backupService = useBackupService(db);
  const { theme, themePreference, toggleTheme } = useAppContext();
  const [isLoading, setIsLoading] = useState(false);

  return (
    <View style={[styles.container, { paddingTop: insets.top, backgroundColor: theme.BACKGROUND }]}>
      <View style={[styles.header, { backgroundColor: theme.PRIMARY }]}>
        <Text style={[styles.title, { color: theme.WHITE }]}>Ayarlar</Text>
      </View>

      <ScrollView style={[styles.content, { backgroundColor: theme.BACKGROUND }]}>
        <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>Görünüm</Text>

        {/* Tema Ayarları */}
        <View style={[styles.settingItem, { backgroundColor: theme.CARD }]}>
          <View style={styles.settingInfo}>
            <MaterialIcons name="palette" size={24} color={theme.PRIMARY} />
            <Text style={[styles.settingTitle, { color: theme.TEXT_PRIMARY }]}>Tema</Text>
          </View>
          <TouchableOpacity
            style={[styles.themeSelector, { borderColor: theme.BORDER }]}
            onPress={() => {
              Alert.alert(
                'Tema Seçin',
                'Hangi temayı kullanmak istiyorsunuz?',
                [
                  { text: 'Açık', onPress: () => toggleTheme('light') },
                  { text: 'Koyu', onPress: () => toggleTheme('dark') },
                  { text: 'Sistem', onPress: () => toggleTheme('system') },
                  { text: 'İptal', style: 'cancel' }
                ]
              );
            }}
          >
            <Text style={[styles.themeSelectorText, { color: theme.TEXT_SECONDARY }]}>
              {themePreference === 'light' ? 'Açık' :
               themePreference === 'dark' ? 'Koyu' : 'Sistem'}
            </Text>
            <MaterialIcons name="chevron-right" size={20} color={theme.TEXT_SECONDARY} />
          </TouchableOpacity>
        </View>

        <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>Güvenlik</Text>

        {/* Güvenlik Ayarları */}
        <TouchableOpacity
          style={[styles.settingItem, { backgroundColor: theme.CARD }]}
          onPress={() => navigation.navigate('SecuritySettings')}
        >
          <View style={styles.settingInfo}>
            <MaterialIcons name="security" size={24} color={theme.PRIMARY} />
            <Text style={[styles.settingTitle, { color: theme.TEXT_PRIMARY }]}>Güvenlik Ayarları</Text>
          </View>
          <MaterialIcons name="chevron-right" size={24} color={theme.TEXT_SECONDARY} />
        </TouchableOpacity>

        <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>Veri Yönetimi</Text>

        {/* Kategori Yönetimi */}
        <TouchableOpacity
          style={[styles.settingItem, { backgroundColor: theme.CARD }]}
          onPress={() => navigation.navigate('Categories')}
        >
          <View style={styles.settingInfo}>
            <MaterialIcons name="category" size={24} color={theme.PRIMARY} />
            <Text style={[styles.settingTitle, { color: theme.TEXT_PRIMARY }]}>Kategori Yönetimi</Text>
          </View>
          <MaterialIcons name="chevron-right" size={24} color={theme.TEXT_SECONDARY} />
        </TouchableOpacity>

        {/* Para Birimi Ayarları */}
        <TouchableOpacity
          style={[styles.settingItem, { backgroundColor: theme.CARD }]}
          onPress={() => navigation.navigate('CurrencySettings')}
        >
          <View style={styles.settingInfo}>
            <MaterialIcons name="currency-exchange" size={24} color={theme.PRIMARY} />
            <Text style={[styles.settingTitle, { color: theme.TEXT_PRIMARY }]}>Para Birimi Ayarları</Text>
          </View>
          <MaterialIcons name="chevron-right" size={24} color={theme.TEXT_SECONDARY} />
        </TouchableOpacity>

        {/* Veritabanı Yedekleme */}
        <TouchableOpacity
          style={[styles.settingItem, { backgroundColor: theme.CARD }]}
          onPress={async () => {
            try {
              setIsLoading(true);
              await backupService.backupDatabase();
            } catch (error) {
              console.error('Yedekleme hatası:', error);
              Alert.alert('Hata', 'Veritabanı yedeklenirken bir hata oluştu.');
            } finally {
              setIsLoading(false);
            }
          }}
          disabled={isLoading}
        >
          <View style={styles.settingInfo}>
            <MaterialIcons name="backup" size={24} color={theme.PRIMARY} />
            <Text style={[styles.settingTitle, { color: theme.TEXT_PRIMARY }]}>Veritabanını Yedekle</Text>
          </View>
          {isLoading ? (
            <ActivityIndicator size="small" color={theme.PRIMARY} />
          ) : (
            <MaterialIcons name="chevron-right" size={24} color={theme.TEXT_SECONDARY} />
          )}
        </TouchableOpacity>

        {/* Veritabanı Geri Yükleme */}
        <TouchableOpacity
          style={[styles.settingItem, { backgroundColor: theme.CARD }]}
          onPress={async () => {
            try {
              setIsLoading(true);
              await backupService.restoreDatabase();
            } catch (error) {
              console.error('Geri yükleme hatası:', error);
              Alert.alert('Hata', 'Veritabanı geri yüklenirken bir hata oluştu.');
            } finally {
              setIsLoading(false);
            }
          }}
          disabled={isLoading}
        >
          <View style={styles.settingInfo}>
            <MaterialIcons name="restore" size={24} color={theme.PRIMARY} />
            <Text style={[styles.settingTitle, { color: theme.TEXT_PRIMARY }]}>Veritabanını Geri Yükle</Text>
          </View>
          {isLoading ? (
            <ActivityIndicator size="small" color={theme.PRIMARY} />
          ) : (
            <MaterialIcons name="chevron-right" size={24} color={theme.TEXT_SECONDARY} />
          )}
        </TouchableOpacity>

        {/* JSON Dışa Aktarma */}
        <TouchableOpacity
          style={[styles.settingItem, { backgroundColor: theme.CARD }]}
          onPress={async () => {
            try {
              setIsLoading(true);
              await backupService.exportDataAsJson();
            } catch (error) {
              console.error('JSON dışa aktarma hatası:', error);
              Alert.alert('Hata', 'Veriler dışa aktarılırken bir hata oluştu.');
            } finally {
              setIsLoading(false);
            }
          }}
          disabled={isLoading}
        >
          <View style={styles.settingInfo}>
            <MaterialIcons name="file-download" size={24} color={theme.PRIMARY} />
            <Text style={[styles.settingTitle, { color: theme.TEXT_PRIMARY }]}>JSON Olarak Dışa Aktar</Text>
          </View>
          {isLoading ? (
            <ActivityIndicator size="small" color={theme.PRIMARY} />
          ) : (
            <MaterialIcons name="chevron-right" size={24} color={theme.TEXT_SECONDARY} />
          )}
        </TouchableOpacity>

        {/* PDF Rapor */}
        <TouchableOpacity
          style={[styles.settingItem, { backgroundColor: theme.CARD }]}
          onPress={async () => {
            try {
              setIsLoading(true);
              await backupService.generatePdfReport();
            } catch (error) {
              console.error('PDF rapor hatası:', error);
              Alert.alert('Hata', 'PDF rapor oluşturulurken bir hata oluştu.');
            } finally {
              setIsLoading(false);
            }
          }}
          disabled={isLoading}
        >
          <View style={styles.settingInfo}>
            <MaterialIcons name="picture-as-pdf" size={24} color={theme.PRIMARY} />
            <Text style={[styles.settingTitle, { color: theme.TEXT_PRIMARY }]}>PDF Rapor Oluştur</Text>
          </View>
          {isLoading ? (
            <ActivityIndicator size="small" color={theme.PRIMARY} />
          ) : (
            <MaterialIcons name="chevron-right" size={24} color={theme.TEXT_SECONDARY} />
          )}
        </TouchableOpacity>

        <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>Tehlikeli Bölge</Text>

        {/* Tüm Verileri Sil */}
        <TouchableOpacity
          style={[styles.settingItem, { backgroundColor: theme.CARD }]}
          onPress={() => {
            Alert.alert(
              'Tüm Verileri Sil',
              'Bu işlem tüm verilerinizi kalıcı olarak silecek ve geri alınamaz. Devam etmek istiyor musunuz?',
              [
                { text: 'İptal', style: 'cancel' },
                {
                  text: 'Sil',
                  style: 'destructive',
                  onPress: async () => {
                    try {
                      setIsLoading(true);

                      // Tüm tabloları temizle
                      await db.runAsync('DELETE FROM transactions');
                      await db.runAsync('DELETE FROM categories');

                      Alert.alert('Başarılı', 'Tüm veriler başarıyla silindi.');
                    } catch (error) {
                      console.error('Veri silme hatası:', error);
                      Alert.alert('Hata', 'Veriler silinirken bir hata oluştu.');
                    } finally {
                      setIsLoading(false);
                    }
                  }
                }
              ]
            );
          }}
          disabled={isLoading}
        >
          <View style={styles.settingInfo}>
            <MaterialIcons name="delete-forever" size={24} color="#e74c3c" />
            <Text style={[styles.settingTitle, { color: '#e74c3c' }]}>Tüm Verileri Sil</Text>
          </View>
          {isLoading ? (
            <ActivityIndicator size="small" color="#e74c3c" />
          ) : (
            <MaterialIcons name="chevron-right" size={24} color={theme.TEXT_SECONDARY} />
          )}
        </TouchableOpacity>

        <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>Hakkında</Text>

        {/* Uygulama Bilgisi */}
        <View style={[styles.settingItem, { backgroundColor: theme.CARD }]}>
          <View style={styles.settingInfo}>
            <MaterialIcons name="info" size={24} color={theme.PRIMARY} />
            <Text style={[styles.settingTitle, { color: theme.TEXT_PRIMARY }]}>Sürüm</Text>
          </View>
          <Text style={[styles.versionText, { color: theme.TEXT_SECONDARY }]}>1.0.0</Text>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 24,
    marginBottom: 16,
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  settingInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    marginLeft: 12,
    flex: 1,
  },
  versionText: {
    fontSize: 14,
    color: '#999',
  },
  themeSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
  },
  themeSelectorText: {
    fontSize: 14,
    marginRight: 8,
  },
});
