import React, { useState, useEffect } from 'react';
import { View, StyleSheet, Text, ScrollView, TouchableOpacity, Switch, Alert, ActivityIndicator } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { MaterialIcons } from '@expo/vector-icons';
import { useSQLiteContext } from 'expo-sqlite';
import { Colors } from '../constants/colors';
import { authService } from '../services/AuthService';
import { pinService } from '../services/pinService';
import { useBackupService } from '../services/backupService';

/**
 * Ayarlar ekranı
 *
 * @returns {JSX.Element} Ayarlar ekranı
 */
export default function SettingsScreen({ navigation }) {
  const insets = useSafeAreaInsets();
  const db = useSQLiteContext();
  const backupService = useBackupService(db);

  const [isBiometricAvailable, setIsBiometricAvailable] = useState(false);
  const [isBiometricEnabled, setIsBiometricEnabled] = useState(false);
  const [isPinEnabled, setIsPinEnabled] = useState(false);
  const [isPinSet, setIsPinSet] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Biyometrik kimlik doğrulama durumunu kontrol et
  useEffect(() => {
    const checkBiometricStatus = async () => {
      try {
        const isAvailable = await authService.isBiometricAvailable();
        setIsBiometricAvailable(isAvailable);

        if (isAvailable) {
          const isEnabled = await authService.isBiometricEnabled();
          setIsBiometricEnabled(isEnabled);
        }
      } catch (error) {
        console.error('Biyometrik durum kontrolü hatası:', error);
      }
    };

    checkBiometricStatus();
  }, []);

  // PIN durumunu kontrol et
  useEffect(() => {
    const checkPinStatus = async () => {
      try {
        const isEnabled = await pinService.isPinEnabled();
        setIsPinEnabled(isEnabled);

        const isPinSetup = await pinService.hasPin();
        setIsPinSet(isPinSetup);
      } catch (error) {
        console.error('PIN durum kontrolü hatası:', error);
      }
    };

    checkPinStatus();
  }, []);

  // Biyometrik kimlik doğrulama durumunu değiştir
  const toggleBiometric = async (value) => {
    try {
      if (value) {
        // Biyometrik kimlik doğrulamayı etkinleştir
        await authService.setAuthEnabled(true);
        setIsBiometricEnabled(true);
      } else {
        // Biyometrik kimlik doğrulamayı devre dışı bırak
        await authService.setAuthEnabled(false);
        setIsBiometricEnabled(false);
      }
    } catch (error) {
      console.error('Biyometrik değiştirme hatası:', error);
      Alert.alert('Hata', 'Biyometrik kimlik doğrulama ayarları değiştirilirken bir hata oluştu.');
    }
  };

  // PIN durumunu değiştir
  const togglePin = async (value) => {
    try {
      if (value) {
        // PIN'i etkinleştir
        if (isPinSet) {
          // PIN zaten ayarlanmışsa, etkinleştir
          setIsPinEnabled(true);
        } else {
          // PIN ayarlanmamışsa, PIN ayarlama ekranına yönlendir
          navigation.navigate('pin', { mode: 'setup' });
        }
      } else {
        // PIN'i devre dışı bırak
        setIsPinEnabled(false);
      }
    } catch (error) {
      console.error('PIN değiştirme hatası:', error);
      Alert.alert('Hata', 'PIN ayarları değiştirilirken bir hata oluştu.');
    }
  };

  // PIN'i değiştir
  const changePin = () => {
    navigation.navigate('pin', { mode: 'change' });
  };

  return (
    <View style={[styles.container, { paddingTop: insets.top }]}>
      <View style={styles.header}>
        <Text style={styles.title}>Ayarlar</Text>
      </View>

      <ScrollView style={styles.content}>
        <Text style={styles.sectionTitle}>Güvenlik</Text>

        {/* Biyometrik Kimlik Doğrulama */}
        {isBiometricAvailable && (
          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <MaterialIcons name="fingerprint" size={24} color={Colors.PRIMARY} />
              <Text style={styles.settingTitle}>Biyometrik Kimlik Doğrulama</Text>
            </View>
            <Switch
              value={isBiometricEnabled}
              onValueChange={toggleBiometric}
              trackColor={{ false: '#767577', true: Colors.PRIMARY_LIGHT }}
              thumbColor={isBiometricEnabled ? Colors.PRIMARY : '#f4f3f4'}
            />
          </View>
        )}

        {/* PIN Kodu */}
        <View style={styles.settingItem}>
          <View style={styles.settingInfo}>
            <MaterialIcons name="lock" size={24} color={Colors.PRIMARY} />
            <Text style={styles.settingTitle}>PIN Kodu</Text>
          </View>
          <Switch
            value={isPinEnabled}
            onValueChange={togglePin}
            trackColor={{ false: '#767577', true: Colors.PRIMARY_LIGHT }}
            thumbColor={isPinEnabled ? Colors.PRIMARY : '#f4f3f4'}
          />
        </View>

        {/* PIN Değiştir */}
        {isPinSet && (
          <TouchableOpacity
            style={styles.settingItem}
            onPress={changePin}
          >
            <View style={styles.settingInfo}>
              <MaterialIcons name="vpn-key" size={24} color={Colors.PRIMARY} />
              <Text style={styles.settingTitle}>PIN Kodunu Değiştir</Text>
            </View>
            <MaterialIcons name="chevron-right" size={24} color="#999" />
          </TouchableOpacity>
        )}

        <Text style={styles.sectionTitle}>Veri Yönetimi</Text>

        {/* Kategori Yönetimi */}
        <TouchableOpacity
          style={styles.settingItem}
          onPress={() => navigation.navigate('Categories')}
        >
          <View style={styles.settingInfo}>
            <MaterialIcons name="category" size={24} color={Colors.PRIMARY} />
            <Text style={styles.settingTitle}>Kategori Yönetimi</Text>
          </View>
          <MaterialIcons name="chevron-right" size={24} color="#999" />
        </TouchableOpacity>

        {/* Para Birimi Ayarları */}
        <TouchableOpacity
          style={styles.settingItem}
          onPress={() => navigation.navigate('CurrencySettings')}
        >
          <View style={styles.settingInfo}>
            <MaterialIcons name="currency-exchange" size={24} color={Colors.PRIMARY} />
            <Text style={styles.settingTitle}>Para Birimi Ayarları</Text>
          </View>
          <MaterialIcons name="chevron-right" size={24} color="#999" />
        </TouchableOpacity>

        {/* Veritabanı Yedekleme */}
        <TouchableOpacity
          style={styles.settingItem}
          onPress={async () => {
            try {
              setIsLoading(true);
              await backupService.backupDatabase();
            } catch (error) {
              console.error('Yedekleme hatası:', error);
              Alert.alert('Hata', 'Veritabanı yedeklenirken bir hata oluştu.');
            } finally {
              setIsLoading(false);
            }
          }}
          disabled={isLoading}
        >
          <View style={styles.settingInfo}>
            <MaterialIcons name="backup" size={24} color={Colors.PRIMARY} />
            <Text style={styles.settingTitle}>Veritabanını Yedekle</Text>
          </View>
          {isLoading ? (
            <ActivityIndicator size="small" color={Colors.PRIMARY} />
          ) : (
            <MaterialIcons name="chevron-right" size={24} color="#999" />
          )}
        </TouchableOpacity>

        {/* Veritabanı Geri Yükleme */}
        <TouchableOpacity
          style={styles.settingItem}
          onPress={async () => {
            try {
              setIsLoading(true);
              await backupService.restoreDatabase();
            } catch (error) {
              console.error('Geri yükleme hatası:', error);
              Alert.alert('Hata', 'Veritabanı geri yüklenirken bir hata oluştu.');
            } finally {
              setIsLoading(false);
            }
          }}
          disabled={isLoading}
        >
          <View style={styles.settingInfo}>
            <MaterialIcons name="restore" size={24} color={Colors.PRIMARY} />
            <Text style={styles.settingTitle}>Veritabanını Geri Yükle</Text>
          </View>
          {isLoading ? (
            <ActivityIndicator size="small" color={Colors.PRIMARY} />
          ) : (
            <MaterialIcons name="chevron-right" size={24} color="#999" />
          )}
        </TouchableOpacity>

        {/* JSON Dışa Aktarma */}
        <TouchableOpacity
          style={styles.settingItem}
          onPress={async () => {
            try {
              setIsLoading(true);
              await backupService.exportDataAsJson();
            } catch (error) {
              console.error('JSON dışa aktarma hatası:', error);
              Alert.alert('Hata', 'Veriler dışa aktarılırken bir hata oluştu.');
            } finally {
              setIsLoading(false);
            }
          }}
          disabled={isLoading}
        >
          <View style={styles.settingInfo}>
            <MaterialIcons name="file-download" size={24} color={Colors.PRIMARY} />
            <Text style={styles.settingTitle}>JSON Olarak Dışa Aktar</Text>
          </View>
          {isLoading ? (
            <ActivityIndicator size="small" color={Colors.PRIMARY} />
          ) : (
            <MaterialIcons name="chevron-right" size={24} color="#999" />
          )}
        </TouchableOpacity>

        {/* PDF Rapor */}
        <TouchableOpacity
          style={styles.settingItem}
          onPress={async () => {
            try {
              setIsLoading(true);
              await backupService.generatePdfReport();
            } catch (error) {
              console.error('PDF rapor hatası:', error);
              Alert.alert('Hata', 'PDF rapor oluşturulurken bir hata oluştu.');
            } finally {
              setIsLoading(false);
            }
          }}
          disabled={isLoading}
        >
          <View style={styles.settingInfo}>
            <MaterialIcons name="picture-as-pdf" size={24} color={Colors.PRIMARY} />
            <Text style={styles.settingTitle}>PDF Rapor Oluştur</Text>
          </View>
          {isLoading ? (
            <ActivityIndicator size="small" color={Colors.PRIMARY} />
          ) : (
            <MaterialIcons name="chevron-right" size={24} color="#999" />
          )}
        </TouchableOpacity>

        <Text style={styles.sectionTitle}>Tehlikeli Bölge</Text>

        {/* Tüm Verileri Sil */}
        <TouchableOpacity
          style={styles.settingItem}
          onPress={() => {
            Alert.alert(
              'Tüm Verileri Sil',
              'Bu işlem tüm verilerinizi kalıcı olarak silecek ve geri alınamaz. Devam etmek istiyor musunuz?',
              [
                { text: 'İptal', style: 'cancel' },
                {
                  text: 'Sil',
                  style: 'destructive',
                  onPress: async () => {
                    try {
                      setIsLoading(true);

                      // Tüm tabloları temizle
                      await db.runAsync('DELETE FROM transactions');
                      await db.runAsync('DELETE FROM categories');

                      Alert.alert('Başarılı', 'Tüm veriler başarıyla silindi.');
                    } catch (error) {
                      console.error('Veri silme hatası:', error);
                      Alert.alert('Hata', 'Veriler silinirken bir hata oluştu.');
                    } finally {
                      setIsLoading(false);
                    }
                  }
                }
              ]
            );
          }}
          disabled={isLoading}
        >
          <View style={styles.settingInfo}>
            <MaterialIcons name="delete-forever" size={24} color="#e74c3c" />
            <Text style={[styles.settingTitle, { color: '#e74c3c' }]}>Tüm Verileri Sil</Text>
          </View>
          {isLoading ? (
            <ActivityIndicator size="small" color="#e74c3c" />
          ) : (
            <MaterialIcons name="chevron-right" size={24} color="#999" />
          )}
        </TouchableOpacity>

        <Text style={styles.sectionTitle}>Hakkında</Text>

        {/* Uygulama Bilgisi */}
        <View style={styles.settingItem}>
          <View style={styles.settingInfo}>
            <MaterialIcons name="info" size={24} color={Colors.PRIMARY} />
            <Text style={styles.settingTitle}>Sürüm</Text>
          </View>
          <Text style={styles.versionText}>1.0.0</Text>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: Colors.PRIMARY,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
    marginTop: 24,
    marginBottom: 16,
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  settingInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  settingTitle: {
    fontSize: 16,
    color: '#333333',
    marginLeft: 12,
  },
  versionText: {
    fontSize: 14,
    color: '#999',
  },
});
