import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  TextInput,
  Alert,
  RefreshControl,
  Modal
} from 'react-native';
import { useSQLiteContext } from 'expo-sqlite';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useFocusEffect } from '@react-navigation/native';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors } from '../constants/colors';
import { useAppContext } from '../context/AppContext';
import { formatCurrency } from '../utils/formatters';

/**
 * Alışveriş Listesi Ekranı
 * Alışveriş öğelerini yönetme ve fiyat takibi
 */
export default function ShoppingScreen({ navigation }) {
  const db = useSQLiteContext();
  const insets = useSafeAreaInsets();
  const { theme } = useAppContext();

  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [shoppingItems, setShoppingItems] = useState([]);
  const [showAddModal, setShowAddModal] = useState(false);
  const [newItem, setNewItem] = useState({
    name: '',
    quantity: '1',
    estimatedPrice: '',
    category: '',
    notes: ''
  });

  // Veritabanı tablosunu oluştur
  const initializeDatabase = useCallback(async () => {
    try {
      await db.execAsync(`
        CREATE TABLE IF NOT EXISTS shopping_items (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          quantity INTEGER DEFAULT 1,
          estimated_price REAL DEFAULT 0,
          actual_price REAL DEFAULT 0,
          category TEXT,
          notes TEXT,
          is_purchased BOOLEAN DEFAULT 0,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          purchased_at DATETIME
        );
      `);
    } catch (error) {
      console.error('Alışveriş veritabanı oluşturma hatası:', error);
    }
  }, [db]);

  // Alışveriş öğelerini yükle
  const loadShoppingItems = useCallback(async () => {
    try {
      setLoading(true);
      await initializeDatabase();

      const items = await db.getAllAsync(`
        SELECT * FROM shopping_items 
        ORDER BY is_purchased ASC, created_at DESC
      `);

      setShoppingItems(items);
      setLoading(false);
    } catch (error) {
      console.error('Alışveriş öğeleri yükleme hatası:', error);
      setLoading(false);
    }
  }, [db, initializeDatabase]);

  // Ekran odaklandığında verileri yükle
  useFocusEffect(
    useCallback(() => {
      loadShoppingItems();
    }, [loadShoppingItems])
  );

  // Yenileme işlemi
  const onRefresh = async () => {
    setRefreshing(true);
    await loadShoppingItems();
    setRefreshing(false);
  };

  // Yeni öğe ekle
  const addShoppingItem = async () => {
    try {
      if (!newItem.name.trim()) {
        Alert.alert('Hata', 'Lütfen ürün adını girin.');
        return;
      }

      await db.runAsync(`
        INSERT INTO shopping_items (name, quantity, estimated_price, category, notes)
        VALUES (?, ?, ?, ?, ?)
      `, [
        newItem.name.trim(),
        parseInt(newItem.quantity) || 1,
        parseFloat(newItem.estimatedPrice) || 0,
        newItem.category.trim(),
        newItem.notes.trim()
      ]);

      setNewItem({
        name: '',
        quantity: '1',
        estimatedPrice: '',
        category: '',
        notes: ''
      });
      setShowAddModal(false);
      loadShoppingItems();
    } catch (error) {
      console.error('Alışveriş öğesi ekleme hatası:', error);
      Alert.alert('Hata', 'Öğe eklenirken bir hata oluştu.');
    }
  };

  // Öğeyi satın alındı olarak işaretle
  const markAsPurchased = async (item) => {
    try {
      await db.runAsync(`
        UPDATE shopping_items 
        SET is_purchased = 1, purchased_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `, [item.id]);

      loadShoppingItems();
    } catch (error) {
      console.error('Öğe güncelleme hatası:', error);
      Alert.alert('Hata', 'Öğe güncellenirken bir hata oluştu.');
    }
  };

  // Öğeyi sil
  const deleteItem = async (item) => {
    Alert.alert(
      'Öğeyi Sil',
      `"${item.name}" öğesini silmek istediğinize emin misiniz?`,
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: async () => {
            try {
              await db.runAsync('DELETE FROM shopping_items WHERE id = ?', [item.id]);
              loadShoppingItems();
            } catch (error) {
              console.error('Öğe silme hatası:', error);
              Alert.alert('Hata', 'Öğe silinirken bir hata oluştu.');
            }
          }
        }
      ]
    );
  };

  // Toplam tahmini fiyat
  const totalEstimatedPrice = shoppingItems
    .filter(item => !item.is_purchased)
    .reduce((sum, item) => sum + (item.estimated_price * item.quantity), 0);

  // Alışveriş öğesi render fonksiyonu
  const renderShoppingItem = ({ item }) => (
    <View style={[styles.itemContainer, { backgroundColor: theme.CARD }]}>
      <TouchableOpacity
        style={styles.itemContent}
        onPress={() => markAsPurchased(item)}
        disabled={item.is_purchased}
      >
        <View style={styles.itemLeft}>
          <View style={[
            styles.checkbox,
            item.is_purchased && styles.checkedBox,
            { borderColor: theme.BORDER }
          ]}>
            {item.is_purchased && (
              <MaterialIcons name="check" size={16} color={theme.WHITE} />
            )}
          </View>
          
          <View style={styles.itemDetails}>
            <Text style={[
              styles.itemName,
              { color: theme.TEXT_PRIMARY },
              item.is_purchased && styles.purchasedText
            ]}>
              {item.name}
            </Text>
            
            <View style={styles.itemMeta}>
              <Text style={[styles.itemQuantity, { color: theme.TEXT_SECONDARY }]}>
                Miktar: {item.quantity}
              </Text>
              {item.estimated_price > 0 && (
                <Text style={[styles.itemPrice, { color: theme.TEXT_SECONDARY }]}>
                  {formatCurrency(item.estimated_price * item.quantity, 'TRY')}
                </Text>
              )}
            </View>
            
            {item.category && (
              <Text style={[styles.itemCategory, { color: theme.PRIMARY }]}>
                {item.category}
              </Text>
            )}
          </View>
        </View>

        <TouchableOpacity
          style={styles.deleteButton}
          onPress={() => deleteItem(item)}
        >
          <MaterialIcons name="delete" size={20} color={theme.TEXT_SECONDARY} />
        </TouchableOpacity>
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={[styles.container, { paddingTop: insets.top, backgroundColor: theme.BACKGROUND }]}>
      <View style={[styles.header, { backgroundColor: theme.PRIMARY }]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <MaterialIcons name="arrow-back" size={24} color={theme.WHITE} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: theme.WHITE }]}>Alışveriş Listesi</Text>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => setShowAddModal(true)}
        >
          <MaterialIcons name="add" size={24} color={theme.WHITE} />
        </TouchableOpacity>
      </View>

      {/* Özet */}
      <View style={[styles.summary, { backgroundColor: theme.CARD }]}>
        <View style={styles.summaryItem}>
          <Text style={[styles.summaryLabel, { color: theme.TEXT_SECONDARY }]}>Toplam Öğe</Text>
          <Text style={[styles.summaryValue, { color: theme.TEXT_PRIMARY }]}>
            {shoppingItems.filter(item => !item.is_purchased).length}
          </Text>
        </View>
        <View style={styles.summaryItem}>
          <Text style={[styles.summaryLabel, { color: theme.TEXT_SECONDARY }]}>Tahmini Tutar</Text>
          <Text style={[styles.summaryValue, { color: theme.PRIMARY }]}>
            {formatCurrency(totalEstimatedPrice, 'TRY')}
          </Text>
        </View>
      </View>

      <FlatList
        data={shoppingItems}
        renderItem={renderShoppingItem}
        keyExtractor={item => item.id.toString()}
        contentContainerStyle={styles.listContent}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <MaterialIcons name="shopping-cart" size={64} color={theme.TEXT_SECONDARY} />
            <Text style={[styles.emptyText, { color: theme.TEXT_PRIMARY }]}>
              Alışveriş listeniz boş
            </Text>
            <Text style={[styles.emptySubtext, { color: theme.TEXT_SECONDARY }]}>
              + butonuna tıklayarak öğe ekleyebilirsiniz
            </Text>
          </View>
        }
      />

      {/* Yeni Öğe Ekleme Modalı */}
      <Modal
        visible={showAddModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowAddModal(false)}
      >
        <View style={styles.modalContainer}>
          <View style={[styles.modalContent, { backgroundColor: theme.CARD }]}>
            <View style={[styles.modalHeader, { borderBottomColor: theme.BORDER }]}>
              <Text style={[styles.modalTitle, { color: theme.TEXT_PRIMARY }]}>Yeni Öğe Ekle</Text>
              <TouchableOpacity onPress={() => setShowAddModal(false)}>
                <MaterialIcons name="close" size={24} color={theme.TEXT_SECONDARY} />
              </TouchableOpacity>
            </View>

            <View style={styles.modalBody}>
              <TextInput
                style={[styles.input, { borderColor: theme.BORDER, backgroundColor: theme.SURFACE, color: theme.TEXT_PRIMARY }]}
                placeholder="Ürün adı"
                placeholderTextColor={theme.TEXT_SECONDARY}
                value={newItem.name}
                onChangeText={(text) => setNewItem({ ...newItem, name: text })}
              />

              <View style={styles.row}>
                <TextInput
                  style={[styles.input, styles.halfInput, { borderColor: theme.BORDER, backgroundColor: theme.SURFACE, color: theme.TEXT_PRIMARY }]}
                  placeholder="Miktar"
                  placeholderTextColor={theme.TEXT_SECONDARY}
                  value={newItem.quantity}
                  onChangeText={(text) => setNewItem({ ...newItem, quantity: text })}
                  keyboardType="numeric"
                />

                <TextInput
                  style={[styles.input, styles.halfInput, { borderColor: theme.BORDER, backgroundColor: theme.SURFACE, color: theme.TEXT_PRIMARY }]}
                  placeholder="Tahmini fiyat"
                  placeholderTextColor={theme.TEXT_SECONDARY}
                  value={newItem.estimatedPrice}
                  onChangeText={(text) => setNewItem({ ...newItem, estimatedPrice: text })}
                  keyboardType="numeric"
                />
              </View>

              <TextInput
                style={[styles.input, { borderColor: theme.BORDER, backgroundColor: theme.SURFACE, color: theme.TEXT_PRIMARY }]}
                placeholder="Kategori"
                placeholderTextColor={theme.TEXT_SECONDARY}
                value={newItem.category}
                onChangeText={(text) => setNewItem({ ...newItem, category: text })}
              />

              <TextInput
                style={[styles.input, styles.textArea, { borderColor: theme.BORDER, backgroundColor: theme.SURFACE, color: theme.TEXT_PRIMARY }]}
                placeholder="Notlar"
                placeholderTextColor={theme.TEXT_SECONDARY}
                value={newItem.notes}
                onChangeText={(text) => setNewItem({ ...newItem, notes: text })}
                multiline
                numberOfLines={3}
              />
            </View>

            <View style={styles.modalFooter}>
              <TouchableOpacity
                style={[styles.button, styles.cancelButton, { backgroundColor: theme.GRAY_200 }]}
                onPress={() => setShowAddModal(false)}
              >
                <Text style={[styles.buttonText, { color: theme.TEXT_PRIMARY }]}>İptal</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.button, styles.saveButton, { backgroundColor: theme.PRIMARY }]}
                onPress={addShoppingItem}
              >
                <Text style={[styles.buttonText, { color: theme.WHITE }]}>Ekle</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  backButton: {
    padding: 8,
    marginRight: 8,
  },
  headerTitle: {
    flex: 1,
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  addButton: {
    padding: 8,
    marginLeft: 8,
  },
  summary: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    padding: 16,
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  summaryItem: {
    alignItems: 'center',
  },
  summaryLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  summaryValue: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  listContent: {
    padding: 16,
    paddingTop: 8,
  },
  itemContainer: {
    borderRadius: 12,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  itemContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  itemLeft: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkedBox: {
    backgroundColor: Colors.SUCCESS,
    borderColor: Colors.SUCCESS,
  },
  itemDetails: {
    flex: 1,
  },
  itemName: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  purchasedText: {
    textDecorationLine: 'line-through',
    opacity: 0.6,
  },
  itemMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  itemQuantity: {
    fontSize: 14,
  },
  itemPrice: {
    fontSize: 14,
    fontWeight: '500',
  },
  itemCategory: {
    fontSize: 12,
    fontWeight: '500',
  },
  deleteButton: {
    padding: 8,
  },
  emptyContainer: {
    alignItems: 'center',
    padding: 32,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    textAlign: 'center',
  },
  emptySubtext: {
    fontSize: 14,
    marginTop: 8,
    textAlign: 'center',
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    padding: 20,
  },
  modalContent: {
    borderRadius: 16,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  modalBody: {
    padding: 16,
  },
  input: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
    marginBottom: 16,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  halfInput: {
    width: '48%',
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
  },
  button: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginHorizontal: 8,
  },
  cancelButton: {
    marginRight: 8,
  },
  saveButton: {
    marginLeft: 8,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '500',
  },
});
