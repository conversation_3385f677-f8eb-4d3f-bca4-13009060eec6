import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  ScrollView,
  Switch,
  ActivityIndicator
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors } from '../constants/colors';

// Basit renk değişkenleri
const safeWhite = '#FFFFFF';
const safePrimary = Colors.PRIMARY;
const safeNeutral = {
  50: '#F5F5F5',
  100: '#EEEEEE',
  200: '#E0E0E0',
  300: '#BDBDBD',
  400: '#9E9E9E',
  500: '#757575',
  600: '#616161',
  700: '#424242',
  800: '#212121',
  900: '#000000'
};

/**
 * Tab Bar Özelleştirme Ekranı
 * Kullanıcıların bottom bar'ı düzenlemelerini sağlar
 */
const TabBarCustomizationScreen = () => {
  const [loading, setLoading] = useState(false);
  const [selectedTabs, setSelectedTabs] = useState([
    { id: 'home', title: 'Ana Say<PERSON>', icon: 'home' },
    { id: 'transactions', title: '<PERSON><PERSON><PERSON>ler', icon: 'receipt-long' },
    { id: 'features', title: 'Özellikler', icon: 'apps' },
    { id: 'settings', title: 'Ayarlar', icon: 'settings' }
  ]);
  const [availableTabs, setAvailableTabs] = useState([
    { id: 'investment', title: 'Yatırımlar', icon: 'account-balance' },
    { id: 'work', title: 'Mesai', icon: 'access-time' },
    { id: 'stats', title: 'İstatistikler', icon: 'bar-chart' }
  ]);
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  // Tab'leri sürükleyerek sıralama işlemi
  const handleDragEnd = ({ data }) => {
    setSelectedTabs(data);
    setHasChanges(true);
  };

  // Tab'i bottom bar'dan kaldır
  const removeTab = (tabId) => {
    // Seçili tab'in sayısı 2'den fazla olmalı
    if (selectedTabs.length <= 2) {
      Alert.alert(
        'Dikkat',
        'En az 2 sekme olmalıdır. Kaldırmadan önce başka bir sekme ekleyin.'
      );
      return;
    }

    // Tab'i kaldır
    const removedTab = selectedTabs.find(tab => tab.id === tabId);
    const newSelectedTabs = selectedTabs.filter(tab => tab.id !== tabId);

    setSelectedTabs(newSelectedTabs);
    setAvailableTabs([...availableTabs, removedTab]);
    setHasChanges(true);
  };

  // Yeni tab ekle
  const addTab = (tab) => {
    // Seçili tab sayısı 5'ten az olmalı
    if (selectedTabs.length >= 5) {
      Alert.alert(
        'Dikkat',
        'En fazla 5 sekme eklenebilir. Yeni sekme eklemeden önce mevcut sekmelerden birini kaldırın.'
      );
      return;
    }

    // Tab'i ekle
    setSelectedTabs([...selectedTabs, tab]);
    setAvailableTabs(availableTabs.filter(t => t.id !== tab.id));
    setHasChanges(true);
  };

  // Varsayılan yapılandırmayı geri yükle
  const resetToDefault = () => {
    Alert.alert(
      'Varsayılana Dön',
      'Tab bar ayarlarını varsayılana sıfırlamak istiyor musunuz?',
      [
        {
          text: 'İptal',
          style: 'cancel'
        },
        {
          text: 'Sıfırla',
          style: 'destructive',
          onPress: () => {
            setSelectedTabs([
              { id: 'home', title: 'Ana Sayfa', icon: 'home' },
              { id: 'transactions', title: 'İşlemler', icon: 'receipt-long' },
              { id: 'features', title: 'Özellikler', icon: 'apps' },
              { id: 'settings', title: 'Ayarlar', icon: 'settings' }
            ]);
            setAvailableTabs([
              { id: 'investment', title: 'Yatırımlar', icon: 'account-balance' },
              { id: 'work', title: 'Mesai', icon: 'access-time' },
              { id: 'stats', title: 'İstatistikler', icon: 'bar-chart' }
            ]);
            setHasChanges(false);
            Alert.alert('Başarılı', 'Tab bar ayarları varsayılana sıfırlandı');
          }
        }
      ]
    );
  };

  // Değişiklikleri kaydet
  const saveChanges = () => {
    setLoading(true);

    // Simüle edilmiş kaydetme işlemi
    setTimeout(() => {
      setHasChanges(false);
      setLoading(false);
      Alert.alert(
        'Başarılı',
        'Tab bar ayarlarınız kaydedildi. Değişikliklerin etkili olması için uygulamayı yeniden başlatmanız gerekebilir.'
      );
    }, 1000);
  };

  if (loading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: safeNeutral[50] }]}>
        <ActivityIndicator size="large" color={safePrimary} />
        <Text style={[styles.loadingText, { color: safeNeutral[700] }]}>Ayarlar yükleniyor...</Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: safeNeutral[50] }]}>
      <View style={styles.header}>
        <Text style={styles.title}>Tab Bar Özelleştir</Text>
        <Text style={styles.subtitle}>Ana ekranda görmek istediğiniz sekmeleri seçin ve sıralarını düzenleyin.</Text>
      </View>

      {/* Seçili Sekmelerin Listesi */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Aktif Sekmeler</Text>
        <Text style={styles.sectionSubtitle}>Sıralamayı değiştirmek için sürükleyin</Text>

        <View style={styles.selectedTabsContainer}>
          {selectedTabs.map((item) => (
            <TouchableOpacity
              key={item.id}
              style={styles.tabItem}
            >
              <MaterialIcons name="drag-handle" size={24} color={safeNeutral[400]} />
              <MaterialIcons name={item.icon} size={24} color={safePrimary} />
              <Text style={styles.tabText}>{item.title}</Text>
              <TouchableOpacity
                style={styles.removeButton}
                onPress={() => removeTab(item.id)}
              >
                <MaterialIcons name="remove-circle-outline" size={24} color="#F44336" />
              </TouchableOpacity>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Eklenebilecek Sekmeler */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Kullanılabilir Sekmeler</Text>
        <Text style={styles.sectionSubtitle}>Eklemek istediğiniz sekmeye tıklayın</Text>

        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.availableTabsContainer}>
          {availableTabs.map(tab => (
            <TouchableOpacity
              key={tab.id}
              style={styles.availableTabItem}
              onPress={() => addTab(tab)}
            >
              <MaterialIcons name={tab.icon} size={24} color={safeNeutral[600]} />
              <Text style={styles.availableTabText}>{tab.title}</Text>
              <MaterialIcons name="add-circle-outline" size={24} color="#4CAF50" />
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Dark Mode (Gelecekte aktifleştirilecek) */}
      <View style={styles.section}>
        <View style={styles.optionRow}>
          <View style={styles.optionInfo}>
            <Text style={styles.optionTitle}>Karanlık Mod</Text>
            <Text style={styles.optionDescription}>Bu özellik yakında gelecek</Text>
          </View>
          <Switch
            value={isDarkMode}
            onValueChange={setIsDarkMode}
            disabled={true}
            trackColor={{ false: safeNeutral[300], true: Colors.PRIMARY_LIGHT }}
            thumbColor={isDarkMode ? safePrimary : safeNeutral[100]}
          />
        </View>
      </View>

      {/* Alt Butonlar */}
      <View style={styles.footer}>
        <TouchableOpacity
          style={[styles.footerButton, styles.resetButton]}
          onPress={resetToDefault}
        >
          <MaterialIcons name="refresh" size={20} color="#F44336" />
          <Text style={[styles.buttonText, styles.resetButtonText]}>Varsayılana Dön</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.footerButton,
            styles.saveButton,
            !hasChanges && styles.saveButtonDisabled
          ]}
          onPress={saveChanges}
          disabled={!hasChanges}
        >
          <MaterialIcons name="save" size={20} color={safeWhite} />
          <Text style={styles.saveButtonText}>Değişiklikleri Kaydet</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: safeNeutral[50]
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: safeNeutral[50]
  },
  loadingText: {
    marginTop: 12,
    color: safeNeutral[700],
    fontSize: 16
  },
  header: {
    padding: 20,
    backgroundColor: safeWhite,
    borderBottomWidth: 1,
    borderBottomColor: safeNeutral[200]
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: safeNeutral[900],
    marginBottom: 8
  },
  subtitle: {
    fontSize: 14,
    color: safeNeutral[600]
  },
  section: {
    margin: 16,
    padding: 16,
    backgroundColor: safeWhite,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: safeNeutral[800],
    marginBottom: 4
  },
  sectionSubtitle: {
    fontSize: 14,
    color: safeNeutral[500],
    marginBottom: 16
  },
  selectedTabsContainer: {
    minHeight: 200,
    maxHeight: 300
  },
  tabItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: safeNeutral[50],
    padding: 16,
    borderRadius: 8,
    marginBottom: 8
  },
  tabItemActive: {
    backgroundColor: safeNeutral[100],
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2
  },
  tabText: {
    flex: 1,
    fontSize: 16,
    marginLeft: 12,
    color: safeNeutral[800]
  },
  removeButton: {
    padding: 4
  },
  availableTabsContainer: {
    flexDirection: 'row'
  },
  availableTabItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: safeNeutral[50],
    padding: 16,
    borderRadius: 8,
    marginRight: 12,
    minWidth: 180
  },
  availableTabText: {
    flex: 1,
    fontSize: 16,
    marginLeft: 12,
    color: safeNeutral[800]
  },
  optionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 8
  },
  optionInfo: {
    flex: 1
  },
  optionTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: safeNeutral[800]
  },
  optionDescription: {
    fontSize: 14,
    color: safeNeutral[500],
    marginTop: 4
  },
  footer: {
    flexDirection: 'row',
    padding: 16,
    backgroundColor: safeWhite,
    borderTopWidth: 1,
    borderTopColor: safeNeutral[200],
    gap: 12
  },
  footerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 14,
    borderRadius: 8,
    flex: 1
  },
  resetButton: {
    backgroundColor: safeWhite,
    borderWidth: 1,
    borderColor: '#F44336'
  },
  resetButtonText: {
    color: '#F44336'
  },
  saveButton: {
    backgroundColor: safePrimary,
    flex: 2
  },
  saveButtonDisabled: {
    backgroundColor: safeNeutral[300]
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8
  },
  saveButtonText: {
    color: safeWhite
  }
});

export default TabBarCustomizationScreen;
