import * as BackgroundFetch from 'expo-background-fetch';
import * as TaskManager from 'expo-task-manager';
import * as Notifications from 'expo-notifications';
import * as SQLite from 'expo-sqlite';
import * as workService from './workService';
import { formatDate } from '../utils/formatters';

// Zaman formatlama yardımcı fonksiyonu
const formatTime = (timeStr) => {
  if (!timeStr) return '';
  return timeStr.substring(0, 5);
};

// Arka plan görev adları
const SHIFT_CHECK_TASK = 'SHIFT_CHECK_TASK';
const SHIFT_NOTIFICATION_TASK = 'SHIFT_NOTIFICATION_TASK';

// Veritabanı adı
const DB_NAME = 'financial_app.db';

/**
 * Vardiya arka plan servisini başlatır
 *
 * @returns {Promise<void>}
 */
export const initShiftBackgroundService = async () => {
  try {
    // Bildirimleri yapılandır
    await configureNotifications();

    // Arka plan görevlerini tanımla
    defineBackgroundTasks();

    // Arka plan görevlerini kaydet
    await registerBackgroundTasks();
  } catch (error) {
    console.warn('Vardiya arka plan servisi başlatma hatası:', error);
  }
};

/**
 * Bildirimleri yapılandırır
 *
 * @returns {Promise<void>}
 */
const configureNotifications = async () => {
  // Bildirim ayarlarını yapılandır
  await Notifications.setNotificationHandler({
    handleNotification: async () => ({
      shouldShowAlert: true,
      shouldPlaySound: true,
      shouldSetBadge: true,
    }),
  });
};

/**
 * Arka plan görevlerini tanımlar
 */
const defineBackgroundTasks = () => {
  // Vardiya kontrol görevi
  if (!TaskManager.isTaskDefined(SHIFT_CHECK_TASK)) {
    TaskManager.defineTask(SHIFT_CHECK_TASK, async () => {
      try {
        await checkShifts();
        return BackgroundFetch.BackgroundFetchResult.NewData;
      } catch (error) {
        console.error('Vardiya kontrol hatası:', error);
        return BackgroundFetch.BackgroundFetchResult.Failed;
      }
    });
  }

  // Vardiya bildirim görevi
  if (!TaskManager.isTaskDefined(SHIFT_NOTIFICATION_TASK)) {
    TaskManager.defineTask(SHIFT_NOTIFICATION_TASK, async () => {
      try {
        await checkUpcomingShifts();
        return BackgroundFetch.BackgroundFetchResult.NewData;
      } catch (error) {
        console.error('Vardiya bildirim hatası:', error);
        return BackgroundFetch.BackgroundFetchResult.Failed;
      }
    });
  }
};

/**
 * Arka plan görevlerini kaydeder
 *
 * @returns {Promise<void>}
 */
const registerBackgroundTasks = async () => {
  // Vardiya kontrol görevini kaydet (15 dakikada bir çalışacak)
  await BackgroundFetch.registerTaskAsync(SHIFT_CHECK_TASK, {
    minimumInterval: 15 * 60, // 15 dakika
    stopOnTerminate: false,
    startOnBoot: true,
  });

  // Vardiya bildirim görevini kaydet (saatte bir çalışacak)
  await BackgroundFetch.registerTaskAsync(SHIFT_NOTIFICATION_TASK, {
    minimumInterval: 60 * 60, // 1 saat
    stopOnTerminate: false,
    startOnBoot: true,
  });
};

/**
 * Vardiyaları kontrol eder ve durumlarını günceller
 *
 * @returns {Promise<void>}
 */
export const checkShifts = async () => {
  // Veritabanı bağlantısını aç
  const db = SQLite.openDatabase(DB_NAME);

  try {
    // Bugünün tarihini al
    const today = new Date();
    const todayStr = formatDate(today, 'yyyy-MM-dd');

    // Şu anki saati al
    const now = new Date();

    // Planlanmış vardiyaları getir
    const plannedShifts = await workService.getWorkShiftsByStatus(db, 'planned');

    // Başlaması gereken vardiyaları kontrol et
    for (const shift of plannedShifts) {
      // Vardiya bugün mü ve başlangıç saati geçti mi?
      const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:00`;

      if (shift.date === todayStr && shift.start_time <= currentTime) {
        // Vardiyayı aktif yap
        await workService.updateWorkShiftStatus(db, shift.id, 'active');

        // Bildirim gönder
        await sendNotification(
          'Vardiya Başladı',
          `${formatTime(shift.start_time)} - ${formatTime(shift.end_time)} vardiyası başladı.`
        );
      }
    }

    // Aktif vardiyaları getir
    const activeShifts = await workService.getWorkShiftsByStatus(db, 'active');

    // Bitmesi gereken vardiyaları kontrol et
    for (const shift of activeShifts) {
      // Vardiya bugün mü ve bitiş saati geçti mi?
      const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:00`;

      if (shift.date === todayStr && shift.end_time <= currentTime) {
        // Vardiyayı tamamlandı yap
        await workService.updateWorkShiftStatus(db, shift.id, 'completed');

        // Bildirim gönder
        await sendNotification(
          'Vardiya Tamamlandı',
          `${formatTime(shift.start_time)} - ${formatTime(shift.end_time)} vardiyası tamamlandı.`
        );
      }
    }
  } catch (error) {
    console.warn('Vardiya kontrol hatası:', error);
  } finally {
    // Veritabanı bağlantısını kapat
    if (db) {
      db.closeAsync();
    }
  }
};

/**
 * Yaklaşan vardiyaları kontrol eder ve bildirim gönderir
 *
 * @returns {Promise<void>}
 */
export const checkUpcomingShifts = async () => {
  // Veritabanı bağlantısını aç
  const db = SQLite.openDatabase(DB_NAME);

  try {
    // Bugünün tarihini al
    const today = new Date();
    const todayStr = formatDate(today, 'yyyy-MM-dd');

    // Yarının tarihini al
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const tomorrowStr = formatDate(tomorrow, 'yyyy-MM-dd');

    // Şu anki saati al
    const now = new Date();

    // Planlanmış vardiyaları getir
    const plannedShifts = await workService.getWorkShiftsByStatus(db, 'planned');

    // Yaklaşan vardiyaları kontrol et
    for (const shift of plannedShifts) {
      // Vardiya bugün mü?
      if (shift.date === todayStr) {
        // Vardiya 1 saat içinde mi başlayacak?
        const shiftHour = parseInt(shift.start_time.split(':')[0]);
        const shiftMinute = parseInt(shift.start_time.split(':')[1]);
        const shiftTime = new Date(today);
        shiftTime.setHours(shiftHour, shiftMinute, 0, 0);

        const timeDiff = (shiftTime - now) / (1000 * 60); // Dakika cinsinden fark

        if (timeDiff > 0 && timeDiff <= 60) {
          // Bildirim gönder
          await sendNotification(
            'Yaklaşan Vardiya',
            `${formatTime(shift.start_time)} - ${formatTime(shift.end_time)} vardiyası 1 saat içinde başlayacak.`
          );
        }
      }
      // Vardiya yarın mı?
      else if (shift.date === tomorrowStr) {
        // Yarınki vardiya için bildirim gönder
        await sendNotification(
          'Yarınki Vardiya Hatırlatması',
          `Yarın ${formatTime(shift.start_time)} - ${formatTime(shift.end_time)} vardiyası var.`
        );
      }
    }
  } catch (error) {
    console.warn('Yaklaşan vardiya kontrol hatası:', error);
  } finally {
    // Veritabanı bağlantısını kapat
    if (db) {
      db.closeAsync();
    }
  }
};

/**
 * Bildirim gönderir
 *
 * @param {string} title - Bildirim başlığı
 * @param {string} body - Bildirim içeriği
 * @returns {Promise<string>} Bildirim ID'si
 */
export const sendNotification = async (title, body) => {
  try {
    return await Notifications.scheduleNotificationAsync({
      content: {
        title,
        body,
        sound: true,
        priority: Notifications.AndroidNotificationPriority.HIGH,
      },
      trigger: null, // Hemen gönder
    });
  } catch (error) {
    console.warn('Bildirim gönderme hatası:', error);
    throw error;
  }
};
