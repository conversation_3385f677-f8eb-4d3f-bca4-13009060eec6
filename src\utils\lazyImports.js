import { lazy } from 'react';

/**
 * Lazy Import Utilities
 * Bundle size optimizasyonu için lazy loading
 */

// Screens - Lazy loaded
export const LazyHomeScreen = lazy(() => import('../screens/HomeScreen'));
export const LazyReportsScreen = lazy(() => import('../screens/ReportsScreen'));
export const LazyBudgetsScreen = lazy(() => import('../screens/BudgetsScreen'));
export const LazyInvestmentScreen = lazy(() => import('../screens/InvestmentScreen'));
export const LazyRemindersScreen = lazy(() => import('../screens/RemindersScreen'));
export const LazyOvertimeScreen = lazy(() => import('../screens/OvertimeScreen'));
export const LazySettingsScreen = lazy(() => import('../screens/SettingsScreen'));

// Forms - Lazy loaded
export const LazyTransactionForm = lazy(() => import('../components/TransactionForm'));
export const LazyBudgetForm = lazy(() => import('../components/BudgetForm'));
export const LazyInvestmentForm = lazy(() => import('../components/InvestmentForm'));
export const LazyReminderForm = lazy(() => import('../components/ReminderForm'));

// Charts - Lazy loaded (heavy components)
export const LazyPieChart = lazy(() => import('../components/charts/PieChart'));
export const LazyLineChart = lazy(() => import('../components/charts/LineChart'));
export const LazyBarChart = lazy(() => import('../components/charts/BarChart'));

// Reports - Lazy loaded
export const LazyAdvancedReports = lazy(() => import('../components/reports/AdvancedReports'));
export const LazyExportReports = lazy(() => import('../components/reports/ExportReports'));

// Utilities - Lazy loaded
export const LazyPDFGenerator = lazy(() => import('../services/pdfGenerator'));
export const LazyBackupService = lazy(() => import('../services/backupService'));

/**
 * Dynamic import helper
 * @param {string} modulePath - Module path
 * @returns {Promise} Dynamic import promise
 */
export const dynamicImport = (modulePath) => {
  return import(modulePath);
};

/**
 * Conditional import helper
 * @param {boolean} condition - Import condition
 * @param {Function} importFn - Import function
 * @returns {Promise|null} Import promise or null
 */
export const conditionalImport = (condition, importFn) => {
  if (condition) {
    return importFn();
  }
  return Promise.resolve(null);
};

/**
 * Preload helper
 * @param {Array} importFunctions - Array of import functions
 * @returns {Promise} Preload promise
 */
export const preloadModules = (importFunctions) => {
  return Promise.all(
    importFunctions.map(importFn => 
      importFn().catch(error => {
        console.warn('Module preload failed:', error);
        return null;
      })
    )
  );
};

/**
 * Feature-based lazy loading
 */
export const FeatureModules = {
  // Advanced features
  advanced: {
    reports: () => import('../components/reports/AdvancedReports'),
    analytics: () => import('../components/analytics/AdvancedAnalytics'),
    charts: () => import('../components/charts/AdvancedCharts'),
  },
  
  // Export features
  export: {
    pdf: () => import('../services/pdfGenerator'),
    excel: () => import('../services/excelGenerator'),
    backup: () => import('../services/backupService'),
  },
  
  // Investment features
  investment: {
    portfolio: () => import('../components/investment/Portfolio'),
    analysis: () => import('../components/investment/Analysis'),
    tracking: () => import('../components/investment/Tracking'),
  },
  
  // Notification features
  notifications: {
    scheduler: () => import('../services/notificationScheduler'),
    background: () => import('../services/backgroundNotifications'),
    push: () => import('../services/pushNotifications'),
  },
};

/**
 * Load feature module
 * @param {string} feature - Feature name
 * @param {string} module - Module name
 * @returns {Promise} Module promise
 */
export const loadFeatureModule = async (feature, module) => {
  try {
    if (FeatureModules[feature] && FeatureModules[feature][module]) {
      return await FeatureModules[feature][module]();
    }
    throw new Error(`Feature module not found: ${feature}.${module}`);
  } catch (error) {
    console.error(`Failed to load feature module ${feature}.${module}:`, error);
    throw error;
  }
};

/**
 * Check if feature is available
 * @param {string} feature - Feature name
 * @param {string} module - Module name
 * @returns {boolean} Is available
 */
export const isFeatureAvailable = (feature, module) => {
  return !!(FeatureModules[feature] && FeatureModules[feature][module]);
};

export default {
  LazyHomeScreen,
  LazyReportsScreen,
  LazyBudgetsScreen,
  LazyInvestmentScreen,
  LazyRemindersScreen,
  LazyOvertimeScreen,
  LazySettingsScreen,
  LazyTransactionForm,
  LazyBudgetForm,
  LazyInvestmentForm,
  LazyReminderForm,
  LazyPieChart,
  LazyLineChart,
  LazyBarChart,
  LazyAdvancedReports,
  LazyExportReports,
  LazyPDFGenerator,
  LazyBackupService,
  dynamicImport,
  conditionalImport,
  preloadModules,
  FeatureModules,
  loadFeatureModule,
  isFeatureAvailable,
};
